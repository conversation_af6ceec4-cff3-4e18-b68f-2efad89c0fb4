<template>
  <div class="edit-container">
    <div class="edit-form">
      <h2>编辑商品</h2>
      
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="top"
      >
        <el-form-item label="商品图片" prop="images">
          <el-upload
            v-model:file-list="fileList"
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :limit="9"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :on-change="handleImageChange"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
          <div class="el-upload__tip">
            最多上传9张图片，支持 jpg、png、gif 格式，单张图片不超过5MB
          </div>
        </el-form-item>

        <el-form-item label="商品标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入商品标题"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="商品分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择商品分类" style="width: 100%">
            <el-option
              v-for="category in categories"
              :key="category.code"
              :label="category.name"
              :value="category.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="商品价格" prop="price">
          <el-input-number
            v-model="form.price"
            :min="0"
            :max="99999"
            :precision="2"
            placeholder="请输入价格"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="商品成色" prop="condition">
          <el-radio-group v-model="form.condition">
            <el-radio value="new">全新</el-radio>
            <el-radio value="like_new">几乎全新</el-radio>
            <el-radio value="good">成色较好</el-radio>
            <el-radio value="fair">成色一般</el-radio>
            <el-radio value="poor">成色较差</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="所在校区" prop="campus">
          <el-select v-model="form.campus" placeholder="请选择校区" style="width: 100%">
            <el-option label="主校区" value="main" />
            <el-option label="东校区" value="east" />
            <el-option label="西校区" value="west" />
            <el-option label="南校区" value="south" />
          </el-select>
        </el-form-item>

        <el-form-item label="商品描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="6"
            placeholder="请详细描述商品的情况、购买时间、使用情况等"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="联系方式" prop="contactInfo">
          <el-input
            v-model="form.contactInfo"
            placeholder="请输入联系方式（微信号、QQ号等）"
            maxlength="50"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            保存修改
          </el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" title="图片预览">
      <img :src="previewUrl" alt="预览图片" style="width: 100%" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { PRODUCT_CATEGORIES } from '@/constants'
import { getProductDetailApi, updateProductApi } from '@/request/productApi'

const route = useRoute()
const router = useRouter()

// 表单数据
const form = reactive({
  title: '',
  category: '',
  price: 0,
  condition: 'good',
  campus: 'main',
  description: '',
  contactInfo: '',
  images: [] as string[]
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入商品标题', trigger: 'blur' },
    { min: 5, max: 50, message: '标题长度在 5 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择商品分类', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入商品价格', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '价格必须大于0', trigger: 'blur' }
  ],
  condition: [
    { required: true, message: '请选择商品成色', trigger: 'change' }
  ],
  campus: [
    { required: true, message: '请选择所在校区', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入商品描述', trigger: 'blur' },
    { min: 10, max: 1000, message: '描述长度在 10 到 1000 个字符', trigger: 'blur' }
  ],
  contactInfo: [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ]
}

// 分类选项
const categories = Object.entries(PRODUCT_CATEGORIES).map(([key, value]) => ({
  code: value,
  name: getCategoryName(value)
}))

function getCategoryName(code: string): string {
  const nameMap: Record<string, string> = {
    textbook: '教材书籍',
    digital: '数码电子',
    clothing: '服装配饰',
    daily: '生活用品',
    sports: '运动器材',
    beauty: '美妆护肤',
    food: '食品零食',
    other: '其他'
  }
  return nameMap[code] || '其他'
}

// 其他状态
const formRef = ref()
const loading = ref(false)
const fileList = ref([])
const previewVisible = ref(false)
const previewUrl = ref('')

// 图片处理
const handlePreview = (file: any) => {
  previewUrl.value = file.url
  previewVisible.value = true
}

const handleRemove = (file: any) => {
  const index = form.images.findIndex(img => img === file.url)
  if (index > -1) {
    form.images.splice(index, 1)
  }
}

const handleImageChange = (file: any, fileList: any[]) => {
  // 这里应该上传图片到服务器，现在只是模拟
  if (file.status === 'ready') {
    const reader = new FileReader()
    reader.onload = (e) => {
      const url = e.target?.result as string
      form.images.push(url)
    }
    reader.readAsDataURL(file.raw)
  }
}

// 获取商品详情
const fetchProductDetail = async () => {
  const productId = Number(route.params.id)

  try {
    const response = await getProductDetailApi(productId)

    if (response.code === 200) {
      const product = response.data

      // 填充表单
      Object.assign(form, {
        productId: product.productId,
        title: product.title,
        category: product.category,
        price: product.price,
        originalPrice: product.originalPrice,
        condition: product.condition,
        campus: product.campus,
        location: product.location,
        description: product.description,
        tags: product.tags || [],
        images: product.images || []
      })

      // 设置图片列表
      fileList.value = product.images.map((url, index) => ({
        uid: index,
        name: `image-${index}.jpg`,
        status: 'done',
        url
      }))
    } else {
      ElMessage.error(response.msg || '获取商品详情失败')
      router.push('/my-products')
    }
  } catch (error) {
    ElMessage.error('获取商品详情失败')
    router.push('/my-products')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 准备提交数据
    const submitData = {
      title: form.title,
      description: form.description,
      category: form.category,
      price: form.price,
      originalPrice: form.originalPrice,
      condition: form.condition,
      campus: form.campus,
      location: form.location,
      tags: form.tags,
      images: form.images
    }

    // 调用更新API
    const response = await updateProductApi(form.productId, submitData)

    if (response.code === 200) {
      ElMessage.success('商品修改成功')
      router.push({ name: 'MyProducts' })
    } else {
      ElMessage.error(response.msg || '商品修改失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('商品修改失败')
  } finally {
    loading.value = false
  }
}

// 取消编辑
const handleCancel = async () => {
  try {
    await ElMessageBox.confirm('确定要取消编辑吗？未保存的内容将丢失', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '继续编辑',
      type: 'warning'
    })
    router.back()
  } catch {
    // 用户取消
  }
}

onMounted(() => {
  fetchProductDetail()
})
</script>

<style lang="stylus" scoped>
.edit-container
  width 100%
  padding 20px

.edit-form
  background white
  padding 30px
  border-radius 8px
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
  max-width 800px
  margin 0 auto
  
  h2
    margin-bottom 30px
    color #333
    text-align center

.el-upload__tip
  color #999
  font-size 12px
  margin-top 5px

@media (max-width: 768px)
  .edit-container
    padding 10px
    
  .edit-form
    padding 20px
</style>
