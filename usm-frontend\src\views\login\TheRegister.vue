<template>
  <div class="register">
    <!-- 左侧宣传区域 -->
    <div class="register-banner">
      <div class="banner-content">
        <div class="logo-section">
          <div class="logo-icon">
            <el-icon size="48"><UserFilled /></el-icon>
          </div>
          <h1 class="platform-title">加入我们</h1>
          <p class="platform-slogan">开启您的校园交易之旅</p>
        </div>

        <div class="benefits">
          <div class="benefit-item">
            <el-icon class="benefit-icon"><CircleCheckFilled /></el-icon>
            <div class="benefit-content">
              <h4>安全交易</h4>
              <p>实名认证，交易更安全</p>
            </div>
          </div>
          <div class="benefit-item">
            <el-icon class="benefit-icon"><TrophyBase /></el-icon>
            <div class="benefit-content">
              <h4>信誉保障</h4>
              <p>评价体系，诚信交易</p>
            </div>
          </div>
          <div class="benefit-item">
            <el-icon class="benefit-icon"><ChatLineRound /></el-icon>
            <div class="benefit-content">
              <h4>便捷沟通</h4>
              <p>即时聊天，高效对接</p>
            </div>
          </div>
          <div class="benefit-item">
            <el-icon class="benefit-icon"><Coin /></el-icon>
            <div class="benefit-content">
              <h4>变废为宝</h4>
              <p>闲置物品，创造价值</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧注册区域 -->
    <div class="register-form-section">
      <div class="register-container">
        <div class="form-header">
          <h2 class="form-title">创建账号</h2>
          <p class="form-subtitle">填写信息，快速注册成为平台用户</p>
        </div>

        <el-form
          ref="formRef"
          :model="user"
          :rules="rules"
          class="register-form"
          label-position="top"
          @submit.prevent="registerClick"
        >
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="用户名" prop="username">
                <el-input
                  v-model.trim="user.username"
                  placeholder="请输入用户名"
                  size="large"
                  class="form-input"
                >
                  <template #prefix>
                    <el-icon><User /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="昵称" prop="nickname">
                <el-input
                  v-model.trim="user.nickname"
                  placeholder="请输入昵称"
                  size="large"
                  class="form-input"
                >
                  <template #prefix>
                    <el-icon><Avatar /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="密码" prop="password">
            <el-input
              v-model.trim="user.password"
              type="password"
              placeholder="请输入密码"
              size="large"
              class="form-input"
              show-password
            >
              <template #prefix>
                <el-icon><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="手机号" prop="phone">
                <el-input
                  v-model.trim="user.phone"
                  placeholder="请输入手机号"
                  size="large"
                  class="form-input"
                >
                  <template #prefix>
                    <el-icon><Phone /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="性别" prop="sex">
                <div class="gender-selection">
                  <el-button
                    :type="user.sex === '0' ? 'primary' : 'default'"
                    @click="user.sex = '0'"
                    class="gender-btn"
                    size="large"
                  >
                    <el-icon><Male /></el-icon>
                    <span>男</span>
                  </el-button>
                  <el-button
                    :type="user.sex === '1' ? 'primary' : 'default'"
                    @click="user.sex = '1'"
                    class="gender-btn"
                    size="large"
                  >
                    <el-icon><Female /></el-icon>
                    <span>女</span>
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model.trim="user.email"
              placeholder="请输入邮箱地址"
              size="large"
              class="form-input"
            >
              <template #prefix>
                <el-icon><Message /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="校区" prop="campus">
            <el-select
              v-model="user.campus"
              placeholder="请选择校区"
              size="large"
              class="form-input"
              style="width: 100%"
            >
              <el-option
                v-for="(label, value) in CAMPUS_LABELS"
                :key="value"
                :label="label"
                :value="value"
              />
            </el-select>
          </el-form-item>

          <div class="agreement-section">
            <el-checkbox v-model="agreeTerms" size="large">
              我已阅读并同意
              <el-link type="primary" class="agreement-link">《用户协议》</el-link>
              和
              <el-link type="primary" class="agreement-link">《隐私政策》</el-link>
            </el-checkbox>
          </div>

          <el-button
            class="register-btn"
            type="primary"
            size="large"
            :loading="loading"
            @click="registerClick"
            :disabled="!agreeTerms"
            block
          >
            <span v-if="!loading">立即注册</span>
            <span v-else>注册中...</span>
          </el-button>

          <div class="login-section">
            <span class="login-text">已有账号？</span>
            <el-link type="primary" @click="goToLogin" class="login-link">
              立即登录
            </el-link>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {ref, reactive, onMounted} from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { registerApi } from '@/request/loginApi'
import { CAMPUS_LABELS } from '@/constants'
import {
  Male,
  Female,
  User,
  Lock,
  Phone,
  Message,
  Avatar,
  UserFilled,
  CircleCheckFilled,
  TrophyBase,
  ChatLineRound,
  Coin
} from '@element-plus/icons-vue'

const router = useRouter()
const loading = ref(false)
const agreeTerms = ref(false)
const formRef = ref()

const user = reactive({
  username: '',
  nickname: '',
  password: '',
  email: '',
  phone: '',
  sex: '',
  campus: '',
  roleKey: 'student' // 默认为学生角色
})
// 移除了角色API调用，因为现在默认注册为学生用户
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
    { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: ['blur', 'change'] }
  ],
  sex: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  campus: [
    { required: true, message: '请选择校区', trigger: 'change' }
  ]
}

const registerClick = async () => {
  if (!formRef.value) return

  if (!agreeTerms.value) {
    ElMessage.warning('请先同意用户协议和隐私政策')
    return
  }

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true
      try {
        const res = await registerApi(user as any)
        if (res.code === 200) {
          ElMessage.success('注册成功')
          await router.push('/login')
        } else {
          ElMessage.error(res.msg || '注册失败')
        }
      } catch (error) {
        ElMessage.error('注册失败，请稍后重试')
      } finally {
        loading.value = false
      }
    } else {
      ElMessage.error('请检查表单信息')
      return false
    }
  })
}

const goToLogin = () => {
  router.push('/login')
}
</script>


<style lang="stylus" scoped>
.register {
  display: flex
  min-height: 100vh
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%)
}

// 左侧宣传区域
.register-banner {
  flex: 1
  display: flex
  align-items: center
  justify-content: center
  padding: 60px 40px
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%)
  position: relative
  overflow: hidden

  &::before {
    content: ''
    position: absolute
    top: 0
    left: 0
    right: 0
    bottom: 0
    background: url("../../assets/background.jpg") center/cover
    opacity: 0.1
    z-index: 0
  }
}

.banner-content {
  position: relative
  z-index: 1
  text-align: center
  color: white
  max-width: 500px
}

.logo-section {
  margin-bottom: 60px

  .logo-icon {
    margin-bottom: 20px

    .el-icon {
      background: rgba(255, 255, 255, 0.2)
      border-radius: 50%
      padding: 20px
      backdrop-filter: blur(10px)
    }
  }

  .platform-title {
    font-size: 36px
    font-weight: 700
    margin: 0 0 16px
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3)
  }

  .platform-slogan {
    font-size: 18px
    opacity: 0.9
    margin: 0
    font-weight: 300
  }
}

.benefits {
  .benefit-item {
    display: flex
    align-items: center
    text-align: left
    margin-bottom: 24px

    .benefit-icon {
      font-size: 24px
      margin-right: 16px
      flex-shrink: 0
    }

    .benefit-content {
      h4 {
        font-size: 16px
        font-weight: 600
        margin: 0 0 4px
      }

      p {
        font-size: 14px
        opacity: 0.8
        margin: 0
      }
    }
  }
}

// 右侧注册区域
.register-form-section {
  flex: 1.2
  display: flex
  align-items: center
  justify-content: center
  padding: 40px
  background: #f8fafc
  min-width: 600px
  overflow-y: auto
}

.register-container {
  width: 100%
  max-width: 520px
  background: white
  border-radius: 16px
  padding: 40px
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1)
  transition: all 0.3s ease

  &:hover {
    transform: translateY(-2px)
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15)
  }
}

.form-header {
  text-align: center
  margin-bottom: 32px

  .form-title {
    font-size: 28px
    font-weight: 700
    color: #1a202c
    margin: 0 0 8px
  }

  .form-subtitle {
    font-size: 16px
    color: #718096
    margin: 0
    line-height: 1.5
  }
}

.register-form {
  .form-input {
    margin-bottom: 20px

    :deep(.el-input__wrapper) {
      border-radius: 8px
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1)
      transition: all 0.3s ease

      &:hover {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15)
      }

      &.is-focus {
        box-shadow: 0 0 0 3px rgba(118, 75, 162, 0.1)
      }
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 600
    color: #374151
    margin-bottom: 8px
  }
}

.gender-selection {
  display: flex
  gap: 12px
  width: 100%

  .gender-btn {
    flex: 1
    display: flex
    align-items: center
    justify-content: center
    gap: 8px
    border-radius: 8px
    transition: all 0.3s ease

    &:hover {
      transform: translateY(-1px)
    }
  }
}

.agreement-section {
  margin-bottom: 24px
  text-align: center

  .agreement-link {
    text-decoration: none

    &:hover {
      text-decoration: underline
    }
  }
}

.register-btn {
  height: 48px
  font-size: 16px
  font-weight: 600
  border-radius: 8px
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%)
  border: none
  transition: all 0.3s ease

  &:hover:not(:disabled) {
    transform: translateY(-1px)
    box-shadow: 0 8px 20px rgba(118, 75, 162, 0.4)
  }

  &:active {
    transform: translateY(0)
  }

  &:disabled {
    opacity: 0.6
    cursor: not-allowed
  }
}

.login-section {
  text-align: center
  margin-top: 24px

  .login-text {
    color: #718096
    font-size: 14px
    margin-right: 8px
  }

  .login-link {
    font-size: 14px
    font-weight: 600
    text-decoration: none

    &:hover {
      text-decoration: underline
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .register-form-section {
    min-width: 500px
  }

  .register-container {
    max-width: 480px
    padding: 30px
  }
}

@media (max-width: 1024px) {
  .register {
    flex-direction: column
  }

  .register-banner {
    flex: none
    min-height: 35vh
    padding: 40px 20px

    .banner-content {
      max-width: 400px
    }

    .logo-section {
      margin-bottom: 40px

      .platform-title {
        font-size: 28px
      }

      .platform-slogan {
        font-size: 16px
      }
    }

    .benefits {
      .benefit-item {
        margin-bottom: 20px

        .benefit-content {
          h4 {
            font-size: 14px
          }

          p {
            font-size: 12px
          }
        }
      }
    }
  }

  .register-form-section {
    min-width: auto
    padding: 20px
  }

  .register-container {
    padding: 30px 20px
  }
}

@media (max-width: 768px) {
  .register-banner {
    min-height: 25vh
    padding: 30px 15px

    .logo-section {
      margin-bottom: 30px

      .platform-title {
        font-size: 24px
      }
    }

    .benefits {
      display: none
    }
  }

  .form-header {
    .form-title {
      font-size: 24px
    }

    .form-subtitle {
      font-size: 14px
    }
  }

  .gender-selection {
    .gender-btn {
      font-size: 14px
    }
  }
}

.gender-btn {
  width: 40px
  height: 40px
  border-radius: 50%
  padding: 0
  display: flex
  align-items: center
  justify-content: center
  transition: all 0.3s ease
  
  :deep(.el-icon) {
    font-size: 18px
  }
  
  &:hover {
    transform: scale(1.05)
  }

  &:first-child {
    &:not(.el-button--primary) {
      color: #409EFF
      border-color: #409EFF
    }
  }

  &:last-child {
    &:not(.el-button--primary) {
      color: #FF69B4
      border-color: #FF69B4
      background-color: rgba(255, 105, 180, 0.1)
    }
    &.el-button--primary {
      background-color: #FF69B4
      border-color: #FF69B4
      &:hover {
        background-color: #FF85C2
        border-color: #FF85C2
      }
    }
  }
}
</style>