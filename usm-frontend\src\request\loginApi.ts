import request from "./request";

type reportData<T = {}> = {
    code: number;
    msg: string;
    data: T;
}
type LoginRequest = {
    username: string;
    password: string;
    code: string;
    uuid: string;
}
type LoginReport = { token: string; }
type RegisterRequest = {
    username: string;
    nickname: string;
    password: string;
    phone: string;
    email: string;
    sex: string;
    roleKey: string;
    campus: string;
}
type UserReport = {
    userId: number;
    username: string;
    avatar: string;
    email: string;
    phone: string;
    sex: string;
    status: string;
}
type RoleReport = {
    roleId: number;
    roleName: string;
    roleKey: string;
}
type captchaReport = {
    uuid: string;
    img: string;
}
export const captchaApi = (): Promise<reportData<captchaReport>> => {
    return request.get('/captcha')
}
export const loginApi = (data:LoginRequest): Promise<reportData<LoginReport>> => {
    return request.post('/login',data)
}
export const getRoleApi = (): Promise<reportData<RoleReport>> => {
    return request.get('/roleList')
}
export const registerApi = (data:RegisterRequest): Promise<reportData> => {
    return request.post('/register',data)
}
export const logoutApi = (): Promise<reportData> => {
    return request.post('/logout')
}
export const userInfoApi = (): Promise<reportData<UserReport>> => {
    return request.get('/user/getInfo')
}