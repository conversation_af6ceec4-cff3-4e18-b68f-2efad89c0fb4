<template>
  <div class="order-detail-container">
    <div class="page-header">
      <el-button @click="goBack" type="text">
        <el-icon><ArrowLeft /></el-icon>
        返回订单列表
      </el-button>
      <h2>订单详情</h2>
    </div>

    <div class="order-content" v-loading="loading">
      <div v-if="order">
        <!-- 订单状态 -->
        <el-card class="status-card">
          <div class="order-status">
            <el-icon class="status-icon" :class="getStatusIconClass(order.status)">
              <component :is="getStatusIcon(order.status)" />
            </el-icon>
            <div class="status-info">
              <h3>{{ getStatusLabel(order.status) }}</h3>
              <p class="status-desc">{{ getStatusDescription(order.status) }}</p>
            </div>
          </div>
        </el-card>

        <!-- 订单信息 -->
        <el-card class="order-info-card">
          <template #header>
            <span>订单信息</span>
          </template>
          
          <div class="order-basic-info">
            <div class="info-row">
              <span class="label">订单号：</span>
              <span class="value">{{ order.orderId }}</span>
            </div>
            <div class="info-row">
              <span class="label">下单时间：</span>
              <span class="value">{{ formatTime(order.createTime) }}</span>
            </div>
            <div class="info-row">
              <span class="label">交易方式：</span>
              <span class="value">{{ order.tradeType === 'pickup' ? '线下面交' : '校内配送' }}</span>
            </div>
            <div class="info-row" v-if="order.location">
              <span class="label">交易地点：</span>
              <span class="value">{{ order.location }}</span>
            </div>
            <div class="info-row" v-if="order.address">
              <span class="label">配送地址：</span>
              <span class="value">{{ order.address }}</span>
            </div>
            <div class="info-row">
              <span class="label">联系电话：</span>
              <span class="value">{{ order.phone }}</span>
            </div>
            <div class="info-row" v-if="order.remark">
              <span class="label">备注信息：</span>
              <span class="value">{{ order.remark }}</span>
            </div>
          </div>
        </el-card>

        <!-- 商品信息 -->
        <el-card class="product-card">
          <template #header>
            <span>商品信息</span>
          </template>
          
          <div class="product-info">
            <el-image
              :src="order.productInfo?.images?.[0]"
              fit="cover"
              class="product-image"
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
            
            <div class="product-details">
              <h3>{{ order.productInfo?.title }}</h3>
              <p class="product-description">{{ order.productInfo?.description }}</p>
              <div class="product-price">
                <span class="current-price">¥{{ order.productInfo?.price }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 卖家信息 -->
        <el-card class="seller-card">
          <template #header>
            <span>卖家信息</span>
          </template>
          
          <div class="seller-info">
            <el-avatar :src="order.sellerInfo?.avatar" :size="50">
              {{ order.sellerInfo?.nickname?.charAt(0) }}
            </el-avatar>
            <div class="seller-details">
              <h4>{{ order.sellerInfo?.nickname }}</h4>
              <div class="seller-stats">
                <el-tag size="small" type="success">信誉良好</el-tag>
                <span>成交 15 笔</span>
                <span>好评率 98%</span>
              </div>
            </div>
            <div class="seller-actions">
              <el-button size="small" @click="handleContactSeller">
                联系卖家
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 费用明细 -->
        <el-card class="cost-card">
          <template #header>
            <span>费用明细</span>
          </template>
          
          <div class="cost-details">
            <div class="cost-item">
              <span>商品价格：</span>
              <span>¥{{ order.productInfo?.price || 0 }}</span>
            </div>
            <div class="cost-item" v-if="order.tradeType === 'delivery'">
              <span>配送费用：</span>
              <span>¥5</span>
            </div>
            <div class="cost-item total">
              <span>订单总计：</span>
              <span class="total-price">¥{{ order.totalAmount }}</span>
            </div>
          </div>
        </el-card>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <template v-if="order.status === 'pending_payment'">
            <el-button type="primary" size="large" @click="handlePay">
              立即付款
            </el-button>
            <el-button size="large" @click="handleCancel">
              取消订单
            </el-button>
          </template>
          
          <template v-if="order.status === 'pending_pickup'">
            <el-button type="primary" size="large" @click="handleConfirmPickup">
              确认取货
            </el-button>
          </template>
          
          <template v-if="order.status === 'pending_confirm'">
            <el-button type="primary" size="large" @click="handleConfirmOrder">
              确认收货
            </el-button>
          </template>
          
          <template v-if="order.status === 'completed'">
            <el-button type="primary" size="large" @click="handleComment">
              评价订单
            </el-button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  ArrowLeft, 
  Picture, 
  Clock, 
  Truck, 
  Check, 
  Close 
} from '@element-plus/icons-vue'
import { formatTime } from '@/utils'
import type { Order } from '@/types'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const order = ref<Order | null>(null)

// 获取订单详情
const fetchOrderDetail = async () => {
  const orderId = route.params.id
  if (!orderId) {
    ElMessage.error('订单ID不存在')
    goBack()
    return
  }

  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟订单数据
    order.value = {
      orderId: `ORD${orderId}`,
      productId: 1,
      productInfo: {
        productId: 1,
        title: '高等数学教材（第七版）',
        description: '同济大学出版社，九成新，无笔记，适合大一学生使用。',
        price: 25,
        images: ['/src/assets/background.jpg']
      },
      sellerId: 2,
      sellerInfo: {
        userId: 2,
        nickname: '小红',
        avatar: ''
      },
      buyerId: 1,
      totalAmount: 30,
      status: 'pending_payment',
      tradeType: 'delivery',
      address: '北校区宿舍楼A栋201',
      phone: '138****5678',
      remark: '请在下午2点后配送',
      createTime: '2024-01-20T10:30:00',
      updateTime: '2024-01-20T10:30:00'
    }
  } catch (error) {
    ElMessage.error('获取订单详情失败')
    goBack()
  } finally {
    loading.value = false
  }
}

// 状态相关方法
const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending_payment: '待付款',
    pending_pickup: '待取货',
    pending_confirm: '待确认',
    completed: '已完成',
    cancelled: '已取消'
  }
  return labels[status] || status
}

const getStatusDescription = (status: string) => {
  const descriptions: Record<string, string> = {
    pending_payment: '请在24小时内完成付款',
    pending_pickup: '卖家已确认，请按约定时间取货',
    pending_confirm: '请确认已收到商品',
    completed: '交易已完成',
    cancelled: '订单已取消'
  }
  return descriptions[status] || ''
}

const getStatusIcon = (status: string) => {
  const icons: Record<string, any> = {
    pending_payment: Clock,
    pending_pickup: Truck,
    pending_confirm: Check,
    completed: Check,
    cancelled: Close
  }
  return icons[status] || Clock
}

const getStatusIconClass = (status: string) => {
  const classes: Record<string, string> = {
    pending_payment: 'warning',
    pending_pickup: 'info',
    pending_confirm: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return classes[status] || 'info'
}

// 操作方法
const handlePay = () => {
  router.push({ name: 'OrderPayment', params: { id: order.value?.orderId } })
}

const handleCancel = async () => {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('订单已取消')
    fetchOrderDetail()
  } catch {
    // 用户取消
  }
}

const handleConfirmPickup = () => {
  ElMessage.success('已确认取货')
  fetchOrderDetail()
}

const handleConfirmOrder = () => {
  ElMessage.success('已确认收货')
  fetchOrderDetail()
}

const handleComment = () => {
  ElMessage.info('评价功能开发中...')
}

const handleContactSeller = () => {
  ElMessage.info('联系卖家功能开发中...')
}

const goBack = () => {
  router.push({ name: 'OrderList' })
}

onMounted(() => {
  fetchOrderDetail()
})
</script>

<style lang="stylus" scoped>
.order-detail-container
  width 100%
  max-width 800px
  margin 0 auto
  padding 20px

.page-header
  display flex
  align-items center
  gap 15px
  margin-bottom 20px
  
  h2
    margin 0
    color #333

.order-content
  .el-card
    margin-bottom 20px

.status-card
  .order-status
    display flex
    align-items center
    gap 15px
    
    .status-icon
      font-size 40px
      
      &.warning
        color #e6a23c
      &.info
        color #409eff
      &.primary
        color #409eff
      &.success
        color #67c23a
      &.danger
        color #f56c6c
    
    .status-info
      h3
        margin 0 0 5px 0
        color #333
        
      .status-desc
        margin 0
        color #666
        font-size 14px

.order-basic-info
  .info-row
    display flex
    margin-bottom 10px
    
    .label
      width 100px
      color #666
      
    .value
      flex 1
      color #333

.product-info
  display flex
  gap 15px
  
  .product-image
    width 120px
    height 120px
    border-radius 8px
    flex-shrink 0
    
  .product-details
    flex 1
    
    h3
      margin 0 0 8px 0
      color #333
      
    .product-description
      margin 0 0 10px 0
      color #666
      font-size 14px
      
    .current-price
      font-size 18px
      color #e74c3c
      font-weight bold

.seller-info
  display flex
  align-items center
  gap 15px
  
  .seller-details
    flex 1
    
    h4
      margin 0 0 5px 0
      color #333
      
    .seller-stats
      display flex
      align-items center
      gap 10px
      font-size 14px
      color #666

.cost-details
  .cost-item
    display flex
    justify-content space-between
    margin-bottom 10px
    
    &.total
      border-top 1px solid #eee
      padding-top 10px
      font-size 16px
      font-weight bold
      
    .total-price
      color #e74c3c
      font-size 18px

.action-buttons
  display flex
  gap 15px
  justify-content center
  margin-top 30px

.image-slot
  display flex
  align-items center
  justify-content center
  width 100%
  height 100%
  background #f5f7fa
  color #909399

@media (max-width: 768px)
  .order-detail-container
    padding 10px
    
  .product-info
    flex-direction column
    
  .product-image
    width 100%
    height 200px
</style>
