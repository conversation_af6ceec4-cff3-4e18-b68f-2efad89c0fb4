"""
初始化商品分类数据
"""
import asyncio
from app.core.database import init_db, close_db
from app.models.product import ProductCategory

async def init_categories():
    """初始化商品分类数据"""
    await init_db()
    
    categories = [
        {
            "category_key": "textbook",
            "category_name": "教材书籍",
            "icon": "book",
            "sort_order": 1
        },
        {
            "category_key": "digital",
            "category_name": "数码电子",
            "icon": "laptop",
            "sort_order": 2
        },
        {
            "category_key": "clothing",
            "category_name": "服装配饰",
            "icon": "shirt",
            "sort_order": 3
        },
        {
            "category_key": "daily",
            "category_name": "生活用品",
            "icon": "home",
            "sort_order": 4
        },
        {
            "category_key": "sports",
            "category_name": "运动器材",
            "icon": "dumbbell",
            "sort_order": 5
        },
        {
            "category_key": "beauty",
            "category_name": "美妆护肤",
            "icon": "palette",
            "sort_order": 6
        },
        {
            "category_key": "food",
            "category_name": "食品零食",
            "icon": "utensils",
            "sort_order": 7
        },
        {
            "category_key": "other",
            "category_name": "其他",
            "icon": "ellipsis",
            "sort_order": 8
        }
    ]
    
    for category_data in categories:
        # 检查分类是否已存在
        existing = await ProductCategory.filter(category_key=category_data["category_key"]).first()
        if not existing:
            await ProductCategory.create(**category_data)
            print(f"创建分类: {category_data['category_name']}")
        else:
            print(f"分类已存在: {category_data['category_name']}")
    
    await close_db()
    print("商品分类初始化完成！")

if __name__ == "__main__":
    asyncio.run(init_categories())
