<script setup lang="ts">
import { onMounted } from 'vue'
import { useUserStore, useAppStore } from '@/stores'
import AppHeader from '@/components/common/AppHeader.vue'

const userStore = useUserStore()
const appStore = useAppStore()

onMounted(() => {
  // 初始化应用状态
  appStore.initSearchHistory()

  // 如果有token，尝试获取用户信息
  if (userStore.token) {
    userStore.fetchUserInfo()
  }

  // 检测设备类型
  const checkDevice = () => {
    const width = window.innerWidth
    if (width < 768) {
      appStore.setDevice('mobile')
    } else if (width < 1024) {
      appStore.setDevice('tablet')
    } else {
      appStore.setDevice('desktop')
    }
  }

  checkDevice()
  window.addEventListener('resize', checkDevice)
})
</script>

<template>
  <div id="app" class="app-container">
    <!-- 头部导航 -->
    <AppHeader v-if="userStore.isLoggedIn" />

    <!-- 主要内容区域 -->
    <main class="main-content" :class="{ 'no-header': !userStore.isLoggedIn }">
      <router-view />
    </main>

    <!-- 全局加载状态 -->
    <div v-if="appStore.loading" class="global-loading">
      <el-loading-service />
    </div>
  </div>
</template>

<style lang="stylus">
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f5f5;
}

#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  overflow-y: auto;

  &.no-header {
    height: 100vh;
  }
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 9999;
}

// Element Plus 样式覆盖
.el-header {
  padding: 0;
  height: 60px !important;
}

// 响应式设计
@media (max-width: 768px) {
  .main-content {
    padding: 0;
  }
}
</style>
