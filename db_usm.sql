-- 校园二手交易平台数据库设计
-- 数据库版本: MySQL 8.0
-- 数据库名: db_usm
-- 字符集: utf8mb4
-- 排序规则: utf8mb4_general_ci

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `db_usm` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_general_ci;

USE `db_usm`;

-- 1. 角色表
CREATE TABLE `sys_roles` (
  `role_id` int NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `role_key` varchar(50) NOT NULL COMMENT '角色标识',
  `description` varchar(200) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`role_id`),
  UNIQUE KEY `uk_role_key` (`role_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色表';

-- 2. 用户表
CREATE TABLE `users` (
  `user_id` int NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码（加密）',
  `nickname` varchar(50) NOT NULL COMMENT '昵称',
  `avatar` varchar(500) DEFAULT '' COMMENT '头像URL',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `sex` tinyint(1) NOT NULL COMMENT '性别：0-男，1-女',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态：active-正常，disabled-禁用，deleted-删除',
  `role_key` varchar(50) NOT NULL DEFAULT 'student' COMMENT '角色标识',
  `campus` varchar(50) DEFAULT NULL COMMENT '校区',
  `student_id` varchar(50) DEFAULT NULL COMMENT '学号',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `is_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否实名认证：1-是，0-否',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_role_key` (`role_key`),
  KEY `idx_campus` (`campus`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户表';

-- 3. 商品分类表
CREATE TABLE `product_categories` (
  `category_id` int NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_key` varchar(50) NOT NULL COMMENT '分类标识',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  `icon` varchar(50) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`category_id`),
  UNIQUE KEY `uk_category_key` (`category_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品分类表';

-- 4. 商品表
CREATE TABLE `products` (
  `product_id` int NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `title` varchar(200) NOT NULL COMMENT '商品标题',
  `description` text NOT NULL COMMENT '商品描述',
  `category` varchar(50) NOT NULL COMMENT '商品分类',
  `price` decimal(10,2) NOT NULL COMMENT '售价',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `condition_type` varchar(20) NOT NULL COMMENT '新旧程度：new-全新，like_new-几乎全新，good-良好，fair-一般，poor-较差',
  `images` json DEFAULT NULL COMMENT '商品图片JSON数组',
  `seller_id` int NOT NULL COMMENT '卖家ID',
  `campus` varchar(50) NOT NULL COMMENT '校区',
  `location` varchar(200) DEFAULT NULL COMMENT '具体位置',
  `status` varchar(20) NOT NULL DEFAULT 'draft' COMMENT '状态：draft-草稿，published-已发布，sold-已售出，removed-已下架',
  `view_count` int NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞次数',
  `tags` json DEFAULT NULL COMMENT '标签JSON数组',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：1-是，0-否',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`product_id`),
  KEY `idx_seller_id` (`seller_id`),
  KEY `idx_category` (`category`),
  KEY `idx_campus` (`campus`),
  KEY `idx_status` (`status`),
  KEY `idx_price` (`price`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_view_count` (`view_count`),
  CONSTRAINT `fk_products_seller` FOREIGN KEY (`seller_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品表';

-- 5. 订单表
CREATE TABLE `orders` (
  `order_id` int NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `product_id` int NOT NULL COMMENT '商品ID',
  `buyer_id` int NOT NULL COMMENT '买家ID',
  `seller_id` int NOT NULL COMMENT '卖家ID',
  `price` decimal(10,2) NOT NULL COMMENT '交易价格',
  `status` varchar(30) NOT NULL DEFAULT 'pending_payment' COMMENT '订单状态：pending_payment-待付款，pending_pickup-待取货，pending_confirm-待确认，completed-已完成，cancelled-已取消',
  `pickup_location` varchar(200) DEFAULT NULL COMMENT '取货地点',
  `pickup_time` datetime DEFAULT NULL COMMENT '取货时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_buyer_id` (`buyer_id`),
  KEY `idx_seller_id` (`seller_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_orders_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`product_id`),
  CONSTRAINT `fk_orders_buyer` FOREIGN KEY (`buyer_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `fk_orders_seller` FOREIGN KEY (`seller_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单表';

-- 6. 求购表
CREATE TABLE `wanted_items` (
  `wanted_id` int NOT NULL AUTO_INCREMENT COMMENT '求购ID',
  `title` varchar(200) NOT NULL COMMENT '求购标题',
  `description` text NOT NULL COMMENT '求购描述',
  `category` varchar(50) NOT NULL COMMENT '商品分类',
  `max_price` decimal(10,2) DEFAULT NULL COMMENT '最高价格',
  `user_id` int NOT NULL COMMENT '发布用户ID',
  `campus` varchar(50) NOT NULL COMMENT '校区',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态：active-有效，completed-已完成，cancelled-已取消',
  `response_count` int NOT NULL DEFAULT '0' COMMENT '响应次数',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：1-是，0-否',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`wanted_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_category` (`category`),
  KEY `idx_campus` (`campus`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_wanted_items_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='求购表';

-- 7. 评论表
CREATE TABLE `comments` (
  `comment_id` int NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `product_id` int NOT NULL COMMENT '商品ID',
  `user_id` int NOT NULL COMMENT '评论用户ID',
  `content` text NOT NULL COMMENT '评论内容',
  `parent_id` int DEFAULT NULL COMMENT '父评论ID（回复）',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：1-是，0-否',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`comment_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_comments_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`product_id`),
  CONSTRAINT `fk_comments_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `fk_comments_parent` FOREIGN KEY (`parent_id`) REFERENCES `comments` (`comment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='评论表';

-- 8. 消息表
CREATE TABLE `messages` (
  `message_id` int NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `type` varchar(20) NOT NULL COMMENT '消息类型：system-系统消息，order-订单消息，comment-评论消息，like-点赞消息',
  `title` varchar(200) NOT NULL COMMENT '消息标题',
  `content` text NOT NULL COMMENT '消息内容',
  `user_id` int NOT NULL COMMENT '接收用户ID',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读：1-是，0-否',
  `related_id` int DEFAULT NULL COMMENT '关联ID（如订单ID、商品ID等）',
  `related_type` varchar(20) DEFAULT NULL COMMENT '关联类型：order-订单，product-商品，comment-评论',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`message_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_messages_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='消息表';

-- 9. 商品收藏表
CREATE TABLE `product_favorites` (
  `favorite_id` int NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `product_id` int NOT NULL COMMENT '商品ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`favorite_id`),
  UNIQUE KEY `uk_user_product` (`user_id`,`product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_favorites_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `fk_favorites_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品收藏表';

-- 10. 举报表
CREATE TABLE `reports` (
  `report_id` int NOT NULL AUTO_INCREMENT COMMENT '举报ID',
  `type` varchar(50) NOT NULL COMMENT '举报类型：fake_product-虚假商品，inappropriate_content-不当内容，fraud-欺诈，spam-垃圾信息，other-其他',
  `target_type` varchar(20) NOT NULL COMMENT '举报目标类型：product-商品，user-用户，comment-评论',
  `target_id` int NOT NULL COMMENT '举报目标ID',
  `reason` varchar(200) NOT NULL COMMENT '举报原因',
  `description` text DEFAULT NULL COMMENT '详细描述',
  `reporter_id` int NOT NULL COMMENT '举报人ID',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '处理状态：pending-待处理，processed-已处理，rejected-已驳回',
  `handler_id` int DEFAULT NULL COMMENT '处理人ID',
  `handle_result` text DEFAULT NULL COMMENT '处理结果',
  `handle_time` datetime DEFAULT NULL COMMENT '处理时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`report_id`),
  KEY `idx_reporter_id` (`reporter_id`),
  KEY `idx_target_type_id` (`target_type`,`target_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_reports_reporter` FOREIGN KEY (`reporter_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='举报表';

-- 11. 申诉表
CREATE TABLE `appeals` (
  `appeal_id` int NOT NULL AUTO_INCREMENT COMMENT '申诉ID',
  `type` varchar(50) NOT NULL COMMENT '申诉类型：product_removal-商品被删，account_ban-账号被封，order_dispute-订单争议，other-其他',
  `title` varchar(200) NOT NULL COMMENT '申诉标题',
  `description` text NOT NULL COMMENT '申诉描述',
  `user_id` int NOT NULL COMMENT '申诉用户ID',
  `related_id` int DEFAULT NULL COMMENT '关联ID',
  `related_type` varchar(20) DEFAULT NULL COMMENT '关联类型：product-商品，order-订单，user-用户',
  `evidence` json DEFAULT NULL COMMENT '证据文件JSON数组',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '处理状态：pending-待处理，approved-已通过，rejected-已驳回',
  `handler_id` int DEFAULT NULL COMMENT '处理人ID',
  `handle_result` text DEFAULT NULL COMMENT '处理结果',
  `handle_time` datetime DEFAULT NULL COMMENT '处理时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`appeal_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_appeals_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='申诉表';

-- 12. 系统配置表
CREATE TABLE `system_config` (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string-字符串，number-数字，boolean-布尔，json-JSON',
  `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否系统配置：1-是，0-否',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`config_id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统配置表';

-- ================================
-- 初始化数据
-- ================================

-- 初始化角色数据
INSERT INTO `sys_roles` (`role_name`, `role_key`, `description`) VALUES
('学生', 'student', '普通学生用户'),
('管理员', 'admin', '系统管理员'),
('超级管理员', 'super_admin', '超级管理员');

-- 初始化商品分类数据
INSERT INTO `product_categories` (`category_key`, `category_name`, `icon`, `sort_order`) VALUES
('textbook', '教材书籍', 'Document', 1),
('digital', '数码电子', 'Monitor', 2),
('clothing', '服装配饰', 'User', 3),
('daily', '生活用品', 'Coffee', 4),
('sports', '运动器材', 'Star', 5),
('beauty', '美妆护肤', 'Brush', 6),
('food', '食品零食', 'Apple', 7),
('other', '其他', 'MoreFilled', 8);

-- 初始化测试用户数据（密码都是123456的bcrypt加密）
INSERT INTO `users` (`username`, `password`, `nickname`, `email`, `phone`, `sex`, `role_key`, `campus`, `student_id`, `real_name`, `is_verified`) VALUES
('student', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5/Plq', '学生小明', '<EMAIL>', '13800138001', 0, 'student', 'main', '2021001', '张三', 1),
('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5/Plq', '管理员', '<EMAIL>', '13800138002', 1, 'admin', 'main', NULL, '李四', 1),
('student2', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5/Plq', '小红', '<EMAIL>', '13800138003', 1, 'student', 'main', '2021002', '王红', 1),
('student3', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5/Plq', '小李', '<EMAIL>', '13800138004', 0, 'student', 'north', '2021003', '李明', 0),
('student4', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5/Plq', '小王', '<EMAIL>', '13800138005', 1, 'student', 'north', '2021004', '王丽', 0);

-- 初始化测试商品数据
INSERT INTO `products` (`title`, `description`, `category`, `price`, `original_price`, `condition_type`, `images`, `seller_id`, `campus`, `location`, `status`, `view_count`, `like_count`, `tags`) VALUES
('高等数学教材（第七版）', '同济大学出版社，九成新，无笔记，适合大一学生使用。书本保存完好，内容清晰。', 'textbook', 25.00, 45.00, 'good', '["https://example.com/book1.jpg", "https://example.com/book2.jpg"]', 2, 'main', '图书馆附近', 'published', 156, 23, '["教材", "数学", "同济"]'),
('iPhone 13 二手', '9成新iPhone 13，128GB，无磕碰，功能正常，配件齐全。', 'digital', 4500.00, 6999.00, 'like_new', '["https://example.com/phone1.jpg", "https://example.com/phone2.jpg"]', 3, 'main', '宿舍楼下', 'published', 89, 15, '["手机", "苹果", "iPhone"]'),
('Nike运动鞋', '正品Nike运动鞋，42码，穿过几次，九成新。', 'clothing', 280.00, 599.00, 'like_new', '["https://example.com/shoes1.jpg"]', 4, 'north', '体育馆', 'published', 45, 8, '["运动鞋", "Nike", "正品"]'),
('小米笔记本电脑', '小米笔记本Pro 14，i5处理器，16G内存，512G固态，使用一年。', 'digital', 3200.00, 4999.00, 'good', '["https://example.com/laptop1.jpg", "https://example.com/laptop2.jpg"]', 5, 'north', '宿舍', 'published', 78, 12, '["笔记本", "小米", "电脑"]');

-- 初始化测试订单数据
INSERT INTO `orders` (`order_no`, `product_id`, `buyer_id`, `seller_id`, `price`, `status`, `pickup_location`, `remark`) VALUES
('ORD202401150001', 1, 1, 2, 25.00, 'pending_pickup', '图书馆门口', '下午3点后取货'),
('ORD202401150002', 2, 4, 3, 4500.00, 'completed', '宿舍楼下', '已完成交易'),
('ORD202401150003', 3, 1, 4, 280.00, 'pending_confirm', '体育馆', '等待确认收货');

-- 初始化测试求购数据
INSERT INTO `wanted_items` (`title`, `description`, `category`, `max_price`, `user_id`, `campus`, `response_count`) VALUES
('求购计算机网络教材', '需要计算机网络第7版教材，八成新以上', 'textbook', 30.00, 4, 'main', 2),
('求购二手自行车', '需要一辆二手自行车，用于校园代步，价格在200-500元之间', 'sports', 500.00, 5, 'north', 1),
('求购iPad', '需要一台二手iPad，用于学习，预算2000-3000元', 'digital', 3000.00, 1, 'main', 0);

-- 初始化测试评论数据
INSERT INTO `comments` (`product_id`, `user_id`, `content`, `parent_id`) VALUES
(1, 1, '这本书质量不错，内容很清晰，推荐购买！', NULL),
(1, 3, '同意楼上，我也买过这个版本的教材。', 1),
(2, 5, '手机成色很好，卖家很诚信。', NULL),
(3, 2, '鞋子质量不错，尺码合适。', NULL);

-- 初始化测试消息数据
INSERT INTO `messages` (`type`, `title`, `content`, `user_id`, `related_id`, `related_type`) VALUES
('system', '欢迎使用校园二手交易平台', '欢迎您注册使用校园二手交易平台，请遵守平台规则，诚信交易。', 1, NULL, NULL),
('order', '订单状态更新', '您的订单ORD202401150001状态已更新为待取货，请及时取货。', 1, 1, 'order'),
('comment', '商品收到新评论', '您的商品"高等数学教材（第七版）"收到了新的评论。', 2, 1, 'product'),
('system', '实名认证提醒', '为了更好的交易体验，建议您完成实名认证。', 4, NULL, NULL);

-- 初始化测试收藏数据
INSERT INTO `product_favorites` (`user_id`, `product_id`) VALUES
(1, 2),
(1, 3),
(3, 1),
(4, 2),
(5, 1);

-- 初始化系统配置数据
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`, `is_system`) VALUES
('site_name', '校园二手交易平台', 'string', '网站名称', 1),
('site_description', '安全、便捷的校园二手交易平台', 'string', '网站描述', 1),
('max_upload_size', '5242880', 'number', '最大上传文件大小（字节）', 1),
('max_images_per_product', '9', 'number', '每个商品最多上传图片数量', 1),
('default_page_size', '20', 'number', '默认分页大小', 1),
('enable_registration', 'true', 'boolean', '是否开放注册', 1),
('require_email_verification', 'false', 'boolean', '是否需要邮箱验证', 1),
('campus_list', '["main", "north", "south", "east", "west"]', 'json', '校区列表', 1),
('contact_email', '<EMAIL>', 'string', '联系邮箱', 1),
('announcement', '欢迎使用校园二手交易平台！请诚信交易，共建和谐校园。', 'string', '平台公告', 0);

-- ================================
-- 创建视图
-- ================================

-- 商品详情视图（包含卖家信息）
CREATE VIEW `v_product_details` AS
SELECT
    p.`product_id`,
    p.`title`,
    p.`description`,
    p.`category`,
    pc.`category_name`,
    p.`price`,
    p.`original_price`,
    p.`condition_type`,
    p.`images`,
    p.`seller_id`,
    u.`username` AS `seller_username`,
    u.`nickname` AS `seller_nickname`,
    u.`avatar` AS `seller_avatar`,
    u.`campus` AS `seller_campus`,
    u.`is_verified` AS `seller_verified`,
    p.`campus`,
    p.`location`,
    p.`status`,
    p.`view_count`,
    p.`like_count`,
    p.`tags`,
    p.`create_time`,
    p.`update_time`
FROM `products` p
LEFT JOIN `users` u ON p.`seller_id` = u.`user_id`
LEFT JOIN `product_categories` pc ON p.`category` = pc.`category_key`
WHERE p.`is_deleted` = 0;

-- 订单详情视图（包含买家、卖家、商品信息）
CREATE VIEW `v_order_details` AS
SELECT
    o.`order_id`,
    o.`order_no`,
    o.`product_id`,
    p.`title` AS `product_title`,
    p.`images` AS `product_images`,
    p.`category` AS `product_category`,
    o.`buyer_id`,
    buyer.`username` AS `buyer_username`,
    buyer.`nickname` AS `buyer_nickname`,
    buyer.`phone` AS `buyer_phone`,
    o.`seller_id`,
    seller.`username` AS `seller_username`,
    seller.`nickname` AS `seller_nickname`,
    seller.`phone` AS `seller_phone`,
    o.`price`,
    o.`status`,
    o.`pickup_location`,
    o.`pickup_time`,
    o.`remark`,
    o.`create_time`,
    o.`update_time`
FROM `orders` o
LEFT JOIN `products` p ON o.`product_id` = p.`product_id`
LEFT JOIN `users` buyer ON o.`buyer_id` = buyer.`user_id`
LEFT JOIN `users` seller ON o.`seller_id` = seller.`user_id`;

-- 求购详情视图（包含用户信息）
CREATE VIEW `v_wanted_details` AS
SELECT
    w.`wanted_id`,
    w.`title`,
    w.`description`,
    w.`category`,
    pc.`category_name`,
    w.`max_price`,
    w.`user_id`,
    u.`username`,
    u.`nickname`,
    u.`avatar`,
    u.`campus` AS `user_campus`,
    w.`campus`,
    w.`status`,
    w.`response_count`,
    w.`create_time`,
    w.`update_time`
FROM `wanted_items` w
LEFT JOIN `users` u ON w.`user_id` = u.`user_id`
LEFT JOIN `product_categories` pc ON w.`category` = pc.`category_key`
WHERE w.`is_deleted` = 0;

-- 评论详情视图（包含用户信息）
CREATE VIEW `v_comment_details` AS
SELECT
    c.`comment_id`,
    c.`product_id`,
    c.`user_id`,
    u.`username`,
    u.`nickname`,
    u.`avatar`,
    c.`content`,
    c.`parent_id`,
    c.`create_time`,
    c.`update_time`
FROM `comments` c
LEFT JOIN `users` u ON c.`user_id` = u.`user_id`
WHERE c.`is_deleted` = 0;

-- ================================
-- 创建存储过程
-- ================================

-- 更新商品浏览次数
DELIMITER //
CREATE PROCEDURE `sp_increment_product_view`(IN `p_product_id` INT)
BEGIN
    UPDATE `products`
    SET `view_count` = `view_count` + 1
    WHERE `product_id` = p_product_id AND `is_deleted` = 0;
END //
DELIMITER ;

-- 更新商品点赞次数
DELIMITER //
CREATE PROCEDURE `sp_increment_product_like`(IN `p_product_id` INT)
BEGIN
    UPDATE `products`
    SET `like_count` = `like_count` + 1
    WHERE `product_id` = p_product_id AND `is_deleted` = 0;
END //
DELIMITER ;

-- 创建订单号
DELIMITER //
CREATE FUNCTION `fn_generate_order_no`() RETURNS VARCHAR(50)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE order_no VARCHAR(50);
    DECLARE order_count INT DEFAULT 0;

    SELECT COUNT(*) INTO order_count
    FROM `orders`
    WHERE DATE(`create_time`) = CURDATE();

    SET order_no = CONCAT('ORD', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(order_count + 1, 4, '0'));

    RETURN order_no;
END //
DELIMITER ;

-- ================================
-- 创建触发器
-- ================================

-- 商品状态变更时更新时间
DELIMITER //
CREATE TRIGGER `tr_products_status_update`
    BEFORE UPDATE ON `products`
    FOR EACH ROW
BEGIN
    IF OLD.`status` != NEW.`status` THEN
        SET NEW.`update_time` = CURRENT_TIMESTAMP;
    END IF;
END //
DELIMITER ;

-- 订单创建时自动生成订单号
DELIMITER //
CREATE TRIGGER `tr_orders_before_insert`
    BEFORE INSERT ON `orders`
    FOR EACH ROW
BEGIN
    IF NEW.`order_no` IS NULL OR NEW.`order_no` = '' THEN
        SET NEW.`order_no` = fn_generate_order_no();
    END IF;
END //
DELIMITER ;

-- 用户最后登录时间更新
DELIMITER //
CREATE TRIGGER `tr_users_login_time`
    BEFORE UPDATE ON `users`
    FOR EACH ROW
BEGIN
    IF OLD.`last_login_time` != NEW.`last_login_time` THEN
        SET NEW.`update_time` = CURRENT_TIMESTAMP;
    END IF;
END //
DELIMITER ;

-- ================================
-- 常用查询示例
-- ================================

-- 查询热门商品（按浏览量排序）
-- SELECT * FROM v_product_details WHERE status = 'published' ORDER BY view_count DESC LIMIT 10;

-- 查询用户的商品收藏
-- SELECT p.*, pf.create_time as favorite_time
-- FROM product_favorites pf
-- JOIN v_product_details p ON pf.product_id = p.product_id
-- WHERE pf.user_id = ? ORDER BY pf.create_time DESC;

-- 查询商品的评论（包含回复）
-- SELECT * FROM v_comment_details WHERE product_id = ? ORDER BY create_time ASC;

-- 查询用户的订单（买家视角）
-- SELECT * FROM v_order_details WHERE buyer_id = ? ORDER BY create_time DESC;

-- 查询用户的订单（卖家视角）
-- SELECT * FROM v_order_details WHERE seller_id = ? ORDER BY create_time DESC;

-- 统计各分类商品数量
-- SELECT category, category_name, COUNT(*) as product_count
-- FROM v_product_details
-- WHERE status = 'published'
-- GROUP BY category, category_name
-- ORDER BY product_count DESC;

-- 统计用户交易数据
-- SELECT
--   u.user_id,
--   u.nickname,
--   COUNT(DISTINCT p.product_id) as published_products,
--   COUNT(DISTINCT o1.order_id) as buy_orders,
--   COUNT(DISTINCT o2.order_id) as sell_orders
-- FROM users u
-- LEFT JOIN products p ON u.user_id = p.seller_id AND p.is_deleted = 0
-- LEFT JOIN orders o1 ON u.user_id = o1.buyer_id
-- LEFT JOIN orders o2 ON u.user_id = o2.seller_id
-- WHERE u.status = 'active'
-- GROUP BY u.user_id, u.nickname;

-- ================================
-- 数据库设计说明
-- ================================

/*
数据库设计特点：

1. 表结构设计
   - 采用InnoDB存储引擎，支持事务和外键约束
   - 使用utf8mb4字符集，支持emoji和特殊字符
   - 合理设置字段类型和长度，优化存储空间
   - 添加必要的索引，提高查询性能

2. 字段设计
   - 统一使用下划线命名法
   - 主键统一使用自增ID
   - 时间字段统一使用datetime类型
   - 状态字段使用varchar存储，便于扩展
   - JSON字段存储数组类型数据（如图片、标签）

3. 约束设计
   - 设置外键约束，保证数据完整性
   - 添加唯一约束，防止重复数据
   - 使用软删除，保留历史数据

4. 索引设计
   - 主键自动创建聚簇索引
   - 外键字段创建索引
   - 常用查询字段创建索引
   - 组合查询创建复合索引

5. 视图设计
   - 创建常用查询视图，简化业务查询
   - 视图包含关联表信息，减少JOIN操作
   - 提高查询效率和代码可维护性

6. 存储过程和函数
   - 封装常用业务逻辑
   - 提高执行效率
   - 减少网络传输

7. 触发器
   - 自动处理业务逻辑
   - 保证数据一致性
   - 记录操作日志

8. 安全考虑
   - 密码字段加密存储
   - 敏感信息脱敏处理
   - 软删除保护数据

9. 性能优化
   - 合理设计索引
   - 使用视图简化查询
   - 分页查询优化
   - 读写分离支持

10. 扩展性
    - 预留扩展字段
    - 模块化设计
    - 支持水平扩展
*/
