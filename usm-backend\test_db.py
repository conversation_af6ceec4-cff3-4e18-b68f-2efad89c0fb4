#!/usr/bin/env python3
"""
测试数据库连接
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import engine, SessionLocal
from app.models.user import User, Role
from sqlalchemy import text

def test_connection():
    """测试数据库连接"""
    try:
        # 测试连接
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            print("✅ 数据库连接成功!")
            
        # 测试会话
        db = SessionLocal()
        try:
            # 查询用户数量
            user_count = db.query(User).count()
            print(f"✅ 用户表查询成功! 当前用户数量: {user_count}")
            
            # 查询角色数量
            role_count = db.query(Role).count()
            print(f"✅ 角色表查询成功! 当前角色数量: {role_count}")
            
            # 查询测试用户
            test_user = db.query(User).filter(User.username == "student").first()
            if test_user:
                print(f"✅ 找到测试用户: {test_user.nickname} ({test_user.email})")
            else:
                print("⚠️ 未找到测试用户 'student'")
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("开始测试数据库连接...")
    success = test_connection()
    if success:
        print("\n🎉 数据库测试完成!")
    else:
        print("\n💥 数据库测试失败!")
        sys.exit(1)
