// 用户状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '@/types'
import { getToken, setToken, removeToken, getUserInfo, setUserInfo, removeUserInfo } from '@/utils'
import { userInfoApi, logoutApi } from '@/request/loginApi'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string | null>(getToken())
  const userInfo = ref<User | null>(getUserInfo())
  const isLoggedIn = computed(() => !!token.value)

  // 设置token
  const setUserToken = (newToken: string) => {
    token.value = newToken
    setToken(newToken)
  }

  // 设置用户信息
  const setUser = (user: any) => {
    // 确保用户信息符合 User 类型要求
    const userData: User = {
      ...user,
      // 添加可能缺少的必填字段，如果API返回的数据中没有这些字段
      nickname: user.nickname || user.username || '',
      roleKey: user.roleKey || 'student'
    }
    userInfo.value = userData
    setUserInfo(userData)
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await userInfoApi()
      if (response.code === 200) {
        setUser(response.data)
        return response.data
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 检查是否为网络错误，如果是网络错误则不登出
      if (error?.name !== 'AxiosError' || error?.code !== 'ERR_NETWORK') {
        // 只有在非网络错误的情况下才登出
        logout()
      }
    }
  }

  // 登录
  const login = async (loginToken: string, user?: User) => {
    setUserToken(loginToken)
    if (user) {
      // 如果直接传入用户信息，则直接设置
      setUser(user)
    } else {
      // 否则从API获取用户信息
      await fetchUserInfo()
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await logoutApi()
      }
    } catch (error) {
      console.error('登出失败:', error)
    } finally {
      // 清除本地数据
      token.value = null
      userInfo.value = null
      removeToken()
      removeUserInfo()
    }
  }

  // 更新用户信息
  const updateUserInfo = (updates: Partial<User>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...updates }
      setUserInfo(userInfo.value)
    }
  }

  // 检查权限
  const hasRole = (role: string) => {
    return userInfo.value?.roleKey === role
  }

  // 检查是否为管理员
  const isAdmin = computed(() => {
    return userInfo.value?.roleKey === 'admin' || userInfo.value?.roleKey === 'super_admin'
  })

  // 检查是否为学生
  const isStudent = computed(() => {
    return userInfo.value?.roleKey === 'student'
  })

  return {
    // 状态
    token,
    userInfo,
    isLoggedIn,
    isAdmin,
    isStudent,
    
    // 方法
    setUserToken,
    setUser,
    fetchUserInfo,
    login,
    logout,
    updateUserInfo,
    hasRole
  }
})
