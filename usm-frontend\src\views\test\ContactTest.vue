<template>
  <div class="contact-test">
    <h2>联系卖家对话框测试</h2>
    
    <el-button type="primary" @click="showDialog">
      打开联系卖家对话框
    </el-button>

    <!-- 联系卖家对话框 -->
    <ContactSellerDialog
      v-model="dialogVisible"
      :seller-info="sellerInfo"
      @message-sent="handleMessageSent"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import ContactSellerDialog from '@/components/common/ContactSellerDialog.vue'

const dialogVisible = ref(false)

// 卖家信息
const sellerInfo = ref({
  userId: 2,
  nickname: '小红',
  avatar: '',
  phone: '138****8888',
  wechat: 'user_wechat_123',
  showPhone: true,
  showWechat: true,
  dealCount: 15,
  goodRate: 98
})

const showDialog = () => {
  dialogVisible.value = true
}

const handleMessageSent = () => {
  ElMessage.success('消息已发送，请等待卖家回复')
}
</script>

<style lang="stylus" scoped>
.contact-test
  padding 40px
  text-align center
  
  h2
    margin-bottom 30px
    color #333
    
  .el-button
    font-size 16px
    padding 12px 24px
</style>
