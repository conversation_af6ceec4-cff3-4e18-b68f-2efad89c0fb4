<template>
  <div class="home-container">
    

    <!-- 轮播图 -->
    <section class="banner-section">
      <el-carousel height="300px" indicator-position="outside">
        <el-carousel-item v-for="item in banners" :key="item.id">
          <div class="banner-item" :style="{ backgroundImage: `url(${item.image})` }">
            <div class="banner-content">
              <h3>{{ item.title }}</h3>
              <p>{{ item.description }}</p>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </section>

    <!-- 分类导航 -->
    <section class="category-section">
      <div class="section-header">
        <h2>商品分类</h2>
      </div>
      <div class="category-grid">
        <div
          v-for="category in categories"
          :key="category.code"
          class="category-item"
          @click="goToCategory(category.code)"
        >
          <el-icon :size="32">
            <component :is="category.icon" />
          </el-icon>
          <span>{{ category.name }}</span>
        </div>
      </div>
    </section>

    <!-- 热门商品 -->
    <section class="products-section">
      <div class="section-header">
        <h2>热门商品</h2>
        <el-button type="text" @click="goToProducts">查看更多</el-button>
      </div>
      <div class="products-grid">
        <div
          v-for="product in hotProducts"
          :key="product.productId"
          class="product-card"
          @click="goToProduct(product.productId)"
        >
          <div class="product-image">
            <el-image
              :src="product.images[0]"
              fit="cover"
              :lazy="true"
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </div>
          <div class="product-info">
            <h4 class="product-title">{{ product.title }}</h4>
            <p class="product-price">¥{{ product.price }}</p>
            <div class="product-meta">
              <span class="product-campus">{{ product.campus }}</span>
              <span class="product-time">{{ formatTime(product.createTime) }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 求购信息 -->
    <section class="wanted-section">
      <div class="section-header">
        <h2>最新求购</h2>
        <el-button type="text" @click="goToWanted">查看更多</el-button>
      </div>
      <div class="wanted-list">
        <div
          v-for="wanted in wantedItems"
          :key="wanted.wantedId"
          class="wanted-item"
        >
          <div class="wanted-content">
            <h4>{{ wanted.title }}</h4>
            <p>{{ wanted.description }}</p>
            <div class="wanted-meta">
              <span class="wanted-price">预算: ¥{{ wanted.maxPrice || '面议' }}</span>
              <span class="wanted-campus">{{ wanted.campus }}</span>
              <span class="wanted-time">{{ formatTime(wanted.createTime) }}</span>
            </div>
          </div>
          <el-button size="small" type="primary" @click="handleContactWanted(wanted)">联系TA</el-button>
        </div>
      </div>
    </section>

    <!-- 联系求购者对话框 -->
    <ContactSellerDialog
      v-model="contactDialogVisible"
      :seller-info="currentWantedUser"
      title="联系求购者"
      @message-sent="handleMessageSent"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import {
  Document, Monitor, User, Coffee, Star, Brush, Apple, MoreFilled, Picture,
  Goods, Bicycle, Headset, ShoppingCart, Collection
} from '@element-plus/icons-vue'
import { formatTime } from '@/utils'
import { PRODUCT_CATEGORIES, PRODUCT_CATEGORY_LABELS, CAMPUS_LABELS } from '@/constants'
import ContactSellerDialog from '@/components/common/ContactSellerDialog.vue'

const router = useRouter()
const userStore = useUserStore()

// 联系对话框相关
const contactDialogVisible = ref(false)
const currentWantedUser = ref({
  userId: 0,
  nickname: '',
  avatar: '',
  phone: '138****8888',
  wechat: 'user_wechat',
  showPhone: true,
  showWechat: true,
  dealCount: 5,
  goodRate: 95
})

// 轮播图数据
const banners = ref([
  {
    id: 1,
    title: '校园二手交易平台',
    description: '安全、便捷的校内交易体验',
    image: '/src/assets/phone.png'
  },
  {
    id: 2,
    title: '教材书籍专区',
    description: '低价好书，知识传递',
    image: '/src/assets/book.jpg'
  }
])

// 分类数据
const categories = ref([
  { code: PRODUCT_CATEGORIES.TEXTBOOK, name: PRODUCT_CATEGORY_LABELS[PRODUCT_CATEGORIES.TEXTBOOK], icon: Document },
  { code: PRODUCT_CATEGORIES.DIGITAL, name: PRODUCT_CATEGORY_LABELS[PRODUCT_CATEGORIES.DIGITAL], icon: Monitor },
  { code: PRODUCT_CATEGORIES.CLOTHING, name: PRODUCT_CATEGORY_LABELS[PRODUCT_CATEGORIES.CLOTHING], icon: User },
  { code: PRODUCT_CATEGORIES.DAILY, name: PRODUCT_CATEGORY_LABELS[PRODUCT_CATEGORIES.DAILY], icon: ShoppingCart },
  { code: PRODUCT_CATEGORIES.SPORTS, name: PRODUCT_CATEGORY_LABELS[PRODUCT_CATEGORIES.SPORTS], icon: Bicycle },
  { code: PRODUCT_CATEGORIES.BEAUTY, name: PRODUCT_CATEGORY_LABELS[PRODUCT_CATEGORIES.BEAUTY], icon: Brush },
  { code: PRODUCT_CATEGORIES.FOOD, name: PRODUCT_CATEGORY_LABELS[PRODUCT_CATEGORIES.FOOD], icon: Apple },
  { code: PRODUCT_CATEGORIES.OTHER, name: PRODUCT_CATEGORY_LABELS[PRODUCT_CATEGORIES.OTHER], icon: MoreFilled }
])

// 热门商品数据（模拟）
const hotProducts = ref([
  {
    productId: 1,
    title: '高等数学教材',
    price: 25,
    images: ['/src/assets/book.jpg'],
    campus: '主校区',
    createTime: '2024-01-15T10:30:00'
  },
  {
    productId: 2,
    title: 'iPhone 13 Pro',
    price: 4500,
    images: ['/src/assets/phone.png'],
    campus: '北校区',
    createTime: '2024-01-14T15:20:00'
  }
])

// 求购信息数据（模拟）
const wantedItems = ref([
  {
    wantedId: 1,
    title: '求购计算机网络教材',
    description: '需要计算机网络第7版教材，八成新以上',
    maxPrice: 30,
    campus: '主校区',
    createTime: '2024-01-15T09:00:00',
    userInfo: {
      userId: 3,
      username: 'student3',
      nickname: '小李',
      avatar: '',
      email: '<EMAIL>',
      phone: '138****5678',
      sex: '0',
      status: 'active',
      roleKey: 'student'
    }
  },
  {
    wantedId: 2,
    title: '求购二手自行车',
    description: '需要一辆二手自行车，用于校园代步，价格在200-500元之间',
    maxPrice: 500,
    campus: '北校区',
    createTime: '2024-01-16T14:30:00',
    userInfo: {
      userId: 4,
      username: 'student4',
      nickname: '小王',
      avatar: '',
      email: '<EMAIL>',
      phone: '139****9999',
      sex: '1',
      status: 'active',
      roleKey: 'student'
    }
  }
])

// 导航方法
const goToCategory = (category: string) => {
  router.push({ name: 'ProductList', query: { category } })
}

const goToProducts = () => {
  router.push({ name: 'ProductList' })
}

const goToProduct = (id: number) => {
  router.push({ name: 'ProductDetail', params: { id } })
}

const goToWanted = () => {
  router.push({ name: 'WantedList' })
}

// 联系求购者
const handleContactWanted = (wanted: any) => {
  // 检查用户是否登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再联系求购者')
    router.push({ name: 'Login' })
    return
  }

  // 设置当前求购者信息
  currentWantedUser.value = {
    userId: wanted.userInfo?.userId || 0,
    nickname: wanted.userInfo?.nickname || '求购者',
    avatar: wanted.userInfo?.avatar || '',
    phone: wanted.userInfo?.phone || '138****8888',
    wechat: `wechat_${wanted.userInfo?.username || 'user'}`,
    showPhone: true,
    showWechat: true,
    dealCount: Math.floor(Math.random() * 20) + 1, // 随机生成交易次数
    goodRate: Math.floor(Math.random() * 10) + 90 // 随机生成好评率 90-99%
  }

  // 打开联系对话框
  contactDialogVisible.value = true
}

// 处理消息发送成功
const handleMessageSent = () => {
  ElMessage.success('消息已发送，请等待求购者回复')
}

onMounted(() => {
  // 这里可以加载实际的数据
  // loadHotProducts()
  // loadWantedItems()
})
</script>

<style lang="stylus" scoped>
.home-container
  width 100%
  padding 20px

.permission-demo-section
  margin-bottom 30px

  .demo-content
    p
      margin 0 0 15px
      font-size 16px

    .demo-instructions
      h4
        margin 15px 0 10px
        color #409eff
        font-size 16px

      ul, ol
        margin 10px 0
        padding-left 20px

        li
          margin-bottom 8px
          line-height 1.6

          strong
            color #303133

      ol
        li
          margin-bottom 5px

.banner-section
  margin-bottom 40px

  .banner-item
    height 300px
    background-size cover
    background-position center
    display flex
    align-items center
    justify-content center
    position relative

    &::before
      content ''
      position absolute
      top 0
      left 0
      right 0
      bottom 0
      background rgba(0, 0, 0, 0.3)

    .banner-content
      text-align center
      color white
      z-index 1

      h3
        font-size 32px
        margin-bottom 10px

      p
        font-size 18px

.section-header
  display flex
  justify-content space-between
  align-items center
  margin-bottom 20px

  h2
    font-size 24px
    color #333

.category-section
  margin-bottom 40px

  .category-grid
    display grid
    grid-template-columns repeat(auto-fit, minmax(150px, 1fr))
    gap 20px

    .category-item
      display flex
      flex-direction column
      align-items center
      padding 20px
      background white
      border-radius 8px
      cursor pointer
      transition all 0.3s ease
      box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)

      &:hover
        transform translateY(-2px)
        box-shadow 0 4px 16px rgba(0, 0, 0, 0.15)

      span
        margin-top 10px
        font-size 14px
        color #666

.products-section
  margin-bottom 40px

  .products-grid
    display grid
    grid-template-columns repeat(auto-fill, minmax(250px, 1fr))
    gap 20px

    .product-card
      background white
      border-radius 8px
      overflow hidden
      cursor pointer
      transition all 0.3s ease
      box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)

      &:hover
        transform translateY(-2px)
        box-shadow 0 4px 16px rgba(0, 0, 0, 0.15)

      .product-image
        height 200px
        overflow hidden

        .el-image
          width 100%
          height 100%

        .image-slot
          display flex
          align-items center
          justify-content center
          height 100%
          background #f5f7fa
          color #909399

      .product-info
        padding 15px

        .product-title
          font-size 16px
          color #333
          margin-bottom 8px
          overflow hidden
          text-overflow ellipsis
          white-space nowrap

        .product-price
          font-size 18px
          color #e74c3c
          font-weight bold
          margin-bottom 8px

        .product-meta
          display flex
          justify-content space-between
          font-size 12px
          color #999

          .product-campus
            color #409eff

.wanted-section
  .wanted-list
    .wanted-item
      display flex
      justify-content space-between
      align-items center
      padding 20px
      background white
      border-radius 8px
      margin-bottom 15px
      box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)

      .wanted-content
        flex 1

        h4
          font-size 16px
          color #333
          margin-bottom 8px

        p
          font-size 14px
          color #666
          margin-bottom 8px

        .wanted-meta
          display flex
          gap 15px
          font-size 12px
          color #999

          .wanted-price
            color #e74c3c
            font-weight bold

@media (max-width: 768px)
  .home-container
    padding 10px

  .banner-item
    height 200px

    .banner-content
      h3
        font-size 24px

      p
        font-size 16px

  .category-grid
    grid-template-columns repeat(3, 1fr)
    gap 10px

    .category-item
      padding 15px

  .products-grid
    grid-template-columns repeat(2, 1fr)
    gap 10px

  .wanted-item
    flex-direction column
    align-items flex-start
    gap 10px
</style>