<template>
  <div class="wanted-container">
    <div class="page-header">
      <h2>求购专区</h2>
      <el-button type="primary" @click="showPublishDialog = true">
        <el-icon><Plus /></el-icon>
        发布求购
      </el-button>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-bar">
      <el-select v-model="filters.category" placeholder="选择分类" clearable class="filter-select">
        <el-option
          v-for="(label, value) in PRODUCT_CATEGORY_LABELS"
          :key="value"
          :label="label"
          :value="value"
        />
      </el-select>

      <el-select v-model="filters.campus" placeholder="选择校区" clearable class="filter-select">
        <el-option
          v-for="(label, value) in CAMPUS_LABELS"
          :key="value"
          :label="label"
          :value="value"
        />
      </el-select>

      <el-select v-model="sortBy" placeholder="排序方式" class="filter-select">
        <el-option label="发布时间" value="createTime" />
        <el-option label="价格从低到高" value="price_asc" />
        <el-option label="价格从高到低" value="price_desc" />
      </el-select>
    </div>

    <!-- 求购列表 -->
    <div class="wanted-list" v-loading="loading">
      <div v-for="item in wantedItems" :key="item.wantedId" class="wanted-item">
        <div class="wanted-header">
          <div class="user-info">
            <el-avatar :src="item.userInfo?.avatar" :size="40">
              {{ item.userInfo?.nickname?.charAt(0) }}
            </el-avatar>
            <div class="user-details">
              <div class="user-name">{{ item.userInfo?.nickname }}</div>
              <div class="user-campus">{{ getCampusLabel(item.campus) }}</div>
            </div>
          </div>
          <div class="wanted-status">
            <el-tag :type="item.status === 'active' ? 'success' : 'info'">
              {{ item.status === 'active' ? '求购中' : '已完成' }}
            </el-tag>
          </div>
        </div>

        <div class="wanted-content">
          <h3 class="wanted-title">{{ item.title }}</h3>
          <p class="wanted-description">{{ item.description }}</p>
          
          <div class="wanted-details">
            <div class="detail-item">
              <span class="label">分类：</span>
              <span class="category-value">
                <CategoryIcon :category="item.category" :size="14" />
                {{ getCategoryLabel(item.category) }}
              </span>
            </div>
            <div class="detail-item">
              <span class="label">期望价格：</span>
              <span class="price">¥{{ item.expectedPrice }}</span>
            </div>
            <div class="detail-item">
              <span class="label">新旧要求：</span>
              <span>{{ getConditionLabel(item.condition) }}</span>
            </div>
          </div>
          
          <div class="wanted-tags" v-if="item.tags && item.tags.length > 0">
            <el-tag
              v-for="tag in item.tags"
              :key="tag"
              size="small"
              style="margin-right: 8px"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>

        <div class="wanted-footer">
          <div class="wanted-meta">
            <span class="publish-time">{{ formatTime(item.createTime) }}</span>
            <span class="response-count">{{ item.responseCount }} 人响应</span>
          </div>
          
          <div class="wanted-actions" v-if="item.status === 'active'">
            <el-button
              v-if="item.userId !== currentUserId"
              type="primary"
              size="small"
              @click="handleResponse(item)"
            >
              我有此商品
            </el-button>
            <el-button
              v-if="item.userId !== currentUserId"
              size="small"
              @click="handleContactWanted(item)"
            >
              联系TA
            </el-button>
            <el-button
              v-if="item.userId === currentUserId"
              size="small"
              @click="handleEdit(item)"
            >
              编辑
            </el-button>
            <el-button
              v-if="item.userId === currentUserId"
              size="small"
              type="danger"
              @click="handleDelete(item)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <el-empty v-if="!loading && wantedItems.length === 0" description="暂无求购信息">
      <el-button type="primary" @click="showPublishDialog = true">
        发布第一个求购
      </el-button>
    </el-empty>

    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 发布求购对话框 -->
    <el-dialog
      v-model="showPublishDialog"
      title="发布求购"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="publishForm"
        :rules="publishRules"
        label-width="100px"
      >
        <el-form-item label="求购标题" prop="title">
          <el-input
            v-model="publishForm.title"
            placeholder="请输入求购标题"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="商品分类" prop="category">
          <el-select v-model="publishForm.category" placeholder="请选择分类">
            <el-option
              v-for="(label, value) in PRODUCT_CATEGORY_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="期望价格" prop="expectedPrice">
          <el-input-number
            v-model="publishForm.expectedPrice"
            :min="0"
            :precision="2"
            placeholder="请输入期望价格"
          />
          <span style="margin-left: 10px">元</span>
        </el-form-item>
        
        <el-form-item label="新旧要求" prop="condition">
          <el-radio-group v-model="publishForm.condition">
            <el-radio
              v-for="(label, value) in PRODUCT_CONDITION_LABELS"
              :key="value"
              :label="value"
            >
              {{ label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="详细描述" prop="description">
          <el-input
            v-model="publishForm.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述您的需求"
            maxlength="300"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showPublishDialog = false">取消</el-button>
        <el-button type="primary" @click="handlePublish" :loading="publishLoading">
          发布
        </el-button>
      </template>
    </el-dialog>

    <!-- 响应求购对话框 -->
    <el-dialog v-model="responseDialogVisible" title="响应求购" width="600px">
      <div class="wanted-info">
        <h4>求购信息：{{ currentWantedItem.title }}</h4>
        <p>期望价格：¥{{ currentWantedItem.expectedPrice }}</p>
      </div>

      <el-form :model="responseForm" label-width="100px">
        <el-form-item label="商品标题" required>
          <el-input v-model="responseForm.productTitle" placeholder="请输入您的商品标题" />
        </el-form-item>
        <el-form-item label="商品描述">
          <el-input
            v-model="responseForm.productDescription"
            type="textarea"
            :rows="3"
            placeholder="请描述商品的具体情况"
          />
        </el-form-item>
        <el-form-item label="出售价格" required>
          <el-input-number
            v-model="responseForm.price"
            :min="0"
            :precision="2"
            placeholder="请输入价格"
          />
        </el-form-item>
        <el-form-item label="商品成色">
          <el-select v-model="responseForm.condition" placeholder="请选择成色">
            <el-option label="全新" value="new" />
            <el-option label="几乎全新" value="like_new" />
            <el-option label="轻微使用痕迹" value="good" />
            <el-option label="明显使用痕迹" value="fair" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系方式">
          <el-input
            v-model="responseForm.contactInfo"
            placeholder="请输入您的联系方式（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="responseDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitResponse">提交响应</el-button>
      </template>
    </el-dialog>

    <!-- 编辑求购对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑求购" width="600px">
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="求购标题" required>
          <el-input v-model="editForm.title" placeholder="请输入求购标题" />
        </el-form-item>
        <el-form-item label="商品分类" required>
          <el-select v-model="editForm.category" placeholder="请选择分类">
            <el-option
              v-for="(label, value) in PRODUCT_CATEGORY_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="期望价格">
          <el-input-number
            v-model="editForm.expectedPrice"
            :min="0"
            :precision="2"
            placeholder="请输入期望价格"
          />
        </el-form-item>
        <el-form-item label="校区">
          <el-select v-model="editForm.campus" placeholder="请选择校区">
            <el-option
              v-for="(label, value) in CAMPUS_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="详细描述">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述您的需求"
          />
        </el-form-item>
        <el-form-item label="联系方式">
          <el-input
            v-model="editForm.contactInfo"
            placeholder="请输入联系方式"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEdit">保存修改</el-button>
      </template>
    </el-dialog>

    <!-- 联系求购者对话框 -->
    <ContactSellerDialog
      v-model="contactDialogVisible"
      :seller-info="currentWantedUser"
      title="联系求购者"
      @message-sent="handleMessageSent"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores'
import { formatTime } from '@/utils'
import {
  PRODUCT_CATEGORY_LABELS,
  PRODUCT_CONDITION_LABELS,
  CAMPUS_LABELS
} from '@/constants'
import CategoryIcon from '@/components/common/CategoryIcon.vue'
import ContactSellerDialog from '@/components/common/ContactSellerDialog.vue'
import type { WantedItem } from '@/types'
import type { FormInstance } from 'element-plus'

const userStore = useUserStore()

const loading = ref(false)
const publishLoading = ref(false)
const wantedItems = ref<WantedItem[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const sortBy = ref('createTime')
const showPublishDialog = ref(false)
const formRef = ref<FormInstance>()

const currentUserId = computed(() => userStore.userInfo?.userId)

// 联系对话框相关
const contactDialogVisible = ref(false)
const currentWantedUser = ref({
  userId: 0,
  nickname: '',
  avatar: '',
  phone: '138****8888',
  wechat: 'user_wechat',
  showPhone: true,
  showWechat: true,
  dealCount: 5,
  goodRate: 95
})

// 筛选条件
const filters = reactive({
  category: '',
  campus: ''
})

// 发布表单
const publishForm = reactive({
  title: '',
  category: '',
  expectedPrice: 0,
  condition: '',
  description: ''
})

// 发布表单验证规则
const publishRules = {
  title: [
    { required: true, message: '请输入求购标题', trigger: 'blur' },
    { min: 2, max: 50, message: '标题长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择商品分类', trigger: 'change' }
  ],
  expectedPrice: [
    { required: true, message: '请输入期望价格', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '价格必须大于0', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入详细描述', trigger: 'blur' },
    { min: 10, max: 300, message: '描述长度在 10 到 300 个字符', trigger: 'blur' }
  ]
}

// 获取求购列表
const fetchWantedItems = async () => {
  loading.value = true
  try {
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    wantedItems.value = [
      {
        wantedId: 1,
        userId: 2,
        userInfo: {
          userId: 2,
          username: 'student2',
          nickname: '小红',
          avatar: '',
          email: '',
          phone: '',
          sex: '1',
          status: '',
          roleKey: 'student'
        },
        title: '求购线性代数教材',
        description: '需要一本线性代数教材，最好是同济版本的，八成新以上即可。',
        category: 'textbook',
        expectedPrice: 20,
        condition: 'good',
        campus: 'main',
        status: 'active',
        tags: ['教材', '线性代数'],
        responseCount: 3,
        createTime: '2024-01-16T10:00:00'
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('获取求购列表失败')
  } finally {
    loading.value = false
  }
}

// 工具函数
const getCategoryLabel = (category: string) => {
  return PRODUCT_CATEGORY_LABELS[category as keyof typeof PRODUCT_CATEGORY_LABELS] || category
}

const getConditionLabel = (condition: string) => {
  return PRODUCT_CONDITION_LABELS[condition as keyof typeof PRODUCT_CONDITION_LABELS] || condition
}

const getCampusLabel = (campus: string) => {
  return CAMPUS_LABELS[campus as keyof typeof CAMPUS_LABELS] || campus
}

// 操作方法
const responseDialogVisible = ref(false)
const currentWantedItem = ref<WantedItem>({} as WantedItem)
const responseForm = reactive({
  productTitle: '',
  productDescription: '',
  price: 0,
  condition: '',
  images: [] as any[],
  contactInfo: ''
})

const handleResponse = (item: WantedItem) => {
  currentWantedItem.value = item
  responseDialogVisible.value = true
  // 预填充一些信息
  responseForm.productTitle = `回应：${item.title}`
  responseForm.price = item.expectedPrice || 0
}

const submitResponse = async () => {
  if (!responseForm.productTitle.trim()) {
    ElMessage.warning('请输入商品标题')
    return
  }
  if (!responseForm.price || responseForm.price <= 0) {
    ElMessage.warning('请输入有效价格')
    return
  }

  try {
    // 模拟提交响应
    await new Promise(resolve => setTimeout(resolve, 1500))

    ElMessage.success('响应提交成功，买家将收到通知')
    responseDialogVisible.value = false
    // 重置表单
    Object.assign(responseForm, {
      productTitle: '',
      productDescription: '',
      price: 0,
      condition: '',
      images: [],
      contactInfo: ''
    })
  } catch (error) {
    ElMessage.error('响应提交失败，请重试')
  }
}

const editDialogVisible = ref(false)
const editForm = reactive({
  title: '',
  description: '',
  category: '',
  expectedPrice: 0,
  campus: '',
  contactInfo: ''
})

const handleEdit = (item: WantedItem) => {
  currentWantedItem.value = item
  editDialogVisible.value = true
  // 填充当前数据
  Object.assign(editForm, {
    title: item.title,
    description: item.description,
    category: item.category,
    expectedPrice: item.expectedPrice,
    campus: item.campus,
    contactInfo: item.contactInfo
  })
}

const submitEdit = async () => {
  if (!editForm.title.trim()) {
    ElMessage.warning('请输入求购标题')
    return
  }
  if (!editForm.category) {
    ElMessage.warning('请选择商品分类')
    return
  }

  try {
    // 模拟更新求购
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 更新列表中的数据
    const index = wantedItems.value.findIndex(item => item.wantedId === currentWantedItem.value.wantedId)
    if (index !== -1) {
      Object.assign(wantedItems.value[index], editForm)
    }

    ElMessage.success('求购信息更新成功')
    editDialogVisible.value = false
  } catch (error) {
    ElMessage.error('更新失败，请重试')
  }
}

const handleDelete = async (item: WantedItem) => {
  try {
    await ElMessageBox.confirm('确定要删除这个求购信息吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
    fetchWantedItems()
  } catch {
    // 用户取消
  }
}

// 发布求购
const handlePublish = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      publishLoading.value = true
      try {
        // 这里应该调用API发布求购
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        ElMessage.success('发布成功')
        showPublishDialog.value = false
        resetForm()
        fetchWantedItems()
      } catch (error) {
        ElMessage.error('发布失败，请稍后重试')
      } finally {
        publishLoading.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  Object.assign(publishForm, {
    title: '',
    category: '',
    expectedPrice: 0,
    condition: '',
    description: ''
  })
  formRef.value?.resetFields()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchWantedItems()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchWantedItems()
}

// 联系求购者
const handleContactWanted = (wanted: WantedItem) => {
  // 检查用户是否登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再联系求购者')
    return
  }

  // 设置当前求购者信息
  currentWantedUser.value = {
    userId: wanted.userInfo?.userId || 0,
    nickname: wanted.userInfo?.nickname || '求购者',
    avatar: wanted.userInfo?.avatar || '',
    phone: wanted.userInfo?.phone || '138****8888',
    wechat: `wechat_${wanted.userInfo?.username || 'user'}`,
    showPhone: true,
    showWechat: true,
    dealCount: Math.floor(Math.random() * 20) + 1,
    goodRate: Math.floor(Math.random() * 10) + 90
  }

  // 打开联系对话框
  contactDialogVisible.value = true
}

// 处理消息发送成功
const handleMessageSent = () => {
  ElMessage.success('消息已发送，请等待求购者回复')
}

onMounted(() => {
  fetchWantedItems()
})
</script>

<style lang="stylus" scoped>
.wanted-container
  width 100%
  padding 20px

.page-header
  display flex
  justify-content space-between
  align-items center
  margin-bottom 20px
  
  h2
    color #333

.filter-bar
  display flex
  gap 20px
  margin-bottom 20px
  padding 20px
  background white
  border-radius 8px
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)

  .filter-select
    width 160px

.wanted-list
  .wanted-item
    background white
    border-radius 8px
    box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
    margin-bottom 20px
    overflow hidden
    
    .wanted-header
      display flex
      justify-content space-between
      align-items center
      padding 20px
      border-bottom 1px solid #f0f0f0
      
      .user-info
        display flex
        align-items center
        gap 12px
        
        .user-details
          .user-name
            font-weight bold
            color #333
            margin-bottom 4px
            
          .user-campus
            color #666
            font-size 12px
            
    .wanted-content
      padding 20px
      
      .wanted-title
        font-size 18px
        color #333
        margin-bottom 10px
        
      .wanted-description
        color #666
        line-height 1.6
        margin-bottom 15px
        
      .wanted-details
        display grid
        grid-template-columns repeat(auto-fit, minmax(200px, 1fr))
        gap 10px
        margin-bottom 15px
        
        .detail-item
          display flex

          .label
            width 80px
            color #999

          .category-value
            display flex
            align-items center
            gap 4px
            color #409eff
            
          .price
            color #e74c3c
            font-weight bold
            
      .wanted-tags
        margin-top 10px
        
    .wanted-footer
      display flex
      justify-content space-between
      align-items center
      padding 15px 20px
      background #f8f9fa
      border-top 1px solid #f0f0f0
      
      .wanted-meta
        display flex
        gap 20px
        font-size 12px
        color #999
        
      .wanted-actions
        display flex
        gap 8px

.pagination-container
  display flex
  justify-content center
  margin-top 30px

@media (max-width: 768px)
  .wanted-container
    padding 10px
    
  .page-header
    flex-direction column
    gap 15px
    align-items stretch
    
  .filter-bar
    flex-wrap wrap

    .filter-select
      width 140px
    
  .wanted-footer
    flex-direction column
    gap 15px
    align-items stretch
    
    .wanted-meta
      justify-content center
</style>
