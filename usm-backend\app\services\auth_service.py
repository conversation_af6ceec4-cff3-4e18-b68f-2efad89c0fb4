from datetime import datetime
from typing import Optional, Dict, Any, List
from fastapi import HTTPException, status

from app.core.security import create_access_token
from app.schemas.auth import LoginRequest, RegisterRequest, UserInfo, RoleInfo
from app.crud.user import user_crud, role_crud
from app.models.user import User

class AuthService:
    def __init__(self):
        pass

    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """验证用户"""
        user = await user_crud.get_user_by_username(username)
        if not user:
            return None

        # 明文密码比较
        if password != user.password:
            return None

        return user

    async def login(self, login_data: LoginRequest) -> Dict[str, str]:
        """用户登录"""
        # 验证用户
        user = await self.authenticate_user(login_data.username, login_data.password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )

        # 更新最后登录时间
        await user_crud.update_last_login_time(user.user_id)

        # 生成token
        access_token = create_access_token(data={"sub": user.username})

        return {"token": access_token}

    async def register(self, register_data: RegisterRequest) -> Dict[str, str]:
        """用户注册"""
        # 检查用户名是否已存在
        if await user_crud.get_user_by_username(register_data.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )

        # 检查邮箱是否已存在
        if await user_crud.get_user_by_email(register_data.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册"
            )

        # 检查手机号是否已存在
        if await user_crud.get_user_by_phone(register_data.phone):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="手机号已被注册"
            )

        # 创建新用户
        try:
            await user_crud.create_user(register_data)
            return {"message": "注册成功"}
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"注册失败: {str(e)}"
            )

    async def get_user_info(self, username: str) -> UserInfo:
        """获取用户信息"""
        user = await user_crud.get_user_by_username(username)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        return UserInfo(
            userId=user.user_id,
            username=user.username,
            nickname=user.nickname,
            avatar=user.avatar,
            email=user.email,
            phone=user.phone,
            sex=str(user.sex),
            status=user.status,
            roleKey=user.role_key,
            campus=user.campus,
            studentId=user.student_id,
            realName=user.real_name,
            isVerified=user.is_verified,
            createTime=user.create_time,
            updateTime=user.update_time
        )

    async def get_roles(self) -> List[RoleInfo]:
        """获取角色列表"""
        roles = await role_crud.get_roles()
        return [RoleInfo(
            roleId=role.role_id,
            roleName=role.role_name,
            roleKey=role.role_key
        ) for role in roles]

# 创建服务实例
auth_service = AuthService()
