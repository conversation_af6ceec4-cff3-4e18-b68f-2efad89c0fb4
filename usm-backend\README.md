# 校园二手交易平台后端 API

基于 FastAPI 开发的校园二手交易平台后端服务。

## 功能特性

- ✅ 用户认证（登录/注册/登出）
- ✅ JWT Token 认证
- ✅ 验证码生成
- ✅ 角色权限管理
- ✅ 统一响应格式
- ✅ CORS 跨域支持
- 🚧 商品管理（开发中）
- 🚧 订单管理（开发中）
- 🚧 消息系统（开发中）

## 技术栈

- **FastAPI**: 现代化的 Python Web 框架
- **Pydantic**: 数据验证和序列化
- **JWT**: JSON Web Token 认证
- **Pillow**: 图像处理（验证码生成）
- **Uvicorn**: ASGI 服务器

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，修改相关配置
```

### 3. 启动服务

```bash
# 方式1：使用启动脚本
python run.py

# 方式2：直接使用 uvicorn
uvicorn main:app --host localhost --port 18080 --reload
```

### 4. 访问文档

- API 文档: http://localhost:18080/docs
- ReDoc 文档: http://localhost:18080/redoc

## API 接口

### 认证相关

- `GET /api/captcha` - 获取验证码
- `POST /api/login` - 用户登录
- `POST /api/register` - 用户注册
- `POST /api/logout` - 用户登出
- `GET /api/user/getInfo` - 获取用户信息
- `GET /api/roleList` - 获取角色列表

## 测试账号

### 学生账号
- 用户名: `student`
- 密码: `123456`
- 角色: 学生

### 管理员账号
- 用户名: `admin`
- 密码: `admin123`
- 角色: 管理员

## 项目结构

```
usm-backend/
├── app/
│   ├── core/           # 核心配置
│   │   ├── config.py   # 应用配置
│   │   └── security.py # 安全相关
│   ├── routers/        # 路由
│   │   └── auth.py     # 认证路由
│   ├── schemas/        # 数据模型
│   │   ├── common.py   # 通用模型
│   │   └── auth.py     # 认证模型
│   └── services/       # 业务逻辑
│       └── auth_service.py # 认证服务
├── main.py            # 应用入口
├── run.py             # 启动脚本
├── requirements.txt   # 依赖包
└── README.md         # 说明文档
```

## 开发说明

当前版本使用模拟数据，后续将集成真实数据库。

### 模拟数据

- 用户数据存储在内存中
- 验证码临时存储
- 支持基本的用户认证流程

### 下一步计划

1. 集成数据库（SQLAlchemy + SQLite/PostgreSQL）
2. 实现商品管理 API
3. 实现订单管理 API
4. 实现文件上传功能
5. 添加单元测试
6. 部署配置
