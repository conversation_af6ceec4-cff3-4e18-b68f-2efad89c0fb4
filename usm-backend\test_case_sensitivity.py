#!/usr/bin/env python3
"""
测试用户名验证的大小写敏感性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.services.auth_service import auth_service
from app.schemas.auth import LoginRequest

def test_case_sensitivity():
    """测试大小写敏感性"""
    db = SessionLocal()
    try:
        print("🔍 测试用户名验证的大小写敏感性...")
        
        # 测试用例
        test_cases = [
            ("admin", "123456", "正确的用户名和密码"),
            ("Admin", "123456", "大写A的用户名"),
            ("ADMIN", "123456", "全大写用户名"),
            ("aDmIn", "123456", "混合大小写用户名"),
            ("admin", "123456", "正确密码"),
            ("admin", "123456", "正确密码（再次测试）"),
        ]
        
        for username, password, description in test_cases:
            print(f"\n📝 测试: {description}")
            print(f"   用户名: '{username}'")
            print(f"   密码: '{password}'")
            
            try:
                login_data = LoginRequest(username=username, password=password)
                result = auth_service.login(db, login_data)
                print(f"   ✅ 登录成功: {result}")
            except Exception as e:
                print(f"   ❌ 登录失败: {str(e)}")
        
        print("\n" + "="*50)
        print("🔍 直接测试数据库查询的大小写敏感性...")
        
        from app.crud.user import user_crud
        
        # 测试数据库查询
        db_test_cases = [
            "admin",
            "Admin", 
            "ADMIN",
            "aDmIn"
        ]
        
        for test_username in db_test_cases:
            user = user_crud.get_user_by_username(db, test_username)
            if user:
                print(f"   ✅ 找到用户: '{test_username}' -> {user.username}")
            else:
                print(f"   ❌ 未找到用户: '{test_username}'")
        
        print("\n" + "="*50)
        print("📊 结论分析:")
        print("1. 如果只有 'admin' 能登录成功，说明用户名区分大小写")
        print("2. 如果 'Admin', 'ADMIN' 等也能登录，说明用户名不区分大小写")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_case_sensitivity()
