<template>
  <div class="order-list-container">
    <div class="order-tabs">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="全部订单" name="all" />
        <el-tab-pane label="待付款" name="pending_payment" />
        <el-tab-pane label="待取货" name="pending_pickup" />
        <el-tab-pane label="待确认" name="pending_confirm" />
        <el-tab-pane label="已完成" name="completed" />
        <el-tab-pane label="已取消" name="cancelled" />
      </el-tabs>
    </div>

    <div class="order-list" v-loading="loading">
      <div v-for="order in orders" :key="order.orderId" class="order-item">
        <div class="order-header">
          <div class="order-info">
            <span class="order-id">订单号：{{ order.orderId }}</span>
            <span class="order-time">{{ formatTime(order.createTime) }}</span>
          </div>
          <div class="order-status">
            <el-tag :type="getStatusType(order.status)">
              {{ getStatusLabel(order.status) }}
            </el-tag>
          </div>
        </div>

        <div class="order-content">
          <div class="product-info" @click="goToProduct(order.productId)">
            <el-image
              :src="order.productInfo?.images[0]"
              fit="cover"
              class="product-image"
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
            <div class="product-details">
              <h4 class="product-title">{{ order.productInfo?.title }}</h4>
              <p class="product-price">¥{{ order.price }}</p>
              <p class="product-condition">
                {{ getConditionLabel(order.productInfo?.condition) }}
              </p>
            </div>
          </div>

          <div class="seller-info" v-if="order.sellerInfo">
            <div class="seller-avatar">
              <el-avatar :src="order.sellerInfo.avatar" :size="32">
                {{ order.sellerInfo.nickname?.charAt(0) }}
              </el-avatar>
            </div>
            <div class="seller-details">
              <div class="seller-name">{{ order.sellerInfo.nickname }}</div>
              <div class="seller-campus">{{ getCampusLabel(order.sellerInfo.campus) }}</div>
            </div>
          </div>
        </div>

        <div class="order-actions">
          <!-- 待付款状态 -->
          <template v-if="order.status === 'pending_payment'">
            <el-button type="primary" @click="handlePay(order)">
              立即付款
            </el-button>
            <el-button @click="handleCancel(order)">
              取消订单
            </el-button>
          </template>

          <!-- 待取货状态 -->
          <template v-if="order.status === 'pending_pickup'">
            <el-button @click="handleContact(order.sellerInfo)">
              联系卖家
            </el-button>
            <el-button type="primary" @click="handleConfirmPickup(order)">
              确认取货
            </el-button>
          </template>

          <!-- 待确认状态 -->
          <template v-if="order.status === 'pending_confirm'">
            <el-button type="primary" @click="handleConfirmOrder(order)">
              确认收货
            </el-button>
          </template>

          <!-- 已完成状态 -->
          <template v-if="order.status === 'completed'">
            <el-button @click="handleComment(order)">
              评价
            </el-button>
            <el-button @click="handleBuyAgain(order)">
              再次购买
            </el-button>
          </template>

          <!-- 通用操作 -->
          <el-button @click="handleViewDetail(order)">
            查看详情
          </el-button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <el-empty v-if="!loading && orders.length === 0" description="暂无订单" />

    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 评价对话框 -->
    <ReviewDialog
      v-model="reviewDialogVisible"
      :order-info="{
        orderId: currentOrder.orderId,
        productTitle: currentOrder.productInfo?.title || '',
        productImage: currentOrder.productInfo?.images?.[0] || '/src/assets/background.jpg',
        totalAmount: currentOrder.totalAmount || 0
      }"
      @review-submitted="handleReviewSubmitted"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import { formatTime } from '@/utils'
import { 
  ORDER_STATUS_LABELS, 
  PRODUCT_CONDITION_LABELS, 
  CAMPUS_LABELS 
} from '@/constants'
import type { Order } from '@/types'
import ReviewDialog from '@/components/common/ReviewDialog.vue'

const router = useRouter()

const loading = ref(false)
const orders = ref<Order[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const activeTab = ref('all')

// 获取订单列表
const fetchOrders = async () => {
  loading.value = true
  try {
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    orders.value = [
      {
        orderId: 1001,
        productId: 1,
        productInfo: {
          productId: 1,
          title: '高等数学教材（第七版）',
          description: '',
          category: 'textbook',
          price: 25,
          condition: 'good',
          images: ['/src/assets/background.jpg'],
          sellerId: 1,
          campus: 'main',
          status: 'published',
          viewCount: 0,
          likeCount: 0,
          createTime: '',
          updateTime: ''
        },
        buyerId: 2,
        sellerId: 1,
        sellerInfo: {
          userId: 1,
          username: 'student1',
          nickname: '小明',
          avatar: '',
          email: '',
          phone: '',
          sex: '0',
          status: '',
          roleKey: 'student',
          campus: 'main'
        },
        price: 25,
        status: 'pending_payment',
        createTime: '2024-01-15T10:30:00',
        updateTime: '2024-01-15T10:30:00'
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 工具函数
const getStatusLabel = (status: string) => {
  return ORDER_STATUS_LABELS[status as keyof typeof ORDER_STATUS_LABELS] || status
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending_payment: 'warning',
    pending_pickup: 'info',
    pending_confirm: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return typeMap[status] || 'default'
}

const getConditionLabel = (condition?: string) => {
  if (!condition) return ''
  return PRODUCT_CONDITION_LABELS[condition as keyof typeof PRODUCT_CONDITION_LABELS] || condition
}

const getCampusLabel = (campus?: string) => {
  if (!campus) return ''
  return CAMPUS_LABELS[campus as keyof typeof CAMPUS_LABELS] || campus
}

// 导航方法
const goToProduct = (productId: number) => {
  router.push({ name: 'ProductDetail', params: { id: productId } })
}

// 订单操作
const handlePay = (order: Order) => {
  router.push({ name: 'OrderPayment', params: { id: order.orderId } })
}

const handleCancel = async (order: Order) => {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('订单已取消')
    fetchOrders()
  } catch {
    // 用户取消
  }
}

const handleContact = (seller: any) => {
  ElMessage.info('联系卖家功能开发中...')
}

const handleConfirmPickup = (order: Order) => {
  ElMessage.success('已确认取货')
  fetchOrders()
}

const handleConfirmOrder = (order: Order) => {
  ElMessage.success('已确认收货')
  fetchOrders()
}

const reviewDialogVisible = ref(false)
const currentOrder = ref<Order>({} as Order)

const handleComment = (order: Order) => {
  currentOrder.value = order
  reviewDialogVisible.value = true
}

const handleReviewSubmitted = () => {
  ElMessage.success('评价提交成功')
  // 刷新订单列表
  fetchOrders()
}

const handleBuyAgain = (order: Order) => {
  goToProduct(order.productId)
}

const handleViewDetail = (order: Order) => {
  router.push({ name: 'OrderDetail', params: { id: order.orderId } })
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  currentPage.value = 1
  fetchOrders()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchOrders()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchOrders()
}

onMounted(() => {
  fetchOrders()
})
</script>

<style lang="stylus" scoped>
.order-list-container
  width 100%
  padding 20px

.order-tabs
  background white
  border-radius 8px
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
  margin-bottom 20px

.order-list
  .order-item
    background white
    border-radius 8px
    box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
    margin-bottom 20px
    overflow hidden
    
    .order-header
      display flex
      justify-content space-between
      align-items center
      padding 15px 20px
      background #f8f9fa
      border-bottom 1px solid #e9ecef
      
      .order-info
        display flex
        gap 20px
        
        .order-id
          font-weight bold
          color #333
          
        .order-time
          color #666
          font-size 14px
          
    .order-content
      display flex
      padding 20px
      gap 20px
      
      .product-info
        flex 1
        display flex
        gap 15px
        cursor pointer
        
        .product-image
          width 80px
          height 80px
          border-radius 4px
          overflow hidden
          
          .image-slot
            display flex
            align-items center
            justify-content center
            height 100%
            background #f5f7fa
            color #909399
            
        .product-details
          flex 1
          
          .product-title
            font-size 16px
            color #333
            margin-bottom 8px
            
          .product-price
            font-size 18px
            color #e74c3c
            font-weight bold
            margin-bottom 5px
            
          .product-condition
            color #666
            font-size 14px
            
      .seller-info
        display flex
        align-items center
        gap 10px
        min-width 150px
        
        .seller-details
          .seller-name
            font-weight bold
            margin-bottom 4px
            
          .seller-campus
            color #666
            font-size 12px
            
    .order-actions
      display flex
      justify-content flex-end
      gap 10px
      padding 15px 20px
      background #f8f9fa
      border-top 1px solid #e9ecef

.pagination-container
  display flex
  justify-content center
  margin-top 30px

@media (max-width: 768px)
  .order-list-container
    padding 10px
    
  .order-content
    flex-direction column
    gap 15px
    
  .seller-info
    min-width auto
    
  .order-actions
    flex-wrap wrap
    gap 8px
</style>
