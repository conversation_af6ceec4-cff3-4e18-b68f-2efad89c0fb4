<template>
  <span :class="iconClass" :style="{ fontSize: size + 'px' }">
    <el-icon :size="size">
      <Document v-if="iconName === 'Document'" />
      <Monitor v-else-if="iconName === 'Monitor'" />
      <User v-else-if="iconName === 'User'" />
      <Coffee v-else-if="iconName === 'Coffee'" />
      <Star v-else-if="iconName === 'Star'" />
      <Brush v-else-if="iconName === 'Brush'" />
      <Apple v-else-if="iconName === 'Apple'" />
      <MoreFilled v-else />
    </el-icon>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Document,
  Monitor,
  User,
  Coffee,
  Star,
  Brush,
  Apple,
  MoreFilled
} from '@element-plus/icons-vue'
import { PRODUCT_CATEGORY_ICONS } from '@/constants'

interface Props {
  category: string
  size?: number | string
  color?: string
  showBackground?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 16,
  color: '',
  showBackground: false
})

// 计算图标名称
const iconName = computed(() => {
  const name = PRODUCT_CATEGORY_ICONS[props.category as keyof typeof PRODUCT_CATEGORY_ICONS]
  return name || 'MoreFilled'
})

// 计算图标样式类
const iconClass = computed(() => {
  const classes = ['category-icon']
  
  if (props.showBackground) {
    classes.push('with-background')
  }
  
  if (props.color) {
    classes.push(`color-${props.color}`)
  }
  
  return classes.join(' ')
})
</script>

<style lang="stylus" scoped>
.category-icon
  display inline-flex
  align-items center
  justify-content center
  
  &.with-background
    width 32px
    height 32px
    border-radius 6px
    background #f5f7fa
    
    &.color-primary
      background #e1f3ff
      color #409eff
      
    &.color-success
      background #e8f5e8
      color #67c23a
      
    &.color-warning
      background #fdf6ec
      color #e6a23c
      
    &.color-danger
      background #fef0f0
      color #f56c6c
      
    &.color-info
      background #f4f4f5
      color #909399
</style>
