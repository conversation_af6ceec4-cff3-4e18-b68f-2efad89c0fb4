import {createRouter, createWebHistory} from 'vue-router';
import { useUserStore } from '@/stores/user';

const routes = [
    {
        path: '/',
        name: 'Home',
        component: () => import('@/views/Home.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/login',
        name: 'TheLogin',
        component: () => import('@/views/login/TheLogin.vue'),
        meta: { requiresAuth: false }
    },
    {
        path: '/register',
        name: 'TheRegister',
        component: () => import('@/views/login/TheRegister.vue'),
        meta: { requiresAuth: false }
    },
    {
        path: '/products',
        name: 'ProductList',
        component: () => import('@/views/product/ProductList.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/product/:id',
        name: 'ProductDetail',
        component: () => import('@/views/product/ProductDetail.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/publish',
        name: 'ProductPublish',
        component: () => import('@/views/product/ProductPublish.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/product/:id/edit',
        name: 'ProductEdit',
        component: () => import('@/views/product/ProductEdit.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/profile',
        name: 'UserProfile',
        component: () => import('@/views/user/UserProfile.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/orders',
        name: 'OrderList',
        component: () => import('@/views/order/OrderList.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/order/:id/payment',
        name: 'OrderPayment',
        component: () => import('@/views/order/OrderPayment.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/my-products',
        name: 'MyProducts',
        component: () => import('@/views/product/MyProducts.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/favorites',
        name: 'Favorites',
        component: () => import('@/views/user/Favorites.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/messages',
        name: 'Messages',
        component: () => import('@/views/message/MessageList.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/wanted',
        name: 'WantedList',
        component: () => import('@/views/wanted/WantedList.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/order/create',
        name: 'OrderCreate',
        component: () => import('@/views/order/OrderCreate.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/order/:id',
        name: 'OrderDetail',
        component: () => import('@/views/order/OrderDetail.vue'),
        meta: { requiresAuth: true }
    },

    {
        path: '/admin',
        name: 'AdminDashboard',
        component: () => import('@/views/admin/Dashboard.vue'),
        meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
        path: '/admin/users',
        name: 'AdminUsers',
        component: () => import('@/views/admin/UserManagement.vue'),
        meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
        path: '/admin/products',
        name: 'AdminProducts',
        component: () => import('@/views/admin/ProductManagement.vue'),
        meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
        path: '/admin/orders',
        name: 'AdminOrders',
        component: () => import('@/views/admin/OrderManagement.vue'),
        meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
        path: '/admin/settings',
        name: 'PersonalSettings',
        component: () => import('@/views/admin/SystemSettings.vue'),
        meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
        path: '/test/icons',
        name: 'IconTest',
        component: () => import('@/views/test/IconTest.vue')
    },
    {
        path: '/test/contact',
        name: 'ContactTest',
        component: () => import('@/views/test/ContactTest.vue')
    }
];

const router = createRouter({
    history: createWebHistory(),
    routes,
});

// 路由守卫
router.beforeEach((to, from, next) => {
    const userStore = useUserStore();

    // 检查是否需要登录
    if (to.meta.requiresAuth && !userStore.isLoggedIn) {
        next('/login');
        return;
    }

    // 检查是否需要管理员权限
    if (to.meta.requiresAdmin && !userStore.isAdmin) {
        next('/');
        return;
    }

    // 已登录用户访问登录/注册页面，重定向到首页
    if ((to.name === 'TheLogin' || to.name === 'TheRegister') && userStore.isLoggedIn) {
        next('/');
        return;
    }

    next();
});

export default router;