#!/usr/bin/env python3
"""
修复数据库中的密码哈希
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.user import User
from app.core.security import get_password_hash, verify_password

def fix_passwords():
    """修复数据库中的密码"""
    db = SessionLocal()
    try:
        print("🔍 检查数据库中的用户密码...")
        
        # 查询所有用户
        users = db.query(User).all()
        print(f"找到 {len(users)} 个用户")
        
        for user in users:
            print(f"\n用户: {user.username}")
            print(f"当前密码哈希: {user.password}")
            
            # 测试当前密码是否能验证123456
            test_password = "123456"
            is_valid = verify_password(test_password, user.password)
            print(f"密码验证结果: {is_valid}")
            
            if not is_valid:
                print("❌ 密码验证失败，正在修复...")
                # 生成正确的密码哈希
                correct_hash = get_password_hash(test_password)
                print(f"新的密码哈希: {correct_hash}")
                
                # 更新数据库
                user.password = correct_hash
                db.commit()
                
                # 再次验证
                new_valid = verify_password(test_password, correct_hash)
                print(f"修复后验证结果: {new_valid}")
                
                if new_valid:
                    print("✅ 密码修复成功!")
                else:
                    print("❌ 密码修复失败!")
            else:
                print("✅ 密码验证正常")
        
        print("\n🎉 密码检查和修复完成!")
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    fix_passwords()
