<template>
  <div class="icon-test">
    <h2>图标测试页面</h2>
    
    <div class="test-section">
      <h3>直接使用Element Plus图标</h3>
      <div class="icon-row">
        <div class="icon-item">
          <el-icon :size="24"><Document /></el-icon>
          <span>Document</span>
        </div>
        <div class="icon-item">
          <el-icon :size="24"><Monitor /></el-icon>
          <span>Monitor</span>
        </div>
        <div class="icon-item">
          <el-icon :size="24"><User /></el-icon>
          <span>User</span>
        </div>
        <div class="icon-item">
          <el-icon :size="24"><Coffee /></el-icon>
          <span>Coffee</span>
        </div>
        <div class="icon-item">
          <el-icon :size="24"><Star /></el-icon>
          <span>Star</span>
        </div>
        <div class="icon-item">
          <el-icon :size="24"><Brush /></el-icon>
          <span>Brush</span>
        </div>
        <div class="icon-item">
          <el-icon :size="24"><Apple /></el-icon>
          <span>Apple</span>
        </div>
        <div class="icon-item">
          <el-icon :size="24"><MoreFilled /></el-icon>
          <span>MoreFilled</span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>使用CategoryIcon组件</h3>
      <div class="icon-row">
        <div class="icon-item">
          <CategoryIcon category="textbook" :size="24" />
          <span>教材书籍</span>
        </div>
        <div class="icon-item">
          <CategoryIcon category="digital" :size="24" />
          <span>数码电子</span>
        </div>
        <div class="icon-item">
          <CategoryIcon category="clothing" :size="24" />
          <span>服装配饰</span>
        </div>
        <div class="icon-item">
          <CategoryIcon category="daily" :size="24" />
          <span>生活用品</span>
        </div>
        <div class="icon-item">
          <CategoryIcon category="sports" :size="24" />
          <span>运动器材</span>
        </div>
        <div class="icon-item">
          <CategoryIcon category="beauty" :size="24" />
          <span>美妆护肤</span>
        </div>
        <div class="icon-item">
          <CategoryIcon category="food" :size="24" />
          <span>食品零食</span>
        </div>
        <div class="icon-item">
          <CategoryIcon category="other" :size="24" />
          <span>其他</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  Document, 
  Monitor, 
  User, 
  Coffee, 
  Star, 
  Brush, 
  Apple, 
  MoreFilled 
} from '@element-plus/icons-vue'
import CategoryIcon from '@/components/common/CategoryIcon.vue'
</script>

<style lang="stylus" scoped>
.icon-test
  padding 20px
  
.test-section
  margin-bottom 40px
  
  h3
    margin-bottom 20px
    color #333
    
.icon-row
  display flex
  gap 20px
  flex-wrap wrap
  
.icon-item
  display flex
  flex-direction column
  align-items center
  gap 8px
  padding 10px
  border 1px solid #eee
  border-radius 6px
  min-width 80px
  
  span
    font-size 12px
    color #666
    text-align center
</style>
