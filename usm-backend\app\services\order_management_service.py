from typing import Dict, Any, List
from datetime import datetime, timedelta
from decimal import Decimal
from tortoise.functions import Count, Sum
from tortoise.expressions import Q

from app.crud.order_management import order_management_crud
from app.models.order import Order
from app.schemas.order_management import (
    OrderListParams, OrderInfo, OrderStatistics, 
    BatchOrderOperationRequest, BatchOperationResult
)

class OrderManagementService:
    
    async def get_orders_list(self, params: OrderListParams) -> Dict[str, Any]:
        """获取订单列表"""
        return await order_management_crud.get_orders_list(params)
    
    async def get_order_detail(self, order_id: int) -> OrderInfo:
        """获取订单详情"""
        order_info = await order_management_crud.get_order_by_id(order_id)
        if not order_info:
            raise ValueError("订单不存在")
        return order_info
    
    async def update_order_status(self, order_id: int, status: str, remark: str = None) -> OrderInfo:
        """更新订单状态"""
        order_info = await order_management_crud.update_order_status(order_id, status, remark)
        if not order_info:
            raise ValueError("订单不存在")
        return order_info
    
    async def delete_order(self, order_id: int) -> bool:
        """删除订单"""
        success = await order_management_crud.delete_order(order_id)
        if not success:
            raise ValueError("订单不存在")
        return True
    
    async def get_order_statistics(self) -> OrderStatistics:
        """获取订单统计信息"""
        # 获取总订单数
        total_orders = await Order.all().count()
        
        # 获取各状态订单数
        status_counts = await Order.all().group_by('status').annotate(count=Count('order_id')).values('status', 'count')
        status_stats = {item['status']: item['count'] for item in status_counts}
        
        pending_payment = status_stats.get('pending_payment', 0)
        pending_pickup = status_stats.get('pending_pickup', 0)
        pending_confirm = status_stats.get('pending_confirm', 0)
        completed = status_stats.get('completed', 0)
        cancelled = status_stats.get('cancelled', 0)
        
        # 获取今日订单数
        today = datetime.now().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())
        today_orders = await Order.filter(create_time__gte=today_start, create_time__lte=today_end).count()
        
        # 获取总交易金额（已完成订单）
        total_amount_result = await Order.filter(status='completed').annotate(total=Sum('price')).values('total')
        total_amount = total_amount_result[0]['total'] if total_amount_result and total_amount_result[0]['total'] else Decimal('0')
        
        # 获取最近7天的订单统计
        daily_stats = []
        for i in range(7):
            date = today - timedelta(days=i)
            date_start = datetime.combine(date, datetime.min.time())
            date_end = datetime.combine(date, datetime.max.time())
            daily_count = await Order.filter(create_time__gte=date_start, create_time__lte=date_end).count()
            daily_stats.append({
                'date': date.strftime('%Y-%m-%d'),
                'count': daily_count
            })
        
        return OrderStatistics(
            totalOrders=total_orders,
            pendingPayment=pending_payment,
            pendingPickup=pending_pickup,
            pendingConfirm=pending_confirm,
            completed=completed,
            cancelled=cancelled,
            todayOrders=today_orders,
            totalAmount=total_amount,
            statusStats=status_stats,
            dailyStats=daily_stats
        )
    
    async def batch_order_operation(self, request: BatchOrderOperationRequest) -> BatchOperationResult:
        """批量订单操作"""
        success_count = 0
        failed_count = 0
        failed_orders = []
        
        for order_id in request.orderIds:
            try:
                if request.operation == "update_status":
                    if not request.value:
                        raise ValueError("状态值不能为空")
                    await order_management_crud.update_order_status(order_id, request.value)
                elif request.operation == "delete":
                    await order_management_crud.delete_order(order_id)
                else:
                    raise ValueError(f"不支持的操作类型: {request.operation}")
                
                success_count += 1
            except Exception as e:
                failed_count += 1
                failed_orders.append({
                    "orderId": order_id,
                    "reason": str(e)
                })
        
        return BatchOperationResult(
            successCount=success_count,
            failedCount=failed_count,
            failedOrders=failed_orders
        )

order_management_service = OrderManagementService()
