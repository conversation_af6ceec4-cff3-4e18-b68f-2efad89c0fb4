from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from decimal import Decimal

class OrderListParams(BaseModel):
    """订单列表查询参数"""
    order_no: Optional[str] = Field(None, description="订单号")
    buyer_username: Optional[str] = Field(None, description="买家用户名")
    seller_username: Optional[str] = Field(None, description="卖家用户名")
    product_title: Optional[str] = Field(None, description="商品标题")
    status: Optional[str] = Field(None, description="订单状态")
    start_date: Optional[str] = Field(None, description="开始日期")
    end_date: Optional[str] = Field(None, description="结束日期")
    min_price: Optional[Decimal] = Field(None, description="最低价格")
    max_price: Optional[Decimal] = Field(None, description="最高价格")
    sortBy: Optional[str] = Field("create_time", description="排序字段")
    sortOrder: Optional[str] = Field("desc", description="排序方向")
    pageNum: Optional[int] = Field(1, ge=1, description="页码")
    pageSize: Optional[int] = Field(20, ge=1, le=100, description="每页数量")

class OrderStatusUpdateRequest(BaseModel):
    """订单状态更新请求"""
    status: str = Field(..., description="订单状态")
    remark: Optional[str] = Field(None, description="备注")
    
    @validator('status')
    def validate_status(cls, v):
        valid_statuses = ['pending_payment', 'pending_pickup', 'pending_confirm', 'completed', 'cancelled']
        if v not in valid_statuses:
            raise ValueError(f'状态必须是以下之一: {", ".join(valid_statuses)}')
        return v

class OrderUpdateRequest(BaseModel):
    """订单更新请求"""
    pickup_location: Optional[str] = Field(None, description="取货地点")
    pickup_time: Optional[datetime] = Field(None, description="取货时间")
    remark: Optional[str] = Field(None, description="备注")

class UserInfo(BaseModel):
    """用户信息"""
    userId: int
    username: str
    nickname: str
    phone: str
    email: str

class ProductInfo(BaseModel):
    """商品信息"""
    productId: int
    title: str
    category: str
    price: Decimal
    images: List[str] = []

class OrderInfo(BaseModel):
    """订单信息响应"""
    orderId: int
    orderNo: str
    productId: int
    productInfo: Optional[ProductInfo] = None
    buyerId: int
    buyerInfo: Optional[UserInfo] = None
    sellerId: int
    sellerInfo: Optional[UserInfo] = None
    price: Decimal
    status: str
    pickupLocation: Optional[str] = None
    pickupTime: Optional[datetime] = None
    paymentMethod: Optional[str] = None
    remark: Optional[str] = None
    createTime: datetime
    updateTime: datetime
    
    class Config:
        from_attributes = True

class OrderStatistics(BaseModel):
    """订单统计信息"""
    totalOrders: int
    pendingPayment: int
    pendingPickup: int
    pendingConfirm: int
    completed: int
    cancelled: int
    todayOrders: int
    totalAmount: Decimal
    statusStats: dict
    dailyStats: List[dict]

class BatchOrderOperationRequest(BaseModel):
    """批量订单操作请求"""
    orderIds: List[int] = Field(..., description="订单ID列表")
    operation: str = Field(..., description="操作类型")
    value: Optional[str] = Field(None, description="操作值")

class BatchOperationResult(BaseModel):
    """批量操作结果"""
    successCount: int
    failedCount: int
    failedOrders: List[dict]
