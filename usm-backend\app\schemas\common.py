from pydantic import BaseModel
from typing import Any, Optional, List, Generic, TypeVar

T = TypeVar('T')

class ApiResponse(BaseModel, Generic[T]):
    """统一API响应格式"""
    code: int = 200
    msg: str = "success"
    data: Optional[T] = None

class PageResponse(BaseModel, Generic[T]):
    """分页响应格式"""
    records: List[T]
    total: int
    pageNum: int
    pageSize: int
    totalPages: int

class ApiPageResponse(BaseModel):
    """分页API响应格式"""
    code: int = 200
    msg: str = "success"
    data: Optional[PageResponse] = None
