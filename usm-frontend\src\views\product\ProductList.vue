<template>
  <div class="product-list-container">
    <!-- 筛选栏 -->
    <div class="filter-bar">
      <div class="filter-left">
        <el-select v-model="filters.category" placeholder="选择分类" clearable class="filter-select">
          <el-option
            v-for="(label, value) in PRODUCT_CATEGORY_LABELS"
            :key="value"
            :label="label"
            :value="value"
          />
        </el-select>

        <el-select v-model="filters.campus" placeholder="选择校区" clearable class="filter-select">
          <el-option
            v-for="(label, value) in CAMPUS_LABELS"
            :key="value"
            :label="label"
            :value="value"
          />
        </el-select>

        <div class="price-range">
          <el-input
            v-model="filters.minPrice"
            placeholder="最低价"
            type="number"
            class="price-input"
          />
          <span class="price-separator">-</span>
          <el-input
            v-model="filters.maxPrice"
            placeholder="最高价"
            type="number"
            class="price-input"
          />
        </div>
      </div>
      
      <div class="filter-right">
        <el-select v-model="sortBy" placeholder="排序方式" class="sort-select">
          <el-option label="发布时间" value="createTime" />
          <el-option label="价格从低到高" value="price_asc" />
          <el-option label="价格从高到低" value="price_desc" />
          <el-option label="浏览量" value="viewCount" />
        </el-select>
      </div>
    </div>

    <!-- 商品列表 -->
    <div class="products-grid" v-loading="loading">
      <div
        v-for="product in products"
        :key="product.productId"
        class="product-card"
        @click="goToProduct(product.productId)"
      >
        <div class="product-image">
          <el-image
            :src="product.images[0]"
            fit="cover"
            :lazy="true"
          >
            <template #error>
              <div class="image-slot">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div class="product-status" v-if="product.status === 'sold'">
            已售出
          </div>
        </div>
        
        <div class="product-info">
          <h4 class="product-title">{{ product.title }}</h4>
          <p class="product-price">
            ¥{{ product.price }}
            <span v-if="product.originalPrice" class="original-price">
              ¥{{ product.originalPrice }}
            </span>
          </p>
          <div class="product-meta">
            <span class="product-category">
              <CategoryIcon :category="product.category" :size="14" />
              {{ getCategoryLabel(product.category) }}
            </span>
            <span class="product-condition">{{ getConditionLabel(product.condition) }}</span>
            <span class="product-campus">{{ getCampusLabel(product.campus) }}</span>
          </div>
          <div class="product-footer">
            <div class="seller-info">
              <el-avatar :src="product.sellerInfo?.avatar" :size="20">
                {{ product.sellerInfo?.nickname?.charAt(0) }}
              </el-avatar>
              <span>{{ product.sellerInfo?.nickname }}</span>
            </div>
            <span class="product-time">{{ formatTime(product.createTime) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <el-empty v-if="!loading && products.length === 0" description="暂无商品" />

    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[20, 40, 60, 80]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Picture } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { formatTime } from '@/utils'
import {
  PRODUCT_CATEGORY_LABELS,
  CAMPUS_LABELS,
  PRODUCT_CONDITION_LABELS
} from '@/constants'
import type { Product } from '@/types'
import CategoryIcon from '@/components/common/CategoryIcon.vue'
import { getProductsApi } from '@/request/productApi'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const products = ref<Product[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const sortBy = ref('createTime')

// 筛选条件
const filters = reactive({
  keyword: '',
  category: '',
  campus: '',
  minPrice: '',
  maxPrice: '',
  condition: ''
})

// 获取商品列表
const fetchProducts = async () => {
  loading.value = true
  try {
    // 调用真实的API
    const response = await getProductsApi({
      ...filters.value,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value
    })

    if (response.code === 200) {
      products.value = response.data.records
      total.value = response.data.total
    } else {
      ElMessage.error(response.msg || '获取商品列表失败')
    }

  } catch (error) {
    console.error('获取商品列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 工具函数
const getCategoryLabel = (category: string) => {
  return PRODUCT_CATEGORY_LABELS[category as keyof typeof PRODUCT_CATEGORY_LABELS] || category
}

const getConditionLabel = (condition: string) => {
  return PRODUCT_CONDITION_LABELS[condition as keyof typeof PRODUCT_CONDITION_LABELS] || condition
}

const getCampusLabel = (campus: string) => {
  return CAMPUS_LABELS[campus as keyof typeof CAMPUS_LABELS] || campus
}

// 导航到商品详情
const goToProduct = (id: number) => {
  router.push({ name: 'ProductDetail', params: { id } })
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchProducts()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchProducts()
}

// 监听路由查询参数
watch(() => route.query, (newQuery) => {
  if (newQuery.keyword) {
    filters.keyword = newQuery.keyword as string
  }
  if (newQuery.category) {
    filters.category = newQuery.category as string
  }
  fetchProducts()
}, { immediate: true })

// 监听筛选条件变化
watch([filters, sortBy], () => {
  currentPage.value = 1
  fetchProducts()
}, { deep: true })

onMounted(() => {
  fetchProducts()
})
</script>

<style lang="stylus" scoped>
.product-list-container
  width 100%
  padding 20px

.filter-bar
  display flex
  justify-content space-between
  align-items center
  margin-bottom 20px
  padding 20px
  background white
  border-radius 8px
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
  
  .filter-left
    display flex
    align-items center
    gap 20px
    flex 1

    .filter-select
      width 160px

    .price-range
      display flex
      align-items center
      gap 8px

      .price-input
        width 120px

      .price-separator
        color #999
        font-weight 500

  .filter-right
    .sort-select
      width 180px

.products-grid
  display grid
  grid-template-columns repeat(auto-fill, minmax(280px, 1fr))
  gap 20px
  margin-bottom 30px
  
  .product-card
    background white
    border-radius 8px
    overflow hidden
    cursor pointer
    transition all 0.3s ease
    box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
    
    &:hover
      transform translateY(-2px)
      box-shadow 0 4px 16px rgba(0, 0, 0, 0.15)
      
    .product-image
      height 200px
      position relative
      overflow hidden
      
      .el-image
        width 100%
        height 100%
        
      .image-slot
        display flex
        align-items center
        justify-content center
        height 100%
        background #f5f7fa
        color #909399
        
      .product-status
        position absolute
        top 10px
        right 10px
        background rgba(0, 0, 0, 0.7)
        color white
        padding 4px 8px
        border-radius 4px
        font-size 12px
        
    .product-info
      padding 15px
      
      .product-title
        font-size 16px
        color #333
        margin-bottom 8px
        overflow hidden
        text-overflow ellipsis
        white-space nowrap
        
      .product-price
        font-size 18px
        color #e74c3c
        font-weight bold
        margin-bottom 8px
        
        .original-price
          font-size 14px
          color #999
          text-decoration line-through
          margin-left 8px
          
      .product-meta
        display flex
        gap 8px
        margin-bottom 10px
        font-size 12px
        color #666
        flex-wrap wrap

        span
          padding 2px 6px
          background #f5f7fa
          border-radius 3px

        .product-category
          display flex
          align-items center
          gap 4px
          color #409eff
          background #e1f3ff
        
      .product-footer
        display flex
        justify-content space-between
        align-items center
        
        .seller-info
          display flex
          align-items center
          gap 6px
          font-size 12px
          color #666
          
        .product-time
          font-size 12px
          color #999

.pagination-container
  display flex
  justify-content center
  margin-top 30px

@media (max-width: 768px)
  .product-list-container
    padding 10px
    
  .filter-bar
    flex-direction column
    gap 15px

    .filter-left
      flex-wrap wrap
      width 100%

      .filter-select
        width 140px

      .price-range
        .price-input
          width 100px

    .filter-right
      width 100%

      .sort-select
        width 100%
      
  .products-grid
    grid-template-columns repeat(2, 1fr)
    gap 10px
</style>
