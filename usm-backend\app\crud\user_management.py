from typing import List, Optional, Dict, Any
from tortoise.expressions import Q
from tortoise.queryset import QuerySet

from app.models.user import User
from app.schemas.user_management import UserListRequest, UserUpdateRequest


class UserManagementCRUD:
    """用户管理CRUD操作"""
    
    async def get_users_list(self, params: UserListRequest) -> Dict[str, Any]:
        """获取用户列表"""
        # 构建查询条件
        query = User.all()
        
        # 用户名筛选
        if params.username:
            query = query.filter(username__icontains=params.username)
        
        # 角色筛选
        if params.role:
            query = query.filter(role_key=params.role)
        
        # 状态筛选
        if params.status:
            query = query.filter(status=params.status)
        
        # 校区筛选
        if params.campus:
            query = query.filter(campus=params.campus)
        
        # 邮箱筛选
        if params.email:
            query = query.filter(email__icontains=params.email)
        
        # 手机号筛选
        if params.phone:
            query = query.filter(phone__icontains=params.phone)
        
        # 排序
        sort_field = self._convert_sort_field(params.sortBy)
        if params.sortOrder == "desc":
            sort_field = f"-{sort_field}"
        query = query.order_by(sort_field)
        
        # 获取总数
        total = await query.count()
        
        # 分页
        offset = (params.pageNum - 1) * params.pageSize
        users = await query.offset(offset).limit(params.pageSize)
        
        # 计算总页数
        total_pages = (total + params.pageSize - 1) // params.pageSize
        
        return {
            "records": users,
            "total": total,
            "pageNum": params.pageNum,
            "pageSize": params.pageSize,
            "totalPages": total_pages
        }
    
    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        return await User.filter(user_id=user_id).first()
    
    async def update_user(self, user_id: int, user_data: UserUpdateRequest) -> Optional[User]:
        """更新用户信息"""
        user = await User.filter(user_id=user_id).first()
        if not user:
            return None
        
        # 更新字段
        update_data = {}
        if user_data.nickname is not None:
            update_data["nickname"] = user_data.nickname
        if user_data.email is not None:
            update_data["email"] = user_data.email
        if user_data.phone is not None:
            update_data["phone"] = user_data.phone
        if user_data.roleKey is not None:
            update_data["role_key"] = user_data.roleKey
        if user_data.campus is not None:
            update_data["campus"] = user_data.campus
        if user_data.status is not None:
            update_data["status"] = user_data.status
        if user_data.sex is not None:
            update_data["sex"] = user_data.sex
        
        if update_data:
            await User.filter(user_id=user_id).update(**update_data)
            # 重新获取更新后的用户
            user = await User.filter(user_id=user_id).first()
        
        return user
    
    async def update_user_status(self, user_id: int, status: str) -> Optional[User]:
        """更新用户状态"""
        user = await User.filter(user_id=user_id).first()
        if not user:
            return None
        
        await User.filter(user_id=user_id).update(status=status)
        return await User.filter(user_id=user_id).first()
    
    async def delete_user(self, user_id: int) -> bool:
        """删除用户（软删除）"""
        user = await User.filter(user_id=user_id).first()
        if not user:
            return False
        
        # 软删除：将状态设置为deleted
        await User.filter(user_id=user_id).update(status="deleted")
        return True
    
    async def get_user_statistics(self) -> Dict[str, Any]:
        """获取用户统计信息"""
        total_users = await User.filter(status__not="deleted").count()
        active_users = await User.filter(status="active").count()
        disabled_users = await User.filter(status="disabled").count()
        pending_users = await User.filter(status="pending").count()
        
        # 按角色统计
        student_count = await User.filter(role_key="student", status__not="deleted").count()
        admin_count = await User.filter(role_key="admin", status__not="deleted").count()
        teacher_count = await User.filter(role_key="teacher", status__not="deleted").count()
        
        # 按校区统计
        campus_stats = {}
        campuses = ["main", "north", "south", "east", "west"]
        for campus in campuses:
            count = await User.filter(campus=campus, status__not="deleted").count()
            campus_stats[campus] = count
        
        return {
            "totalUsers": total_users,
            "activeUsers": active_users,
            "disabledUsers": disabled_users,
            "pendingUsers": pending_users,
            "roleStats": {
                "student": student_count,
                "admin": admin_count,
                "teacher": teacher_count
            },
            "campusStats": campus_stats
        }
    
    def _convert_sort_field(self, field_name: str) -> str:
        """转换排序字段名"""
        field_mapping = {
            "createTime": "create_time",
            "updateTime": "update_time",
            "roleKey": "role_key"
        }
        return field_mapping.get(field_name, field_name)


# 创建CRUD实例
user_management_crud = UserManagementCRUD()
