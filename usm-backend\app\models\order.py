from tortoise.models import Model
from tortoise import fields

class Order(Model):
    """订单模型"""

    order_id = fields.IntField(pk=True, description="订单ID")
    order_no = fields.CharField(max_length=50, unique=True, index=True, description="订单号")
    price = fields.DecimalField(max_digits=10, decimal_places=2, description="交易价格")
    status = fields.CharField(
        max_length=30,
        default="pending_payment",
        description="订单状态：pending_payment-待付款，pending_pickup-待取货，pending_confirm-待确认，completed-已完成，cancelled-已取消"
    )
    pickup_location = fields.CharField(max_length=200, null=True, description="取货地点")
    pickup_time = fields.DatetimeField(null=True, description="取货时间")
    remark = fields.CharField(max_length=500, null=True, description="备注")
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")

    # 关联关系 - 使用外键关系，不需要单独的ID字段
    product = fields.ForeignKeyField("models.Product", related_name="orders", description="商品")
    buyer = fields.ForeignKeyField("models.User", related_name="buy_orders", description="买家")
    seller = fields.ForeignKeyField("models.User", related_name="sell_orders", description="卖家")

    class Meta:
        table = "orders"
        ordering = ["-create_time"]
