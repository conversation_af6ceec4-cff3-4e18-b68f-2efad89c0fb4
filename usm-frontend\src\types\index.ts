// TypeScript 类型定义

// 基础响应类型
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 分页响应类型
export interface PageResponse<T> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
}

// 用户相关类型
export interface User {
  userId: number
  username: string
  nickname: string
  avatar: string
  email: string
  phone: string
  sex: '0' | '1' // 0-男 1-女
  status: string
  roleKey: string
  campus?: string
  studentId?: string
  realName?: string
  isVerified?: boolean
  createTime?: string
  updateTime?: string
}

export interface LoginRequest {
  username: string
  password: string
  code: string
  uuid: string
}

export interface RegisterRequest {
  username: string
  nickname: string
  password: string
  phone: string
  email: string
  sex: '0' | '1'
  roleKey: string
}

// 商品相关类型
export interface Product {
  productId: number
  title: string
  description: string
  category: string
  price: number
  originalPrice?: number
  condition: string
  images: string[]
  sellerId: number
  sellerInfo?: User
  campus: string
  location?: string
  status: string
  viewCount: number
  likeCount: number
  isLiked?: boolean
  isDeleted?: boolean
  tags?: string[]
  createTime: string
  updateTime: string
}

export interface ProductCreateRequest {
  title: string
  description: string
  category: string
  price: number
  originalPrice?: number
  condition: string
  images: string[]
  campus: string
  location?: string
  tags?: string[]
}

export interface ProductUpdateRequest extends Partial<ProductCreateRequest> {
  productId: number
}

export interface ProductSearchParams {
  keyword?: string
  category?: string
  minPrice?: number
  maxPrice?: number
  condition?: string
  campus?: string
  sortBy?: 'createTime' | 'price' | 'viewCount'
  sortOrder?: 'asc' | 'desc'
  pageNum?: number
  pageSize?: number
}

// 订单相关类型
export interface Order {
  orderId: number
  productId: number
  productInfo?: Product
  buyerId: number
  buyerInfo?: User
  sellerId: number
  sellerInfo?: User
  price: number
  status: string
  pickupLocation?: string
  pickupTime?: string
  remark?: string
  createTime: string
  updateTime: string
}

export interface OrderCreateRequest {
  productId: number
  pickupLocation?: string
  remark?: string
}

export interface OrderUpdateRequest {
  orderId: number
  status?: string
  pickupLocation?: string
  pickupTime?: string
  remark?: string
}

// 评论相关类型
export interface Comment {
  commentId: number
  productId: number
  userId: number
  userInfo?: User
  content: string
  parentId?: number
  replies?: Comment[]
  createTime: string
}

export interface CommentCreateRequest {
  productId: number
  content: string
  parentId?: number
}

// 求购相关类型
export interface WantedItem {
  wantedId: number
  title: string
  description: string
  category: string
  maxPrice?: number
  userId: number
  userInfo?: User
  campus: string
  status: string
  responseCount: number
  createTime: string
  updateTime: string
}

export interface WantedCreateRequest {
  title: string
  description: string
  category: string
  maxPrice?: number
  campus: string
}

// 消息相关类型
export interface Message {
  messageId: number
  type: string
  title: string
  content: string
  userId: number
  isRead: boolean
  relatedId?: number
  createTime: string
}

// 举报相关类型
export interface Report {
  reportId: number
  type: string
  targetType: 'product' | 'user' | 'comment'
  targetId: number
  reason: string
  description?: string
  reporterId: number
  status: 'pending' | 'processed' | 'rejected'
  createTime: string
}

export interface ReportCreateRequest {
  type: string
  targetType: 'product' | 'user' | 'comment'
  targetId: number
  reason: string
  description?: string
}

// 申诉相关类型
export interface Appeal {
  appealId: number
  userId: number
  userInfo?: User
  type: 'account_ban' | 'product_removal' | 'order_dispute' | 'payment_issue' | 'other'
  title: string
  description: string
  relatedId?: number // 相关的商品ID、订单ID等
  relatedType?: 'product' | 'order' | 'user'
  status: 'pending' | 'processing' | 'resolved' | 'rejected'
  adminReply?: string
  adminId?: number
  adminInfo?: User
  evidence?: string[] // 证据图片或文件
  priority: 'low' | 'medium' | 'high' | 'urgent'
  createTime: string
  updateTime: string
  resolveTime?: string
}

export interface AppealCreateRequest {
  type: 'account_ban' | 'product_removal' | 'order_dispute' | 'payment_issue' | 'other'
  title: string
  description: string
  relatedId?: number
  relatedType?: 'product' | 'order' | 'user'
  evidence?: string[]
}

// 文件上传相关类型
export interface UploadResponse {
  url: string
  filename: string
  size: number
}

// 统计相关类型
export interface Statistics {
  totalProducts: number
  totalOrders: number
  totalUsers: number
  todayOrders: number
  todayRevenue: number
  popularCategories: Array<{
    category: string
    count: number
  }>
  recentActivities: Array<{
    type: string
    description: string
    time: string
  }>
}

// 角色相关类型
export interface Role {
  roleId: number
  roleName: string
  roleKey: string
  status: string
  permissions?: string[]
}

// 校区相关类型
export interface Campus {
  campusId: number
  campusName: string
  campusCode: string
  address?: string
  description?: string
}

// 分类相关类型
export interface Category {
  categoryId: number
  categoryName: string
  categoryCode: string
  parentId?: number
  icon?: string
  sort: number
  children?: Category[]
}

// 表单验证规则类型
export interface FormRule {
  required?: boolean
  message: string
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: any) => void
}

// 表格列配置类型
export interface TableColumn {
  prop: string
  label: string
  width?: number
  minWidth?: number
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any) => string
}

// 路由元信息类型
export interface RouteMeta {
  title?: string
  icon?: string
  requiresAuth?: boolean
  roles?: string[]
  keepAlive?: boolean
  hidden?: boolean
}
