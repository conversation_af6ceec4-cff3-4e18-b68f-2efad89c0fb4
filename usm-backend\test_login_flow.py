#!/usr/bin/env python3
"""
测试完整的登录流程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.services.auth_service import auth_service
from app.schemas.auth import LoginRequest
from app.crud.user import user_crud
from app.core.security import verify_password

def test_login_flow():
    """测试登录流程"""
    db = SessionLocal()
    try:
        print("🔍 开始测试登录流程...")
        
        # 1. 测试数据库中的用户
        print("\n1. 查询数据库中的用户:")
        admin_user = user_crud.get_user_by_username(db, "admin")
        if admin_user:
            print(f"✅ 找到admin用户:")
            print(f"  - ID: {admin_user.user_id}")
            print(f"  - 用户名: {admin_user.username}")
            print(f"  - 昵称: {admin_user.nickname}")
            print(f"  - 状态: {admin_user.status}")
            print(f"  - 密码哈希: {admin_user.password}")
        else:
            print("❌ 未找到admin用户")
            return
        
        # 2. 测试密码验证
        print("\n2. 测试密码验证:")
        test_password = "123456"
        is_valid = verify_password(test_password, admin_user.password)
        print(f"  - 测试密码: {test_password}")
        print(f"  - 验证结果: {is_valid}")
        
        if not is_valid:
            print("❌ 密码验证失败!")
            return
        
        # 3. 测试authenticate_user方法
        print("\n3. 测试authenticate_user方法:")
        auth_user = auth_service.authenticate_user(db, "admin", "123456")
        if auth_user:
            print(f"✅ authenticate_user成功: {auth_user.username}")
        else:
            print("❌ authenticate_user失败")
            return
        
        # 4. 测试完整登录流程
        print("\n4. 测试完整登录流程:")
        login_request = LoginRequest(username="admin", password="123456")
        try:
            result = auth_service.login(db, login_request)
            print(f"✅ 登录成功!")
            print(f"  - Token: {result['token'][:50]}...")
        except Exception as e:
            print(f"❌ 登录失败: {e}")
            import traceback
            traceback.print_exc()
            return
        
        print("\n🎉 所有测试通过!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_login_flow()
