<template>
  <div class="order-create-container">
    <div class="page-header">
      <el-button @click="goBack" type="text">
        <el-icon><ArrowLeft /></el-icon>
        返回商品详情
      </el-button>
      <h2>确认订单</h2>
    </div>

    <div class="order-content" v-loading="loading">
      <!-- 商品信息 -->
      <el-card class="product-card">
        <template #header>
          <span>商品信息</span>
        </template>
        
        <div class="product-info" v-if="product">
          <el-image
            :src="product.images[0]"
            fit="cover"
            class="product-image"
          >
            <template #error>
              <div class="image-slot">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          
          <div class="product-details">
            <h3>{{ product.title }}</h3>
            <p class="product-description">{{ product.description }}</p>
            <div class="product-meta">
              <span>成色：{{ getConditionLabel(product.condition) }}</span>
              <span>校区：{{ getCampusLabel(product.campus) }}</span>
            </div>
            <div class="product-price">
              <span class="current-price">¥{{ product.price }}</span>
              <span v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 卖家信息 -->
      <el-card class="seller-card">
        <template #header>
          <span>卖家信息</span>
        </template>
        
        <div class="seller-info" v-if="product?.sellerInfo">
          <el-avatar :src="product.sellerInfo.avatar" :size="50">
            {{ product.sellerInfo.nickname?.charAt(0) }}
          </el-avatar>
          <div class="seller-details">
            <h4>{{ product.sellerInfo.nickname }}</h4>
            <div class="seller-stats">
              <el-tag size="small" type="success">信誉良好</el-tag>
              <span>成交 15 笔</span>
              <span>好评率 98%</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 交易信息 -->
      <el-card class="order-card">
        <template #header>
          <span>交易信息</span>
        </template>
        
        <el-form :model="orderForm" :rules="orderRules" ref="formRef" label-width="100px">
          <el-form-item label="交易方式" prop="tradeType">
            <el-radio-group v-model="orderForm.tradeType">
              <el-radio value="pickup">线下面交</el-radio>
              <el-radio value="delivery">校内配送</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="交易地点" prop="location" v-if="orderForm.tradeType === 'pickup'">
            <el-input
              v-model="orderForm.location"
              placeholder="请输入具体的交易地点"
            />
          </el-form-item>
          
          <el-form-item label="配送地址" prop="address" v-if="orderForm.tradeType === 'delivery'">
            <el-input
              v-model="orderForm.address"
              type="textarea"
              :rows="3"
              placeholder="请输入详细的配送地址"
            />
          </el-form-item>
          
          <el-form-item label="联系电话" prop="phone">
            <el-input
              v-model="orderForm.phone"
              placeholder="请输入您的联系电话"
            />
          </el-form-item>
          
          <el-form-item label="备注信息">
            <el-input
              v-model="orderForm.remark"
              type="textarea"
              :rows="3"
              placeholder="有什么特殊要求可以在这里说明"
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 订单总计 -->
      <el-card class="total-card">
        <div class="order-total">
          <div class="total-item">
            <span>商品价格：</span>
            <span class="price">¥{{ product?.price || 0 }}</span>
          </div>
          <div class="total-item" v-if="orderForm.tradeType === 'delivery'">
            <span>配送费用：</span>
            <span class="price">¥5</span>
          </div>
          <div class="total-item total-final">
            <span>总计：</span>
            <span class="price final-price">¥{{ totalPrice }}</span>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button size="large" @click="goBack">取消</el-button>
        <el-button type="primary" size="large" @click="handleSubmit" :loading="submitting">
          确认下单
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Picture } from '@element-plus/icons-vue'
import { PRODUCT_CONDITION_LABELS, CAMPUS_LABELS } from '@/constants'
import type { Product } from '@/types'
import type { FormInstance } from 'element-plus'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const submitting = ref(false)
const product = ref<Product | null>(null)
const formRef = ref<FormInstance>()

// 订单表单
const orderForm = reactive({
  tradeType: 'pickup',
  location: '',
  address: '',
  phone: '',
  remark: ''
})

// 表单验证规则
const orderRules = {
  tradeType: [
    { required: true, message: '请选择交易方式', trigger: 'change' }
  ],
  location: [
    { required: true, message: '请输入交易地点', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入配送地址', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 计算总价
const totalPrice = computed(() => {
  const basePrice = product.value?.price || 0
  const deliveryFee = orderForm.tradeType === 'delivery' ? 5 : 0
  return basePrice + deliveryFee
})

// 获取商品详情
const fetchProductDetail = async () => {
  const productId = route.query.productId
  if (!productId) {
    ElMessage.error('商品信息不存在')
    goBack()
    return
  }

  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟商品数据
    product.value = {
      productId: Number(productId),
      title: '高等数学教材（第七版）',
      description: '同济大学出版社，九成新，无笔记，适合大一学生使用。',
      category: 'textbook',
      price: 25,
      originalPrice: 45,
      condition: 'good',
      images: ['/src/assets/background.jpg'],
      sellerId: 2,
      sellerInfo: {
        userId: 2,
        username: 'student2',
        nickname: '小红',
        avatar: '',
        email: '',
        phone: '',
        sex: '0',
        status: '',
        roleKey: 'student',
        campus: 'main'
      },
      campus: 'main',
      status: 'published',
      viewCount: 156,
      likeCount: 23,
      createTime: '2024-01-15T10:30:00',
      updateTime: '2024-01-15T10:30:00'
    }
  } catch (error) {
    ElMessage.error('获取商品信息失败')
    goBack()
  } finally {
    loading.value = false
  }
}

// 提交订单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        ElMessage.success('订单创建成功！')
        // 跳转到订单列表或支付页面
        router.push({ name: 'OrderList' })
      } catch (error) {
        ElMessage.error('订单创建失败，请重试')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 工具函数
const getConditionLabel = (condition: string) => {
  return PRODUCT_CONDITION_LABELS[condition as keyof typeof PRODUCT_CONDITION_LABELS] || condition
}

const getCampusLabel = (campus: string) => {
  return CAMPUS_LABELS[campus as keyof typeof CAMPUS_LABELS] || campus
}

onMounted(() => {
  fetchProductDetail()
})
</script>

<style lang="stylus" scoped>
.order-create-container
  width 100%
  max-width 800px
  margin 0 auto
  padding 20px

.page-header
  display flex
  align-items center
  gap 15px
  margin-bottom 20px
  
  h2
    margin 0
    color #333

.order-content
  .el-card
    margin-bottom 20px

.product-info
  display flex
  gap 15px
  
  .product-image
    width 120px
    height 120px
    border-radius 8px
    flex-shrink 0
    
  .product-details
    flex 1
    
    h3
      margin 0 0 8px 0
      color #333
      
    .product-description
      margin 0 0 10px 0
      color #666
      font-size 14px
      
    .product-meta
      display flex
      gap 15px
      margin-bottom 10px
      font-size 14px
      color #999
      
    .product-price
      .current-price
        font-size 20px
        color #e74c3c
        font-weight bold
        
      .original-price
        margin-left 10px
        color #999
        text-decoration line-through

.seller-info
  display flex
  align-items center
  gap 15px
  
  .seller-details
    h4
      margin 0 0 5px 0
      color #333
      
    .seller-stats
      display flex
      align-items center
      gap 10px
      font-size 14px
      color #666

.order-total
  .total-item
    display flex
    justify-content space-between
    margin-bottom 10px
    
    &.total-final
      border-top 1px solid #eee
      padding-top 10px
      font-size 18px
      font-weight bold
      
    .price
      color #e74c3c
      
    .final-price
      font-size 20px

.action-buttons
  display flex
  gap 15px
  justify-content center
  margin-top 30px

.image-slot
  display flex
  align-items center
  justify-content center
  width 100%
  height 100%
  background #f5f7fa
  color #909399

@media (max-width: 768px)
  .order-create-container
    padding 10px
    
  .product-info
    flex-direction column
    
  .product-image
    width 100%
    height 200px
</style>
