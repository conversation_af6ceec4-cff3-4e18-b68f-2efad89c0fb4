from typing import Optional, List
from datetime import datetime

from app.models.user import User, Role
from app.schemas.auth import RegisterRequest

class UserCRUD:
    """用户CRUD操作"""

    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        return await User.filter(user_id=user_id).first()

    async def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return await User.filter(username=username).first()

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        return await User.filter(email=email).first()

    async def get_user_by_phone(self, phone: str) -> Optional[User]:
        """根据手机号获取用户"""
        return await User.filter(phone=phone).first()

    async def create_user(self, user_data: RegisterRequest) -> User:
        """创建用户"""
        db_user = await User.create(
            username=user_data.username,
            password=user_data.password,  # 明文存储密码
            nickname=user_data.nickname,
            email=user_data.email,
            phone=user_data.phone,
            sex=int(user_data.sex),
            role_key=user_data.roleKey,
            campus=user_data.campus,
            status="active",
            is_verified=False
        )
        return db_user

    async def update_user(self, user_id: int, **kwargs) -> Optional[User]:
        """更新用户信息"""
        db_user = await self.get_user_by_id(user_id)
        if not db_user:
            return None

        for key, value in kwargs.items():
            if hasattr(db_user, key):
                setattr(db_user, key, value)

        await db_user.save()
        return db_user

    async def update_last_login_time(self, user_id: int) -> None:
        """更新最后登录时间"""
        await User.filter(user_id=user_id).update(last_login_time=datetime.now())

    async def get_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """获取用户列表"""
        return await User.all().offset(skip).limit(limit)

    async def get_users_count(self) -> int:
        """获取用户总数"""
        return await User.all().count()

class RoleCRUD:
    """角色CRUD操作"""

    async def get_role_by_key(self, role_key: str) -> Optional[Role]:
        """根据角色标识获取角色"""
        return await Role.filter(role_key=role_key).first()

    async def get_roles(self) -> List[Role]:
        """获取所有角色"""
        return await Role.filter(status=True).all()

    async def create_role(self, role_name: str, role_key: str, description: str = None) -> Role:
        """创建角色"""
        db_role = await Role.create(
            role_name=role_name,
            role_key=role_key,
            description=description
        )
        return db_role

# 创建CRUD实例
user_crud = UserCRUD()
role_crud = RoleCRUD()
