-- 修复用户密码为明文
-- 所有用户的密码都是 123456
-- 改为明文存储（仅用于开发测试）

USE db_usm;

-- 更新所有测试用户的密码为明文
-- 密码: 123456

UPDATE users SET password = '123456' WHERE username = 'admin';
UPDATE users SET password = '123456' WHERE username = 'student';
UPDATE users SET password = '123456' WHERE username = 'student2';
UPDATE users SET password = '123456' WHERE username = 'student3';
UPDATE users SET password = '123456' WHERE username = 'student4';

-- 验证更新结果
SELECT username, nickname, password FROM users;

-- 显示更新的用户数量
SELECT '密码更新完成' as message, COUNT(*) as updated_users FROM users WHERE password = '123456';
