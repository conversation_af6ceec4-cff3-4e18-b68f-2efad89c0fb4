<template>
  <div class="order-payment">
    <div class="payment-container">
      <!-- 订单信息 -->
      <el-card class="order-info-card">
        <template #header>
          <span>订单信息</span>
        </template>
        
        <div class="order-details">
          <div class="product-info">
            <el-image 
              :src="orderInfo.productImage" 
              fit="cover"
              class="product-image"
            />
            <div class="product-details">
              <h3>{{ orderInfo.productTitle }}</h3>
              <p class="product-desc">{{ orderInfo.productDescription }}</p>
              <div class="price-info">
                <span class="current-price">¥{{ orderInfo.price }}</span>
                <span v-if="orderInfo.originalPrice" class="original-price">¥{{ orderInfo.originalPrice }}</span>
              </div>
            </div>
          </div>
          
          <div class="order-summary">
            <div class="summary-item">
              <span>商品金额：</span>
              <span>¥{{ orderInfo.price }}</span>
            </div>
            <div class="summary-item">
              <span>运费：</span>
              <span>¥0.00</span>
            </div>
            <div class="summary-item total">
              <span>应付总额：</span>
              <span class="total-amount">¥{{ orderInfo.totalAmount }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 支付方式 -->
      <el-card class="payment-method-card">
        <template #header>
          <span>选择支付方式</span>
        </template>
        
        <el-radio-group v-model="selectedPaymentMethod" class="payment-methods">
          <div class="payment-option" v-for="method in paymentMethods" :key="method.value">
            <el-radio :value="method.value" class="payment-radio">
              <div class="payment-content">
                <div class="payment-icon">
                  <el-icon :size="24">
                    <component :is="method.icon" />
                  </el-icon>
                </div>
                <div class="payment-info">
                  <div class="payment-name">{{ method.name }}</div>
                  <div class="payment-desc">{{ method.description }}</div>
                </div>
                <div class="payment-badge" v-if="method.badge">
                  <el-tag size="small" type="success">{{ method.badge }}</el-tag>
                </div>
              </div>
            </el-radio>
          </div>
        </el-radio-group>
      </el-card>

      <!-- 支付按钮 -->
      <div class="payment-actions">
        <el-button size="large" @click="goBack">返回订单</el-button>
        <el-button 
          type="primary" 
          size="large" 
          @click="handlePay"
          :loading="paymentLoading"
          :disabled="!selectedPaymentMethod"
        >
          确认支付 ¥{{ orderInfo.totalAmount }}
        </el-button>
      </div>
    </div>

    <!-- 支付确认对话框 -->
    <el-dialog
      v-model="paymentDialogVisible"
      title="支付确认"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="payment-confirm">
        <div class="confirm-info">
          <el-icon class="confirm-icon" :size="48" color="#409eff">
            <CreditCard />
          </el-icon>
          <h3>确认支付信息</h3>
          <p>支付方式：{{ getPaymentMethodName(selectedPaymentMethod) }}</p>
          <p>支付金额：<span class="amount">¥{{ orderInfo.totalAmount }}</span></p>
        </div>
        
        <div class="payment-password" v-if="selectedPaymentMethod === 'balance'">
          <el-input
            v-model="paymentPassword"
            type="password"
            placeholder="请输入支付密码"
            show-password
          />
        </div>
      </div>
      
      <template #footer>
        <el-button @click="paymentDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmPayment"
          :loading="paymentLoading"
        >
          确认支付
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  CreditCard,
  Wallet,
  Phone,
  Money
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

const paymentLoading = ref(false)
const paymentDialogVisible = ref(false)
const selectedPaymentMethod = ref('')
const paymentPassword = ref('')

// 订单信息
const orderInfo = reactive({
  orderId: '',
  productTitle: '',
  productDescription: '',
  productImage: '/src/assets/background.jpg',
  price: 0,
  originalPrice: 0,
  totalAmount: 0
})

// 支付方式
const paymentMethods = [
  {
    value: 'alipay',
    name: '支付宝',
    icon: Wallet,
    badge: '推荐'
  },
  {
    value: 'wechat',
    name: '微信支付',
    icon: Phone
  },
  {
    value: 'balance',
    name: '余额支付',
    icon: Money
  },
  {
    value: 'bank',
    name: '银行卡支付',
    icon: CreditCard
  }
]

// 获取支付方式名称
const getPaymentMethodName = (value: string) => {
  const method = paymentMethods.find(m => m.value === value)
  return method?.name || ''
}

// 获取订单信息
const fetchOrderInfo = async () => {
  try {
    const orderId = route.params.id
    // 模拟获取订单信息
    Object.assign(orderInfo, {
      orderId: orderId,
      productTitle: '二手iPhone 13 Pro',
      productDescription: '9成新，无磕碰，功能正常',
      price: 4500,
      originalPrice: 6999,
      totalAmount: 4500
    })
  } catch (error) {
    ElMessage.error('获取订单信息失败')
  }
}

// 返回订单页面
const goBack = () => {
  router.push({ name: 'OrderList' })
}

// 处理支付
const handlePay = () => {
  if (!selectedPaymentMethod.value) {
    ElMessage.warning('请选择支付方式')
    return
  }
  
  paymentDialogVisible.value = true
}

// 确认支付
const confirmPayment = async () => {
  if (selectedPaymentMethod.value === 'balance' && !paymentPassword.value) {
    ElMessage.warning('请输入支付密码')
    return
  }
  
  paymentLoading.value = true
  try {
    // 模拟支付处理
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('支付成功！')
    paymentDialogVisible.value = false
    
    // 跳转到支付成功页面或订单详情
    router.push({ 
      name: 'OrderDetail', 
      params: { id: orderInfo.orderId },
      query: { paymentSuccess: 'true' }
    })
  } catch (error) {
    ElMessage.error('支付失败，请重试')
  } finally {
    paymentLoading.value = false
  }
}

onMounted(() => {
  fetchOrderInfo()
})
</script>

<style lang="stylus" scoped>
.order-payment
  width 100%
  padding 20px
  background #f5f7fa
  min-height calc(100vh - 60px)

.payment-container
  max-width 1000px
  margin 0 auto

.order-info-card, .payment-method-card
  margin-bottom 20px

.order-details
  .product-info
    display flex
    gap 15px
    margin-bottom 20px
    
    .product-image
      width 100px
      height 100px
      border-radius 8px
      
    .product-details
      flex 1
      
      h3
        margin 0 0 8px 0
        font-size 18px
        color #333
        
      .product-desc
        margin 0 0 10px 0
        color #666
        font-size 14px
        
      .price-info
        .current-price
          font-size 20px
          color #e74c3c
          font-weight bold
          margin-right 10px
          
        .original-price
          font-size 14px
          color #999
          text-decoration line-through

  .order-summary
    border-top 1px solid #eee
    padding-top 15px
    
    .summary-item
      display flex
      justify-content space-between
      margin-bottom 8px
      
      &.total
        font-size 16px
        font-weight bold
        color #333
        border-top 1px solid #eee
        padding-top 8px
        margin-top 8px
        
        .total-amount
          color #e74c3c
          font-size 18px

.payment-methods
  width 100%
  display flex
  flex-direction column
  
  .payment-option
    margin-bottom 15px
    border 1px solid #e0e6ed
    border-radius 8px
    overflow hidden
    transition all 0.3s
    width 100%
    
    &:hover
      border-color #409eff
      
    .payment-radio
      width 100%
      margin 0
      
      :deep(.el-radio__input)
        margin-right 15px
        
      .payment-content
        display flex
        align-items center
        padding 20px
        width 100%
        
        .payment-icon
          margin-right 20px
          color #409eff
          font-size 28px
          
        .payment-info
          flex 1
          
          .payment-name
            font-size 18px
            font-weight 500
            color #333
            margin-bottom 6px
            
          .payment-desc
            font-size 14px
            color #666
            
        .payment-badge
          margin-left auto

.payment-actions
  display flex
  justify-content space-between
  gap 15px
  margin-top 30px
  
  .el-button
    flex 1
    height 50px
    font-size 16px

.payment-confirm
  text-align center
  
  .confirm-info
    margin-bottom 20px
    
    .confirm-icon
      margin-bottom 15px
      
    h3
      margin 0 0 15px 0
      color #333
      
    p
      margin 5px 0
      color #666
      
      .amount
        color #e74c3c
        font-weight bold
        font-size 18px

  .payment-password
    margin-top 20px

@media (max-width: 768px)
  .order-payment
    padding 10px
    
  .order-details
    .product-info
      flex-direction column
      
      .product-image
        width 80px
        height 80px
        
  .payment-actions
    flex-direction column
    
    .el-button
      margin-bottom 10px
</style>
