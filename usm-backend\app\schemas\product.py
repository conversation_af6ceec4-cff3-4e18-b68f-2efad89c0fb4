from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from decimal import Decimal

class ProductCategoryInfo(BaseModel):
    """商品分类信息"""
    categoryId: int
    categoryKey: str
    categoryName: str
    icon: str = ""
    sortOrder: int = 0
    status: bool = True


class ProductImageInfo(BaseModel):
    """商品图片信息"""
    imageId: int
    imageUrl: str
    sortOrder: int = 0
    isCover: bool = False


class ProductCreateRequest(BaseModel):
    """创建商品请求"""
    title: str = Field(..., min_length=1, max_length=200, description="商品标题")
    description: str = Field(..., min_length=1, description="商品描述")
    category: str = Field(..., description="商品分类")
    price: Decimal = Field(..., gt=0, description="价格")
    originalPrice: Optional[Decimal] = Field(None, gt=0, description="原价")
    condition: str = Field(..., description="商品成色")
    campus: str = Field(..., description="校区")
    location: Optional[str] = Field(None, max_length=200, description="具体位置")
    images: List[str] = Field(default=[], description="商品图片URL列表")
    tags: List[str] = Field(default=[], description="商品标签")
    
    @validator('condition')
    def validate_condition(cls, v):
        valid_conditions = ['new', 'like_new', 'good', 'fair', 'poor']
        if v not in valid_conditions:
            raise ValueError(f'商品成色必须是以下之一: {", ".join(valid_conditions)}')
        return v


class ProductUpdateRequest(BaseModel):
    """更新商品请求"""
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="商品标题")
    description: Optional[str] = Field(None, min_length=1, description="商品描述")
    category: Optional[str] = Field(None, description="商品分类")
    price: Optional[Decimal] = Field(None, gt=0, description="价格")
    originalPrice: Optional[Decimal] = Field(None, gt=0, description="原价")
    condition: Optional[str] = Field(None, description="商品成色")
    campus: Optional[str] = Field(None, description="校区")
    location: Optional[str] = Field(None, max_length=200, description="具体位置")
    images: Optional[List[str]] = Field(None, description="商品图片URL列表")
    tags: Optional[List[str]] = Field(None, description="商品标签")
    
    @validator('condition')
    def validate_condition(cls, v):
        if v is not None:
            valid_conditions = ['new', 'like_new', 'good', 'fair', 'poor']
            if v not in valid_conditions:
                raise ValueError(f'商品成色必须是以下之一: {", ".join(valid_conditions)}')
        return v


class ProductStatusUpdateRequest(BaseModel):
    """商品状态更新请求"""
    status: str = Field(..., description="商品状态")
    
    @validator('status')
    def validate_status(cls, v):
        valid_statuses = ['draft', 'pending_review', 'published', 'sold', 'removed']
        if v not in valid_statuses:
            raise ValueError(f'商品状态必须是以下之一: {", ".join(valid_statuses)}')
        return v


class SellerInfo(BaseModel):
    """卖家信息"""
    userId: int
    username: str
    nickname: str
    avatar: str = ""
    campus: Optional[str] = None


class ProductInfo(BaseModel):
    """商品信息"""
    productId: int
    title: str
    description: str
    category: str
    price: Decimal
    originalPrice: Optional[Decimal] = None
    condition: str
    campus: str
    location: Optional[str] = None
    sellerId: int
    sellerInfo: Optional[SellerInfo] = None
    status: str
    viewCount: int = 0
    likeCount: int = 0
    isLiked: bool = False
    isDeleted: bool = False
    images: List[str] = []
    tags: List[str] = []
    createTime: datetime
    updateTime: datetime


class ProductListRequest(BaseModel):
    """商品列表查询请求"""
    category: Optional[str] = Field(None, description="商品分类")
    campus: Optional[str] = Field(None, description="校区")
    condition: Optional[str] = Field(None, description="商品成色")
    minPrice: Optional[Decimal] = Field(None, ge=0, description="最低价格")
    maxPrice: Optional[Decimal] = Field(None, ge=0, description="最高价格")
    keyword: Optional[str] = Field(None, description="搜索关键词")
    sellerUsername: Optional[str] = Field(None, description="卖家用户名")
    startDate: Optional[str] = Field(None, description="开始日期")
    endDate: Optional[str] = Field(None, description="结束日期")
    status: Optional[str] = Field(None, description="商品状态")
    isDeleted: Optional[str] = Field(None, description="是否已删除：true-已删除，false-未删除")
    sortBy: str = Field("create_time", description="排序字段")
    sortOrder: str = Field("desc", description="排序方向")
    pageNum: int = Field(1, ge=1, description="页码")
    pageSize: int = Field(20, ge=1, le=100, description="每页数量")

    @validator('sortBy')
    def validate_sort_by(cls, v):
        # 如果为空字符串，使用默认值
        if not v or v.strip() == "":
            return "create_time"

        valid_fields = ['create_time', 'update_time', 'price', 'view_count', 'like_count']
        if v not in valid_fields:
            raise ValueError(f'排序字段必须是以下之一: {", ".join(valid_fields)}')
        return v

    @validator('sortOrder')
    def validate_sort_order(cls, v):
        # 如果为空字符串，使用默认值
        if not v or v.strip() == "":
            return "desc"

        if v not in ['asc', 'desc']:
            raise ValueError('排序方向必须是 asc 或 desc')
        return v
