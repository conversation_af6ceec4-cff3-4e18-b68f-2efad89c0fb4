import axios from 'axios';
const instance = axios.create({
        baseURL: 'http://localhost:18080/api',
        timeout: 15000
    });

// 拦截器
instance.interceptors.request.use(config =>{
    // 从工具函数中获取token，而不是直接从localStorage获取
    // 这样可以确保token有效性检查
    const token = localStorage.getItem('token')
    if(token){
        try {
            // 尝试解析token数据
            const tokenData = JSON.parse(token)
            // 如果是新格式（包含value和expires）
            if(tokenData && tokenData.value && tokenData.expires) {
                // 检查是否过期
                if(Date.now() < tokenData.expires) {
                    // 使用 Bearer 方案，配合后端 HTTPBearer
                    config.headers.Authorization = `Bearer ${tokenData.value}`
                } else {
                    // 如果过期，清除localStorage中的token
                    localStorage.removeItem('token')
                }
            } else {
                // 如果是旧格式，直接使用（补上 Bearer 前缀）
                config.headers.Authorization = token.startsWith('Bearer ')
                  ? token
                  : `Bearer ${token}`
            }
        } catch(e) {
            // 如果解析失败，说明是旧格式的token字符串
            config.headers.Authorization = token.startsWith('Bearer ')
              ? token
              : `Bearer ${token}`
        }
    }
    return config;
},err =>{
    return Promise.reject(err);
})
instance.interceptors.response.use(result =>{
    return result.data;
},err =>{
    return Promise.reject(err);
})
export default instance;