from typing import Optional, List
from pydantic import BaseModel, Field, validator
from datetime import datetime


class UserListRequest(BaseModel):
    """用户列表查询请求"""
    username: Optional[str] = Field(None, description="用户名")
    role: Optional[str] = Field(None, description="角色")
    status: Optional[str] = Field(None, description="状态")
    campus: Optional[str] = Field(None, description="校区")
    email: Optional[str] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, description="手机号")
    sortBy: str = Field("create_time", description="排序字段")
    sortOrder: str = Field("desc", description="排序方向")
    pageNum: int = Field(1, ge=1, description="页码")
    pageSize: int = Field(20, ge=1, le=100, description="每页数量")
    
    @validator('sortBy')
    def validate_sort_by(cls, v):
        if not v or v.strip() == "":
            return "create_time"
        
        valid_fields = ['create_time', 'update_time', 'username', 'nickname', 'email', 'role_key', 'status']
        if v not in valid_fields:
            raise ValueError(f'排序字段必须是以下之一: {", ".join(valid_fields)}')
        return v
    
    @validator('sortOrder')
    def validate_sort_order(cls, v):
        if not v or v.strip() == "":
            return "desc"
        
        if v not in ['asc', 'desc']:
            raise ValueError('排序方向必须是 asc 或 desc')
        return v


class UserUpdateRequest(BaseModel):
    """用户更新请求"""
    nickname: Optional[str] = Field(None, max_length=50, description="昵称")
    email: Optional[str] = Field(None, max_length=100, description="邮箱")
    phone: Optional[str] = Field(None, max_length=20, description="手机号")
    roleKey: Optional[str] = Field(None, description="角色")
    campus: Optional[str] = Field(None, description="校区")
    status: Optional[str] = Field(None, description="状态")
    sex: Optional[str] = Field(None, description="性别")
    
    @validator('email')
    def validate_email(cls, v):
        if v is not None and v.strip():
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, v):
                raise ValueError('邮箱格式不正确')
        return v
    
    @validator('phone')
    def validate_phone(cls, v):
        if v is not None and v.strip():
            import re
            phone_pattern = r'^1[3-9]\d{9}$'
            if not re.match(phone_pattern, v):
                raise ValueError('手机号格式不正确')
        return v
    
    @validator('roleKey')
    def validate_role_key(cls, v):
        if v is not None:
            valid_roles = ['student', 'teacher', 'admin']
            if v not in valid_roles:
                raise ValueError(f'角色必须是以下之一: {", ".join(valid_roles)}')
        return v
    
    @validator('campus')
    def validate_campus(cls, v):
        if v is not None:
            valid_campuses = ['main', 'north', 'south', 'east', 'west']
            if v not in valid_campuses:
                raise ValueError(f'校区必须是以下之一: {", ".join(valid_campuses)}')
        return v
    
    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            valid_statuses = ['active', 'disabled', 'pending', 'deleted']
            if v not in valid_statuses:
                raise ValueError(f'状态必须是以下之一: {", ".join(valid_statuses)}')
        return v
    
    @validator('sex')
    def validate_sex(cls, v):
        if v is not None:
            valid_sex = ['0', '1', '2']  # 0-未知，1-男，2-女
            if v not in valid_sex:
                raise ValueError('性别值不正确')
        return v


class UserStatusUpdateRequest(BaseModel):
    """用户状态更新请求"""
    status: str = Field(..., description="状态")
    
    @validator('status')
    def validate_status(cls, v):
        valid_statuses = ['active', 'disabled', 'pending']
        if v not in valid_statuses:
            raise ValueError(f'状态必须是以下之一: {", ".join(valid_statuses)}')
        return v


class UserInfo(BaseModel):
    """用户信息响应"""
    userId: int
    username: str
    nickname: str
    email: str
    phone: str
    sex: str
    status: str
    roleKey: str
    campus: Optional[str] = None
    avatar: str
    createTime: datetime
    updateTime: datetime

    @validator('sex', pre=True)
    def validate_sex(cls, v):
        # 将数据库中的整数转换为字符串
        if isinstance(v, int):
            return str(v)
        return v or "0"  # 默认为未知

    @validator('campus', pre=True)
    def validate_campus(cls, v):
        # 处理NULL值
        return v or ""

    class Config:
        from_attributes = True


class UserStatistics(BaseModel):
    """用户统计信息"""
    totalUsers: int = Field(..., description="总用户数")
    activeUsers: int = Field(..., description="活跃用户数")
    disabledUsers: int = Field(..., description="禁用用户数")
    pendingUsers: int = Field(..., description="待审核用户数")
    roleStats: dict = Field(..., description="角色统计")
    campusStats: dict = Field(..., description="校区统计")


class BatchUserOperationRequest(BaseModel):
    """批量用户操作请求"""
    userIds: List[int] = Field(..., description="用户ID列表")
    operation: str = Field(..., description="操作类型")
    value: Optional[str] = Field(None, description="操作值")
    
    @validator('operation')
    def validate_operation(cls, v):
        valid_operations = ['enable', 'disable', 'delete', 'change_role', 'change_campus']
        if v not in valid_operations:
            raise ValueError(f'操作类型必须是以下之一: {", ".join(valid_operations)}')
        return v
