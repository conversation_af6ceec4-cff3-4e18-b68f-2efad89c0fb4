<template>
  <div class="message-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>消息中心</h2>
      <div class="header-actions">
        <el-button size="small" @click="handleMarkAllRead">
          <el-icon><Check /></el-icon>
          全部已读
        </el-button>
        <el-button size="small" @click="handleDeleteRead">
          <el-icon><Delete /></el-icon>
          删除已读
        </el-button>
      </div>
    </div>

    <!-- 消息筛选 -->
    <div class="message-filters">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="全部消息" name="all" />
        <el-tab-pane label="未读消息" name="unread" />
        <el-tab-pane label="系统通知" name="system" />
        <el-tab-pane label="交易消息" name="trade" />
      </el-tabs>
    </div>

    <!-- 消息列表 -->
    <div class="message-list" v-loading="loading">
      <div v-if="filteredMessages.length === 0" class="empty-state">
        <el-empty :description="getEmptyDescription()" />
      </div>

      <div v-else class="messages">
        <div
          v-for="message in filteredMessages"
          :key="message.messageId"
          class="message-item"
          :class="{ 'unread': !message.isRead }"
          @click="handleMessageClick(message)"
        >
          <!-- 消息图标 -->
          <div class="message-icon">
            <el-icon v-if="message.type === 'system'" class="system-icon">
              <Bell />
            </el-icon>
            <el-icon v-else-if="message.type === 'trade'" class="trade-icon">
              <ShoppingBag />
            </el-icon>
            <el-avatar v-else :src="message.senderInfo?.avatar" :size="40">
              {{ message.senderInfo?.nickname?.charAt(0) }}
            </el-avatar>
          </div>

          <!-- 消息内容 -->
          <div class="message-content">
            <div class="message-header">
              <h4 class="message-title">{{ message.title }}</h4>
              <div class="message-meta">
                <span class="message-time">{{ formatTime(message.createTime) }}</span>
                <el-tag v-if="message.type === 'system'" type="info" size="small">系统</el-tag>
                <el-tag v-else-if="message.type === 'trade'" type="success" size="small">交易</el-tag>
                <el-tag v-else type="primary" size="small">用户</el-tag>
              </div>
            </div>
            <p class="message-summary">{{ message.content }}</p>
            <div v-if="message.productInfo" class="related-product">
              <span class="product-label">相关商品：</span>
              <span class="product-name">{{ message.productInfo.title }}</span>
              <span class="product-price">¥{{ message.productInfo.price }}</span>
            </div>
          </div>

          <!-- 消息状态 -->
          <div class="message-status">
            <el-badge v-if="!message.isRead" is-dot />
            <el-dropdown @command="handleMessageAction">
              <el-button size="small" text>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    :command="{ action: 'read', message }"
                    v-if="!message.isRead"
                  >
                    标记已读
                  </el-dropdown-item>
                  <el-dropdown-item
                    :command="{ action: 'unread', message }"
                    v-else
                  >
                    标记未读
                  </el-dropdown-item>
                  <el-dropdown-item
                    :command="{ action: 'delete', message }"
                    divided
                  >
                    删除消息
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Check,
  Delete,
  Bell,
  ShoppingBag,
  MoreFilled
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { formatTime } from '@/utils'

interface MessageItem {
  messageId: number
  type: 'system' | 'trade' | 'user'
  title: string
  content: string
  senderInfo?: {
    userId: number
    username: string
    nickname: string
    avatar: string
  }
  productInfo?: {
    productId: number
    title: string
    price: number
  }
  isRead: boolean
  createTime: string
}

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const activeTab = ref('all')
const messages = ref<MessageItem[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 计算属性
const filteredMessages = computed(() => {
  let filtered = messages.value

  switch (activeTab.value) {
    case 'unread':
      filtered = messages.value.filter(m => !m.isRead)
      break
    case 'system':
      filtered = messages.value.filter(m => m.type === 'system')
      break
    case 'trade':
      filtered = messages.value.filter(m => m.type === 'trade')
      break
    default:
      filtered = messages.value
  }

  return filtered
})

// 获取消息列表
const fetchMessages = async () => {
  loading.value = true
  try {
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000))

    messages.value = [
      {
        messageId: 1,
        type: 'system',
        title: '欢迎使用校园二手交易平台',
        content: '欢迎您注册成为平台用户！请遵守平台规则，诚信交易。如有问题请联系客服。',
        isRead: false,
        createTime: '2024-01-16T10:00:00'
      },
      {
        messageId: 2,
        type: 'trade',
        title: '商品询问',
        content: '用户小红对您的商品"高等数学教材"感兴趣，询问是否还有库存。',
        senderInfo: {
          userId: 2,
          username: 'student2',
          nickname: '小红',
          avatar: ''
        },
        productInfo: {
          productId: 1,
          title: '高等数学教材（第七版）',
          price: 25
        },
        isRead: true,
        createTime: '2024-01-16T09:30:00'
      },
      {
        messageId: 3,
        type: 'system',
        title: '商品审核通过',
        content: '您发布的商品"iPhone 13 Pro 256G"已通过审核，现已上架展示。',
        productInfo: {
          productId: 2,
          title: 'iPhone 13 Pro 256G',
          price: 4500
        },
        isRead: true,
        createTime: '2024-01-15T16:20:00'
      },
      {
        messageId: 4,
        type: 'trade',
        title: '交易完成',
        content: '您与用户小李的交易已完成，请及时确认收货并进行评价。',
        senderInfo: {
          userId: 3,
          username: 'student3',
          nickname: '小李',
          avatar: ''
        },
        productInfo: {
          productId: 3,
          title: '计算机组成原理教材',
          price: 35
        },
        isRead: false,
        createTime: '2024-01-15T14:10:00'
      },
      {
        messageId: 5,
        type: 'system',
        title: '安全提醒',
        content: '请注意交易安全，建议选择平台推荐的安全交易方式，避免私下转账。',
        isRead: true,
        createTime: '2024-01-14T12:00:00'
      }
    ]

    total.value = messages.value.length
  } catch (error) {
    ElMessage.error('获取消息列表失败')
  } finally {
    loading.value = false
  }
}

// 事件处理方法
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  currentPage.value = 1
}

const handleMessageClick = (message: MessageItem) => {
  // 标记为已读
  if (!message.isRead) {
    message.isRead = true
    ElMessage.success('消息已标记为已读')
  }

  // 如果有相关商品，跳转到商品详情
  if (message.productInfo) {
    router.push({
      name: 'ProductDetail',
      params: { id: message.productInfo.productId }
    })
  }
}

const handleMessageAction = (command: { action: string, message: MessageItem }) => {
  const { action, message } = command

  switch (action) {
    case 'read':
      message.isRead = true
      ElMessage.success('已标记为已读')
      break
    case 'unread':
      message.isRead = false
      ElMessage.success('已标记为未读')
      break
    case 'delete':
      handleDeleteMessage(message)
      break
  }
}

const handleDeleteMessage = async (message: MessageItem) => {
  try {
    await ElMessageBox.confirm('确定要删除这条消息吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const index = messages.value.findIndex(m => m.messageId === message.messageId)
    if (index > -1) {
      messages.value.splice(index, 1)
      total.value = messages.value.length
      ElMessage.success('消息已删除')
    }
  } catch {
    // 用户取消
  }
}

const handleMarkAllRead = () => {
  messages.value.forEach(message => {
    message.isRead = true
  })
  ElMessage.success('已标记全部为已读')
}

const handleDeleteRead = async () => {
  try {
    await ElMessageBox.confirm('确定要删除所有已读消息吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    messages.value = messages.value.filter(message => !message.isRead)
    total.value = messages.value.length
    ElMessage.success('已删除所有已读消息')
  } catch {
    // 用户取消
  }
}

const getEmptyDescription = () => {
  switch (activeTab.value) {
    case 'unread':
      return '暂无未读消息'
    case 'system':
      return '暂无系统通知'
    case 'trade':
      return '暂无交易消息'
    default:
      return '暂无消息'
  }
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchMessages()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchMessages()
}

onMounted(() => {
  fetchMessages()
})
</script>

<style lang="stylus" scoped>
.message-container
  width 100%
  padding 20px
  background #f5f7fa
  min-height calc(100vh - 60px)

.page-header
  display flex
  justify-content space-between
  align-items center
  margin-bottom 20px
  padding 20px
  background white
  border-radius 8px
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)

  h2
    margin 0
    color #333
    font-size 24px

  .header-actions
    display flex
    gap 10px

.message-filters
  margin-bottom 20px
  background white
  border-radius 8px
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)

  .el-tabs
    padding 0 20px

.message-list
  background white
  border-radius 8px
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
  min-height 400px

  .empty-state
    display flex
    align-items center
    justify-content center
    height 400px

  .messages
    .message-item
      display flex
      align-items flex-start
      padding 20px
      border-bottom 1px solid #f0f0f0
      cursor pointer
      transition background-color 0.2s

      &:hover
        background #f8f9fa

      &.unread
        background #f0f9ff
        border-left 4px solid #409eff

        .message-title
          font-weight bold

      &:last-child
        border-bottom none

      .message-icon
        margin-right 15px
        flex-shrink 0

        .system-icon
          width 40px
          height 40px
          background #e6f7ff
          border-radius 50%
          display flex
          align-items center
          justify-content center
          color #409eff

        .trade-icon
          width 40px
          height 40px
          background #f0f9ff
          border-radius 50%
          display flex
          align-items center
          justify-content center
          color #67c23a

      .message-content
        flex 1
        min-width 0

        .message-header
          display flex
          justify-content space-between
          align-items flex-start
          margin-bottom 8px

          .message-title
            color #333
            font-size 16px
            margin 0

          .message-meta
            display flex
            align-items center
            gap 8px
            flex-shrink 0

            .message-time
              color #999
              font-size 12px

        .message-summary
          color #666
          font-size 14px
          line-height 1.5
          margin 0 0 10px 0
          overflow hidden
          text-overflow ellipsis
          display -webkit-box
          -webkit-line-clamp 2
          -webkit-box-orient vertical

        .related-product
          display flex
          align-items center
          gap 8px
          font-size 12px

          .product-label
            color #999

          .product-name
            color #409eff

          .product-price
            color #e74c3c
            font-weight bold

      .message-status
        margin-left 15px
        flex-shrink 0
        display flex
        align-items center
        gap 10px

.pagination-container
  margin-top 20px
  display flex
  justify-content center
  padding 20px
  background white
  border-radius 8px
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)

@media (max-width: 768px)
  .message-container
    padding 10px

  .page-header
    flex-direction column
    gap 15px
    align-items flex-start

    .header-actions
      width 100%
      justify-content flex-end

  .message-item
    .message-content
      .message-header
        flex-direction column
        align-items flex-start
        gap 5px

        .message-meta
          align-self flex-end

    .message-status
      margin-left 10px
</style>
