from typing import List, Optional, Dict, Any
from fastapi import HTTPException

from app.crud.user_management import user_management_crud
from app.models.user import User
from app.schemas.user_management import (
    UserListRequest, UserUpdateRequest, UserStatusUpdateRequest,
    UserInfo, UserStatistics, BatchUserOperationRequest
)


class UserManagementService:
    """用户管理服务"""
    
    async def _get_user_id_by_username(self, username: str) -> int:
        """根据用户名获取用户ID"""
        user = await User.filter(username=username).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        return user.user_id
    
    async def get_users_list(self, params: UserListRequest, username: Optional[str] = None) -> Dict[str, Any]:
        """获取用户列表"""
        try:
            # 管理员权限检查
            if username:
                current_user = await User.filter(username=username).first()
                if not current_user or current_user.role_key != 'admin':
                    raise HTTPException(status_code=403, detail="权限不足")
            
            result = await user_management_crud.get_users_list(params)
            
            # 转换用户信息
            users_info = []
            for user in result["records"]:
                user_info = await self._convert_to_user_info(user)
                users_info.append(user_info)
            
            result["records"] = users_info
            return result
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")
    
    async def get_user_detail(self, user_id: int, username: str) -> UserInfo:
        """获取用户详情"""
        # 权限检查
        current_user = await User.filter(username=username).first()
        if not current_user:
            raise HTTPException(status_code=401, detail="用户未登录")
        
        # 管理员可以查看所有用户，普通用户只能查看自己
        if current_user.role_key != 'admin' and current_user.user_id != user_id:
            raise HTTPException(status_code=403, detail="权限不足")
        
        user = await user_management_crud.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        return await self._convert_to_user_info(user)
    
    async def update_user(self, user_id: int, user_data: UserUpdateRequest, username: str) -> UserInfo:
        """更新用户信息"""
        # 权限检查
        current_user = await User.filter(username=username).first()
        if not current_user:
            raise HTTPException(status_code=401, detail="用户未登录")
        
        # 管理员可以修改所有用户，普通用户只能修改自己
        if current_user.role_key != 'admin' and current_user.user_id != user_id:
            raise HTTPException(status_code=403, detail="权限不足")
        
        # 普通用户不能修改角色和状态
        if current_user.role_key != 'admin':
            user_data.roleKey = None
            user_data.status = None
        
        user = await user_management_crud.update_user(user_id, user_data)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        return await self._convert_to_user_info(user)
    
    async def update_user_status(self, user_id: int, status_data: UserStatusUpdateRequest, username: str) -> UserInfo:
        """更新用户状态"""
        # 权限检查 - 只有管理员可以修改用户状态
        current_user = await User.filter(username=username).first()
        if not current_user or current_user.role_key != 'admin':
            raise HTTPException(status_code=403, detail="权限不足")
        
        # 不能修改自己的状态
        if current_user.user_id == user_id:
            raise HTTPException(status_code=400, detail="不能修改自己的状态")
        
        user = await user_management_crud.update_user_status(user_id, status_data.status)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        return await self._convert_to_user_info(user)
    
    async def delete_user(self, user_id: int, username: str) -> bool:
        """删除用户"""
        # 权限检查 - 只有管理员可以删除用户
        current_user = await User.filter(username=username).first()
        if not current_user or current_user.role_key != 'admin':
            raise HTTPException(status_code=403, detail="权限不足")
        
        # 不能删除自己
        if current_user.user_id == user_id:
            raise HTTPException(status_code=400, detail="不能删除自己")
        
        success = await user_management_crud.delete_user(user_id)
        if not success:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        return True
    
    async def get_user_statistics(self, username: str) -> UserStatistics:
        """获取用户统计信息"""
        # 权限检查 - 只有管理员可以查看统计信息
        current_user = await User.filter(username=username).first()
        if not current_user or current_user.role_key != 'admin':
            raise HTTPException(status_code=403, detail="权限不足")
        
        try:
            stats = await user_management_crud.get_user_statistics()
            return UserStatistics(**stats)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")
    
    async def batch_user_operation(self, operation_data: BatchUserOperationRequest, username: str) -> Dict[str, Any]:
        """批量用户操作"""
        # 权限检查 - 只有管理员可以进行批量操作
        current_user = await User.filter(username=username).first()
        if not current_user or current_user.role_key != 'admin':
            raise HTTPException(status_code=403, detail="权限不足")
        
        success_count = 0
        failed_count = 0
        failed_users = []
        
        for user_id in operation_data.userIds:
            try:
                # 不能操作自己
                if user_id == current_user.user_id:
                    failed_count += 1
                    failed_users.append({"userId": user_id, "reason": "不能操作自己"})
                    continue
                
                if operation_data.operation == 'enable':
                    await user_management_crud.update_user_status(user_id, 'active')
                elif operation_data.operation == 'disable':
                    await user_management_crud.update_user_status(user_id, 'disabled')
                elif operation_data.operation == 'delete':
                    await user_management_crud.delete_user(user_id)
                elif operation_data.operation == 'change_role':
                    if operation_data.value:
                        update_data = UserUpdateRequest(roleKey=operation_data.value)
                        await user_management_crud.update_user(user_id, update_data)
                elif operation_data.operation == 'change_campus':
                    if operation_data.value:
                        update_data = UserUpdateRequest(campus=operation_data.value)
                        await user_management_crud.update_user(user_id, update_data)
                
                success_count += 1
            except Exception as e:
                failed_count += 1
                failed_users.append({"userId": user_id, "reason": str(e)})
        
        return {
            "successCount": success_count,
            "failedCount": failed_count,
            "failedUsers": failed_users
        }
    
    async def _convert_to_user_info(self, user: User) -> UserInfo:
        """转换用户信息"""
        return UserInfo(
            userId=user.user_id,
            username=user.username,
            nickname=user.nickname,
            email=user.email,
            phone=user.phone,
            sex=user.sex,
            status=user.status,
            roleKey=user.role_key,
            campus=user.campus,
            avatar=user.avatar,
            createTime=user.create_time,
            updateTime=user.update_time
        )


# 创建服务实例
user_management_service = UserManagementService()
