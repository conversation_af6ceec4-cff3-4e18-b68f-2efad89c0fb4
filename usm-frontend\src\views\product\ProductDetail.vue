<template>
  <div class="product-detail-container">
    <div class="product-detail-content" v-loading="loading">
      <div class="product-images">
        <el-carousel height="400px" indicator-position="outside">
          <el-carousel-item v-for="(image, index) in product.images" :key="index">
            <el-image :src="image" fit="cover" style="width: 100%; height: 100%">
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </el-carousel-item>
        </el-carousel>
      </div>
      
      <div class="product-info">
        <h1 class="product-title">{{ product.title }}</h1>
        <div class="product-price">
          <span class="current-price">¥{{ product.price }}</span>
          <span v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</span>
        </div>
        
        <div class="product-meta">
          <div class="meta-item">
            <span class="label">分类：</span>
            <span class="category-value">
              <CategoryIcon :category="product.category" :size="16" />
              {{ getCategoryLabel(product.category) }}
            </span>
          </div>
          <div class="meta-item">
            <span class="label">新旧程度：</span>
            <span>{{ getConditionLabel(product.condition) }}</span>
          </div>
          <div class="meta-item">
            <span class="label">校区：</span>
            <span>{{ getCampusLabel(product.campus) }}</span>
          </div>
          <div class="meta-item">
            <span class="label">浏览量：</span>
            <span>{{ product.viewCount }}</span>
          </div>
        </div>
        
        <div class="product-description">
          <h3>商品描述</h3>
          <p>{{ product.description }}</p>
        </div>
        
        <div class="seller-info">
          <h3>卖家信息</h3>
          <div class="seller-card">
            <el-avatar :src="product.sellerInfo?.avatar" :size="50">
              {{ product.sellerInfo?.nickname?.charAt(0) }}
            </el-avatar>
            <div class="seller-details">
              <div class="seller-name">{{ product.sellerInfo?.nickname }}</div>
              <div class="seller-campus">{{ getCampusLabel(product.sellerInfo?.campus) }}</div>
            </div>
          </div>
        </div>
        
        <div class="action-buttons">
          <el-button type="primary" size="large" @click="handleBuy">
            立即购买
          </el-button>
          <el-button @click="handleContact" size="large">
            联系卖家
          </el-button>
          <el-button @click="handleFavorite" :type="isFavorited ? 'danger' : 'default'" size="large">
            <el-icon><Star /></el-icon>
            {{ isFavorited ? '取消收藏' : '收藏' }}
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 评论区 -->
    <div class="comments-section">
      <h3>商品评论</h3>
      <div class="comment-form" v-if="userStore.isLoggedIn">
        <el-input
          v-model="newComment"
          type="textarea"
          placeholder="写下你的评论..."
          :rows="3"
        />
        <el-button type="primary" @click="handleSubmitComment" style="margin-top: 10px">
          发表评论
        </el-button>
      </div>
      
      <div class="comments-list">
        <div v-for="comment in comments" :key="comment.commentId" class="comment-item">
          <el-avatar :src="comment.userInfo?.avatar" :size="32">
            {{ comment.userInfo?.nickname?.charAt(0) }}
          </el-avatar>
          <div class="comment-content">
            <div class="comment-header">
              <span class="comment-author">{{ comment.userInfo?.nickname }}</span>
              <span class="comment-time">{{ formatTime(comment.createTime) }}</span>
            </div>
            <p class="comment-text">{{ comment.content }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 联系卖家对话框 -->
    <ContactSellerDialog
      v-model="contactDialogVisible"
      :seller-info="sellerInfo"
      @message-sent="handleMessageSent"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture, Star } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores'
import { formatTime } from '@/utils'
import { PRODUCT_CATEGORY_LABELS, PRODUCT_CONDITION_LABELS, CAMPUS_LABELS } from '@/constants'
import type { Product, Comment } from '@/types'
import ContactSellerDialog from '@/components/common/ContactSellerDialog.vue'
import CategoryIcon from '@/components/common/CategoryIcon.vue'
import { getProductDetailApi } from '@/request/productApi'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const product = ref<Product>({} as Product)
const comments = ref<Comment[]>([])
const newComment = ref('')
const isFavorited = ref(false)

const productId = computed(() => Number(route.params.id))

// 获取商品详情
const fetchProductDetail = async () => {
  loading.value = true
  try {
    // 调用真实的API
    const response = await getProductDetailApi(productId.value)

    if (response.code === 200) {
      product.value = response.data
      isFavorited.value = response.data.isLiked || false
    } else {
      ElMessage.error(response.msg || '获取商品详情失败')
      router.push('/products')
      return
    }

    // 暂时使用空的评论数据，后续实现评论功能时再对接
    comments.value = []
  } catch (error) {
    ElMessage.error('获取商品详情失败')
  } finally {
    loading.value = false
  }
}

// 工具函数
const getCategoryLabel = (category: string) => {
  return PRODUCT_CATEGORY_LABELS[category as keyof typeof PRODUCT_CATEGORY_LABELS] || category
}

const getConditionLabel = (condition: string) => {
  return PRODUCT_CONDITION_LABELS[condition as keyof typeof PRODUCT_CONDITION_LABELS] || condition
}

const getCampusLabel = (campus: string) => {
  return CAMPUS_LABELS[campus as keyof typeof CAMPUS_LABELS] || campus
}

// 操作方法
const handleBuy = () => {
  // 检查用户是否登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再购买商品')
    router.push({ name: 'Login' })
    return
  }

  router.push({ name: 'OrderCreate', query: { productId: productId.value } })
}

const contactDialogVisible = ref(false)

// 卖家信息
const sellerInfo = computed(() => ({
  userId: product.value.sellerInfo?.userId || 0,
  nickname: product.value.sellerInfo?.nickname || '',
  avatar: product.value.sellerInfo?.avatar || '',
  phone: product.value.sellerInfo?.phone || '138****8888',
  wechat: 'user_wechat_123',
  showPhone: true,
  showWechat: true,
  dealCount: 15,
  goodRate: 98
}))

const handleContact = () => {
  // 检查用户是否登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再联系卖家')
    router.push({ name: 'Login' })
    return
  }

  contactDialogVisible.value = true
}

const handleMessageSent = () => {
  ElMessage.success('消息已发送，请等待卖家回复')
}

const handleFavorite = () => {
  isFavorited.value = !isFavorited.value
  ElMessage.success(isFavorited.value ? '收藏成功' : '取消收藏成功')
}



const handleSubmitComment = () => {
  if (!newComment.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }
  
  // 这里应该调用API提交评论
  ElMessage.success('评论发表成功')
  newComment.value = ''
}

onMounted(() => {
  fetchProductDetail()
})
</script>

<style lang="stylus" scoped>
.product-detail-container
  width 100%
  padding 20px

.product-detail-content
  display grid
  grid-template-columns 1fr 1fr
  gap 40px
  background white
  padding 30px
  border-radius 8px
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
  margin-bottom 30px

.product-images
  .image-slot
    display flex
    align-items center
    justify-content center
    height 100%
    background #f5f7fa
    color #909399

.product-info
  .product-title
    font-size 28px
    color #333
    margin-bottom 20px
    
  .product-price
    margin-bottom 20px
    
    .current-price
      font-size 32px
      color #e74c3c
      font-weight bold
      
    .original-price
      font-size 18px
      color #999
      text-decoration line-through
      margin-left 15px
      
  .product-meta
    margin-bottom 30px
    
    .meta-item
      display flex
      margin-bottom 10px
      
      .label
        width 80px
        color #666
        font-weight 500

      .category-value
        display flex
        align-items center
        gap 6px
        color #409eff
        
  .product-description
    margin-bottom 30px
    
    h3
      margin-bottom 15px
      color #333
      
    p
      line-height 1.6
      color #666
      
  .seller-info
    margin-bottom 30px
    
    h3
      margin-bottom 15px
      color #333
      
    .seller-card
      display flex
      align-items center
      gap 15px
      padding 15px
      background #f8f9fa
      border-radius 8px
      
      .seller-details
        .seller-name
          font-weight bold
          margin-bottom 5px
          
        .seller-campus
          color #666
          font-size 14px
          
  .action-buttons, .owner-actions
    display flex
    gap 15px

.comments-section
  background white
  padding 30px
  border-radius 8px
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
  
  h3
    margin-bottom 20px
    color #333
    
  .comment-form
    margin-bottom 30px
    
  .comments-list
    .comment-item
      display flex
      gap 15px
      margin-bottom 20px
      
      .comment-content
        flex 1
        
        .comment-header
          display flex
          justify-content space-between
          margin-bottom 8px
          
          .comment-author
            font-weight bold
            color #333
            
          .comment-time
            color #999
            font-size 12px
            
        .comment-text
          color #666
          line-height 1.5

@media (max-width: 768px)
  .product-detail-container
    padding 10px
    
  .product-detail-content
    grid-template-columns 1fr
    gap 20px
    padding 20px
</style>
