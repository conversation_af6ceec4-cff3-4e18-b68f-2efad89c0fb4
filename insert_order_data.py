import asyncio
import random
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目路径
import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'usm-backend'))

from app.core.database import init_db
from app.models.order import Order
from app.models.user import User
from app.models.product import Product

async def insert_test_orders():
    """插入测试订单数据"""
    await init_db()
    
    # 获取现有用户和商品
    users = await User.all()
    products = await Product.all()
    
    if len(users) < 2 or len(products) < 1:
        print("需要至少2个用户和1个商品才能创建订单")
        return
    
    # 订单状态列表
    statuses = ['pending_payment', 'pending_pickup', 'pending_confirm', 'completed', 'cancelled']
    
    # 取货地点列表
    pickup_locations = [
        '图书馆门口', '宿舍楼下', '体育馆', '食堂门口', '教学楼A座',
        '校门口', '实验楼', '行政楼', '学生活动中心', '操场'
    ]
    
    # 备注列表
    remarks = [
        '下午3点后取货', '请提前联系', '周末可取货', '上课时间勿扰',
        '已完成交易', '质量很好', '物品完好', '交易愉快', '推荐卖家', '快速交易'
    ]
    
    # 生成订单数据
    orders_to_create = []
    
    for i in range(50):  # 创建50个测试订单
        # 随机选择买家和卖家（确保不是同一人）
        buyer = random.choice(users)
        seller = random.choice([u for u in users if u.user_id != buyer.user_id])
        
        # 随机选择商品
        product = random.choice(products)
        
        # 随机生成订单号
        date_str = datetime.now().strftime('%Y%m%d')
        order_no = f"ORD{date_str}{str(i+1000).zfill(4)}"
        
        # 随机价格（基于商品价格的80%-120%）
        base_price = float(product.price)
        price_factor = random.uniform(0.8, 1.2)
        order_price = Decimal(str(round(base_price * price_factor, 2)))
        
        # 随机状态
        status = random.choice(statuses)
        
        # 随机取货地点和备注
        pickup_location = random.choice(pickup_locations)
        remark = random.choice(remarks)
        
        # 随机创建时间（最近30天内）
        days_ago = random.randint(0, 30)
        hours_ago = random.randint(0, 23)
        minutes_ago = random.randint(0, 59)
        create_time = datetime.now() - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)
        
        # 随机取货时间（如果状态需要的话）
        pickup_time = None
        if status in ['pending_pickup', 'pending_confirm', 'completed']:
            pickup_time = create_time + timedelta(hours=random.randint(1, 72))
        
        order_data = {
            'order_no': order_no,
            'product_id': product.product_id,
            'buyer_id': buyer.user_id,
            'seller_id': seller.user_id,
            'price': order_price,
            'status': status,
            'pickup_location': pickup_location,
            'pickup_time': pickup_time,
            'remark': remark,
            'create_time': create_time,
            'update_time': create_time
        }
        
        orders_to_create.append(order_data)
    
    # 批量创建订单
    try:
        for order_data in orders_to_create:
            await Order.create(**order_data)
        
        print(f"成功插入 {len(orders_to_create)} 条订单数据")
        
        # 显示统计信息
        total_orders = await Order.all().count()
        print(f"数据库中总订单数: {total_orders}")
        
        # 按状态统计
        for status in statuses:
            count = await Order.filter(status=status).count()
            print(f"{status}: {count} 个订单")
            
    except Exception as e:
        print(f"插入数据失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(insert_test_orders())
