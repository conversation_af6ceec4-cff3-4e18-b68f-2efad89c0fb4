from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional, List

from app.schemas.common import ApiResponse, PageResponse
from app.schemas.product import (
    ProductCreateRequest, ProductUpdateRequest, ProductStatusUpdateRequest,
    ProductListRequest, ProductInfo, ProductCategoryInfo
)
from app.services.product_service import product_service
from app.core.security import get_current_user, get_current_user_optional

router = APIRouter(prefix="/products", tags=["商品管理"])


@router.post("", response_model=ApiResponse[ProductInfo])
async def create_product(
    product_data: ProductCreateRequest,
    current_user: str = Depends(get_current_user)
):
    """发布商品"""
    try:
        result = await product_service.create_product(current_user, product_data)
        return ApiResponse(
            code=200,
            msg="商品创建成功",
            data=result
        )
    except HTTPException as e:
        return ApiResponse(
            code=e.status_code,
            msg=e.detail,
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"创建商品失败: {str(e)}",
            data=None
        )


@router.get("", response_model=ApiResponse[PageResponse[ProductInfo]])
async def get_products_list(
    category: Optional[str] = Query(None, description="商品分类"),
    campus: Optional[str] = Query(None, description="校区"),
    condition: Optional[str] = Query(None, description="商品成色"),
    minPrice: Optional[float] = Query(None, description="最低价格"),
    maxPrice: Optional[float] = Query(None, description="最高价格"),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    sellerUsername: Optional[str] = Query(None, description="卖家用户名"),
    startDate: Optional[str] = Query(None, description="开始日期"),
    endDate: Optional[str] = Query(None, description="结束日期"),
    status: Optional[str] = Query(None, description="商品状态"),
    isDeleted: Optional[str] = Query(None, description="是否已删除"),
    sortBy: str = Query("create_time", description="排序字段"),
    sortOrder: str = Query("desc", description="排序方向"),
    pageNum: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: str = Depends(get_current_user)
):
    """获取商品列表"""
    try:
        params = ProductListRequest(
            category=category,
            campus=campus,
            condition=condition,
            minPrice=minPrice,
            maxPrice=maxPrice,
            keyword=keyword,
            sellerUsername=sellerUsername,
            startDate=startDate,
            endDate=endDate,
            status=status,
            isDeleted=isDeleted,
            sortBy=sortBy,
            sortOrder=sortOrder,
            pageNum=pageNum,
            pageSize=pageSize
        )
        
        result = await product_service.get_products_list(params, current_user)
        
        page_response = PageResponse(
            records=result["products"],
            total=result["total"],
            pageNum=result["pageNum"],
            pageSize=result["pageSize"],
            totalPages=result["totalPages"]
        )
        
        return ApiResponse(
            code=200,
            msg="success",
            data=page_response
        )
    except HTTPException as e:
        return ApiResponse(
            code=e.status_code,
            msg=e.detail,
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"获取商品列表失败: {str(e)}",
            data=None
        )


@router.get("/my", response_model=ApiResponse[PageResponse[ProductInfo]])
async def get_my_products(
    status: Optional[str] = Query(None, description="商品状态"),
    pageNum: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: str = Depends(get_current_user)
):
    """获取我的商品"""
    try:
        result = await product_service.get_my_products(current_user, status, pageNum, pageSize)
        
        page_response = PageResponse(
            records=result["products"],
            total=result["total"],
            pageNum=result["pageNum"],
            pageSize=result["pageSize"],
            totalPages=result["totalPages"]
        )
        
        return ApiResponse(
            code=200,
            msg="success",
            data=page_response
        )
    except HTTPException as e:
        return ApiResponse(
            code=e.status_code,
            msg=e.detail,
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"获取我的商品失败: {str(e)}",
            data=None
        )


@router.get("/{product_id}", response_model=ApiResponse[ProductInfo])
async def get_product_detail(
    product_id: int,
    current_user: Optional[str] = Depends(get_current_user_optional)
):
    """获取商品详情"""
    try:
        result = await product_service.get_product_detail(product_id, current_user)
        return ApiResponse(
            code=200,
            msg="success",
            data=result
        )
    except HTTPException as e:
        return ApiResponse(
            code=e.status_code,
            msg=e.detail,
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"获取商品详情失败: {str(e)}",
            data=None
        )


@router.put("/{product_id}", response_model=ApiResponse[ProductInfo])
async def update_product(
    product_id: int,
    product_data: ProductUpdateRequest,
    current_user: str = Depends(get_current_user)
):
    """更新商品信息"""
    try:
        result = await product_service.update_product(product_id, current_user, product_data)
        return ApiResponse(
            code=200,
            msg="商品更新成功",
            data=result
        )
    except HTTPException as e:
        return ApiResponse(
            code=e.status_code,
            msg=e.detail,
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"更新商品失败: {str(e)}",
            data=None
        )


@router.put("/{product_id}/status", response_model=ApiResponse[ProductInfo])
async def update_product_status(
    product_id: int,
    status_data: ProductStatusUpdateRequest,
    current_user: str = Depends(get_current_user)
):
    """更新商品状态"""
    try:
        result = await product_service.update_product_status(product_id, current_user, status_data)
        return ApiResponse(
            code=200,
            msg="商品状态更新成功",
            data=result
        )
    except HTTPException as e:
        return ApiResponse(
            code=e.status_code,
            msg=e.detail,
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"更新商品状态失败: {str(e)}",
            data=None
        )


@router.delete("/{product_id}", response_model=ApiResponse[bool])
async def delete_product(
    product_id: int,
    current_user: str = Depends(get_current_user)
):
    """删除商品"""
    try:
        result = await product_service.delete_product(product_id, current_user)
        return ApiResponse(
            code=200,
            msg="商品删除成功",
            data=result
        )
    except HTTPException as e:
        return ApiResponse(
            code=e.status_code,
            msg=e.detail,
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"删除商品失败: {str(e)}",
            data=None
        )


@router.post("/{product_id}/restore", response_model=ApiResponse[bool])
async def restore_product(
    product_id: int,
    current_user: str = Depends(get_current_user)
):
    """恢复已删除的商品（仅管理员）"""
    try:
        result = await product_service.restore_product(product_id, current_user)
        return ApiResponse(
            code=200,
            msg="商品恢复成功",
            data=result
        )
    except HTTPException as e:
        return ApiResponse(
            code=e.status_code,
            msg=e.detail,
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"恢复商品失败: {str(e)}",
            data=None
        )


@router.get("/categories/list", response_model=ApiResponse[List[ProductCategoryInfo]])
async def get_categories():
    """获取商品分类列表"""
    try:
        result = await product_service.get_categories()
        return ApiResponse(
            code=200,
            msg="success",
            data=result
        )
    except HTTPException as e:
        return ApiResponse(
            code=e.status_code,
            msg=e.detail,
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"获取分类列表失败: {str(e)}",
            data=None
        )
