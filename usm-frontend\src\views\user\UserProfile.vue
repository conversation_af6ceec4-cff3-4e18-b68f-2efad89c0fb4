<template>
  <div class="profile-container">
    <div class="profile-content">
      <!-- 个人信息卡片 -->
      <el-card class="profile-card">
        <div class="profile-header">
          <div class="avatar-section">
            <el-avatar :src="userInfo.avatar" :size="80">
              {{ userInfo.nickname?.charAt(0) }}
            </el-avatar>
            <el-button size="small" @click="handleAvatarUpload">
              更换头像
            </el-button>
          </div>
          <div class="basic-info">
            <h2>{{ userInfo.nickname }}</h2>
            <p class="username">@{{ userInfo.username }}</p>
            <div class="verification-status">
              <el-tag v-if="userInfo.isVerified" type="success">
                <el-icon><Check /></el-icon>
                已认证
              </el-tag>
              <el-tag v-else type="warning">
                <el-icon><Warning /></el-icon>
                未认证
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 个人资料编辑 -->
      <el-card class="edit-card">
        <template #header>
          <span>个人资料</span>
        </template>
        
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
        >
          <el-form-item label="昵称" prop="nickname">
            <el-input v-model="form.nickname" placeholder="请输入昵称" />
          </el-form-item>
          
          <el-form-item label="真实姓名" prop="realName">
            <el-input v-model="form.realName" placeholder="请输入真实姓名" />
          </el-form-item>
          
          <el-form-item label="学号" prop="studentId">
            <el-input v-model="form.studentId" placeholder="请输入学号" />
          </el-form-item>
          
          <el-form-item label="性别" prop="sex">
            <el-radio-group v-model="form.sex">
              <el-radio label="0">男</el-radio>
              <el-radio label="1">女</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入手机号" />
          </el-form-item>
          
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱" />
          </el-form-item>
          
          <el-form-item label="所在校区" prop="campus">
            <el-select v-model="form.campus" placeholder="请选择校区">
              <el-option
                v-for="(label, value) in CAMPUS_LABELS"
                :key="value"
                :label="label"
                :value="value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSave" :loading="loading">
              保存修改
            </el-button>
            <el-button @click="handleReset">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

    

      <!-- 邮箱绑定 -->
      <el-card class="security-card">
        <template #header>
          <span>账户安全</span>
        </template>
        
        <div class="security-items">
          <div class="security-item">
            <div class="item-info">
              <h4>登录密码</h4>
              <p>定期更换密码可以提高账户安全性</p>
            </div>
            <el-button @click="handleChangePassword">修改密码</el-button>
          </div>
          
          <div class="security-item">
            <div class="item-info">
              <h4>手机绑定</h4>
              <p>{{ userInfo.phone ? `已绑定：${userInfo.phone}` : '未绑定手机号' }}</p>
            </div>
            <el-button @click="handleChangePhone">
              {{ userInfo.phone ? '更换' : '绑定' }}
            </el-button>
          </div>
          
          <div class="security-item">
            <div class="item-info">
              <h4>邮箱绑定</h4>
              <p>{{ userInfo.email ? `已绑定：${userInfo.email}` : '未绑定邮箱' }}</p>
            </div>
            <el-button @click="handleChangeEmail">
              {{ userInfo.email ? '更换' : '绑定' }}
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog v-model="passwordDialogVisible" title="修改密码" width="400px">
      <el-form :model="passwordForm" label-width="100px">
        <el-form-item label="当前密码">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            show-password
            placeholder="请输入当前密码"
          />
        </el-form-item>
        <el-form-item label="新密码">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
            placeholder="请输入新密码（至少6位）"
          />
        </el-form-item>
        <el-form-item label="确认密码">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
            placeholder="请再次输入新密码"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="passwordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitPasswordChange">确认修改</el-button>
      </template>
    </el-dialog>

    <!-- 更换手机号对话框 -->
    <el-dialog v-model="phoneDialogVisible" title="更换手机号" width="400px">
      <el-form :model="phoneForm" label-width="100px">
        <el-form-item label="新手机号">
          <el-input
            v-model="phoneForm.newPhone"
            placeholder="请输入新手机号"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="phoneDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitPhoneChange">确认更换</el-button>
      </template>
    </el-dialog>

    <!-- 更换邮箱对话框 -->
    <el-dialog v-model="emailDialogVisible" title="更换邮箱" width="400px">
      <el-form :model="emailForm" label-width="100px">
        <el-form-item label="新邮箱">
          <el-input
            v-model="emailForm.newEmail"
            placeholder="请输入新邮箱地址"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="emailDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEmailChange">确认更换</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Warning, Upload } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores'
import { CAMPUS_LABELS } from '@/constants'
import { validatePhone, validateEmail } from '@/utils'
import type { FormInstance } from 'element-plus'

const userStore = useUserStore()
const formRef = ref<FormInstance>()
const loading = ref(false)

const userInfo = computed(() => userStore.userInfo || {})

// 表单数据
const form = reactive({
  nickname: '',
  realName: '',
  studentId: '',
  sex: '0',
  phone: '',
  email: '',
  campus: ''
})

// 表单验证规则
const rules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { validator: (rule: any, value: string, callback: any) => {
      if (value && !validatePhone(value)) {
        callback(new Error('请输入正确的手机号'))
      } else {
        callback()
      }
    }, trigger: 'blur' }
  ],
  email: [
    { validator: (rule: any, value: string, callback: any) => {
      if (value && !validateEmail(value)) {
        callback(new Error('请输入正确的邮箱地址'))
      } else {
        callback()
      }
    }, trigger: 'blur' }
  ]
}

// 初始化表单数据
const initForm = () => {
  form.nickname = userInfo.value.nickname || ''
  form.realName = userInfo.value.realName || ''
  form.studentId = userInfo.value.studentId || ''
  form.sex = userInfo.value.sex || '0'
  form.phone = userInfo.value.phone || ''
  form.email = userInfo.value.email || ''
  form.campus = userInfo.value.campus || ''
}

// 保存修改
const handleSave = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        // 这里应该调用API更新用户信息
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        userStore.updateUserInfo(form)
        ElMessage.success('保存成功')
      } catch (error) {
        ElMessage.error('保存失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
  })
}

// 重置表单
const handleReset = () => {
  initForm()
}

// 头像上传
const handleAvatarUpload = (file: any) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
    return false
  }

  // 模拟上传
  const reader = new FileReader()
  reader.onload = (e) => {
    userInfo.avatar = e.target?.result as string
    ElMessage.success('头像上传成功')
  }
  reader.readAsDataURL(file)
  return false // 阻止自动上传
}

// 学生证上传
const handleStudentCardUpload = (file: any) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('学生证只能上传图片格式!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('学生证图片大小不能超过 5MB!')
    return false
  }

  // 模拟上传
  const reader = new FileReader()
  reader.onload = (e) => {
    userInfo.studentIdImage = e.target?.result as string
    ElMessage.success('学生证上传成功，等待审核')
  }
  reader.readAsDataURL(file)
  return false // 阻止自动上传
}

// 提交认证
const handleSubmitVerification = async () => {
  if (!userInfo.studentIdImage) {
    ElMessage.warning('请先上传学生证')
    return
  }

  try {
    // 模拟提交认证
    await new Promise(resolve => setTimeout(resolve, 2000))

    userInfo.verificationStatus = 'pending'
    ElMessage.success('身份认证申请已提交，请等待审核')
  } catch (error) {
    ElMessage.error('提交失败，请重试')
  }
}

// 修改密码
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})
const passwordDialogVisible = ref(false)

const handleChangePassword = () => {
  passwordDialogVisible.value = true
}

const submitPasswordChange = async () => {
  if (!passwordForm.currentPassword) {
    ElMessage.warning('请输入当前密码')
    return
  }
  if (!passwordForm.newPassword) {
    ElMessage.warning('请输入新密码')
    return
  }
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    ElMessage.warning('两次输入的密码不一致')
    return
  }
  if (passwordForm.newPassword.length < 6) {
    ElMessage.warning('新密码长度不能少于6位')
    return
  }

  try {
    // 模拟修改密码
    await new Promise(resolve => setTimeout(resolve, 1500))

    ElMessage.success('密码修改成功')
    passwordDialogVisible.value = false
    // 重置表单
    Object.assign(passwordForm, {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
  } catch (error) {
    ElMessage.error('密码修改失败，请重试')
  }
}

// 更换手机号
const phoneForm = reactive({
  newPhone: ''
})
const phoneDialogVisible = ref(false)

const handleChangePhone = () => {
  phoneDialogVisible.value = true
}

const submitPhoneChange = async () => {
  if (!phoneForm.newPhone) {
    ElMessage.warning('请输入新手机号')
    return
  }
  if (!/^1[3-9]\d{9}$/.test(phoneForm.newPhone)) {
    ElMessage.warning('请输入正确的手机号')
    return
  }

  try {
    // 模拟更换手机号
    await new Promise(resolve => setTimeout(resolve, 1500))

    userInfo.phone = phoneForm.newPhone
    ElMessage.success('手机号更换成功')
    phoneDialogVisible.value = false
    // 重置表单
    Object.assign(phoneForm, {
      newPhone: ''
    })
  } catch (error) {
    ElMessage.error('手机号更换失败，请重试')
  }
}

// 更换邮箱
const emailForm = reactive({
  newEmail: ''
})
const emailDialogVisible = ref(false)

const handleChangeEmail = () => {
  emailDialogVisible.value = true
}

const submitEmailChange = async () => {
  if (!emailForm.newEmail) {
    ElMessage.warning('请输入新邮箱')
    return
  }
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailForm.newEmail)) {
    ElMessage.warning('请输入正确的邮箱地址')
    return
  }

  try {
    // 模拟更换邮箱
    await new Promise(resolve => setTimeout(resolve, 1500))

    userInfo.email = emailForm.newEmail
    ElMessage.success('邮箱更换成功')
    emailDialogVisible.value = false
    // 重置表单
    Object.assign(emailForm, {
      newEmail: ''
    })
  } catch (error) {
    ElMessage.error('邮箱更换失败，请重试')
  }
}

onMounted(() => {
  initForm()
})
</script>

<style lang="stylus" scoped>
.profile-container
  width 100%
  padding 20px

.profile-content
  display flex
  flex-direction column
  gap 20px

.profile-card
  .profile-header
    display flex
    align-items center
    gap 30px
    
    .avatar-section
      display flex
      flex-direction column
      align-items center
      gap 10px
      
    .basic-info
      flex 1
      
      h2
        margin-bottom 8px
        color #333
        
      .username
        color #666
        margin-bottom 10px
        
      .verification-status
        .el-tag
          .el-icon
            margin-right 4px

.edit-card, .verification-card, .security-card
  .el-card__body
    padding 30px

.verification-content
  .el-form-item
    margin-bottom 20px

.security-items
  .security-item
    display flex
    justify-content space-between
    align-items center
    padding 20px 0
    border-bottom 1px solid #f0f0f0
    
    &:last-child
      border-bottom none
      
    .item-info
      h4
        margin-bottom 5px
        color #333
        
      p
        color #666
        font-size 14px

@media (max-width: 768px)
  .profile-container
    padding 10px
    
  .profile-header
    flex-direction column
    text-align center
    gap 20px
    
  .security-item
    flex-direction column
    align-items flex-start
    gap 10px
</style>
