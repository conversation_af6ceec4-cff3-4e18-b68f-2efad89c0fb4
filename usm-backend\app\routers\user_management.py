from typing import Optional
from fastapi import APIRouter, Depends, Query
from fastapi.security import HTT<PERSON>Bearer

from app.routers.auth import get_current_user
from app.schemas.common import ApiResponse, PageResponse
from app.services.user_management_service import user_management_service
from app.schemas.user_management import (
    UserListRequest, UserUpdateRequest, UserStatusUpdateRequest,
    UserInfo, UserStatistics, BatchUserOperationRequest
)

router = APIRouter(prefix="/users", tags=["用户管理"])
security = HTTPBearer()


@router.get("/list", response_model=ApiResponse[PageResponse[UserInfo]])
async def get_users_list(
    username: Optional[str] = Query(None, description="用户名筛选"),
    role: Optional[str] = Query(None, description="角色筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    campus: Optional[str] = Query(None, description="校区筛选"),
    email: Optional[str] = Query(None, description="邮箱筛选"),
    phone: Optional[str] = Query(None, description="手机号筛选"),
    sortBy: str = Query("create_time", description="排序字段"),
    sortOrder: str = Query("desc", description="排序方向"),
    pageNum: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: str = Depends(get_current_user)
):
    """获取用户列表"""
    params = UserListRequest(
        username=username,
        role=role,
        status=status,
        campus=campus,
        email=email,
        phone=phone,
        sortBy=sortBy,
        sortOrder=sortOrder,
        pageNum=pageNum,
        pageSize=pageSize
    )
    
    result = await user_management_service.get_users_list(params, current_user)
    
    return ApiResponse(
        code=200,
        msg="获取用户列表成功",
        data=PageResponse(
            records=result["records"],
            total=result["total"],
            pageNum=result["pageNum"],
            pageSize=result["pageSize"],
            totalPages=result["totalPages"]
        )
    )


@router.get("/{user_id}", response_model=ApiResponse[UserInfo])
async def get_user_detail(
    user_id: int,
    current_user: str = Depends(get_current_user)
):
    """获取用户详情"""
    user_info = await user_management_service.get_user_detail(user_id, current_user)
    
    return ApiResponse(
        code=200,
        msg="获取用户详情成功",
        data=user_info
    )


@router.put("/{user_id}", response_model=ApiResponse[UserInfo])
async def update_user(
    user_id: int,
    user_data: UserUpdateRequest,
    current_user: str = Depends(get_current_user)
):
    """更新用户信息"""
    user_info = await user_management_service.update_user(user_id, user_data, current_user)
    
    return ApiResponse(
        code=200,
        msg="用户信息更新成功",
        data=user_info
    )


@router.put("/{user_id}/status", response_model=ApiResponse[UserInfo])
async def update_user_status(
    user_id: int,
    status_data: UserStatusUpdateRequest,
    current_user: str = Depends(get_current_user)
):
    """更新用户状态"""
    user_info = await user_management_service.update_user_status(user_id, status_data, current_user)
    
    return ApiResponse(
        code=200,
        msg="用户状态更新成功",
        data=user_info
    )


@router.delete("/{user_id}", response_model=ApiResponse[bool])
async def delete_user(
    user_id: int,
    current_user: str = Depends(get_current_user)
):
    """删除用户"""
    success = await user_management_service.delete_user(user_id, current_user)
    
    return ApiResponse(
        code=200,
        msg="用户删除成功",
        data=success
    )


@router.get("/statistics/overview", response_model=ApiResponse[UserStatistics])
async def get_user_statistics(
    current_user: str = Depends(get_current_user)
):
    """获取用户统计信息"""
    stats = await user_management_service.get_user_statistics(current_user)
    
    return ApiResponse(
        code=200,
        msg="获取统计信息成功",
        data=stats
    )


@router.post("/batch", response_model=ApiResponse[dict])
async def batch_user_operation(
    operation_data: BatchUserOperationRequest,
    current_user: str = Depends(get_current_user)
):
    """批量用户操作"""
    result = await user_management_service.batch_user_operation(operation_data, current_user)
    
    return ApiResponse(
        code=200,
        msg=f"批量操作完成，成功{result['successCount']}个，失败{result['failedCount']}个",
        data=result
    )
