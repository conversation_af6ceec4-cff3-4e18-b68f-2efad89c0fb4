from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from pydantic import ValidationError
import traceback
from typing import Union

from app.schemas.common import ApiResponse


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """处理HTTP异常"""
    return JSONResponse(
        status_code=exc.status_code,
        content=ApiResponse(
            code=exc.status_code,
            msg=exc.detail,
            data={"detail": exc.detail}
        ).model_dump()
    )


async def starlette_http_exception_handler(request: Request, exc: StarletteHTTPException) -> JSONResponse:
    """处理Starlette HTTP异常"""
    return JSONResponse(
        status_code=exc.status_code,
        content=ApiResponse(
            code=exc.status_code,
            msg=exc.detail,
            data={"detail": exc.detail}
        ).model_dump()
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """处理请求验证异常"""
    errors = []
    for error in exc.errors():
        errors.append({
            "field": " -> ".join(str(x) for x in error["loc"]),
            "message": error["msg"],
            "type": error["type"]
        })
    
    return JSONResponse(
        status_code=422,
        content=ApiResponse(
            code=422,
            msg="请求参数验证失败",
            data={
                "detail": "Validation Error",
                "errors": errors
            }
        ).model_dump()
    )


async def pydantic_validation_exception_handler(request: Request, exc: ValidationError) -> JSONResponse:
    """处理Pydantic验证异常"""
    errors = []
    for error in exc.errors():
        errors.append({
            "field": " -> ".join(str(x) for x in error["loc"]),
            "message": error["msg"],
            "type": error["type"]
        })
    
    return JSONResponse(
        status_code=422,
        content=ApiResponse(
            code=422,
            msg="数据验证失败",
            data={
                "detail": "Pydantic Validation Error",
                "errors": errors
            }
        ).model_dump()
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """处理通用异常"""
    # 记录异常信息（在生产环境中应该使用日志系统）
    print(f"Unhandled exception: {exc}")
    print(f"Traceback: {traceback.format_exc()}")
    
    return JSONResponse(
        status_code=500,
        content=ApiResponse(
            code=500,
            msg="服务器内部错误",
            data={
                "detail": "Internal Server Error",
                "error": str(exc) if str(exc) else "Unknown error"
            }
        ).model_dump()
    )
