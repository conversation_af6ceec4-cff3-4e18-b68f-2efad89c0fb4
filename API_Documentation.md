# 校园二手交易平台 API 文档

## 基础信息

- **Base URL**: `http://localhost:18080/api`
- **认证方式**: JWT <PERSON> (Header: `Authorization Bearer {{token}}`)
- **数据格式**: JSON
- **响应格式**: 统一响应格式

### 统一响应格式

```json
{
  "code": 200,
  "msg": "success",
  "data": {}
}
```

### 分页响应格式

```json
{
  "code": 200,
  "msg": "success", 
  "data": {
    "list": [],
    "total": 100,
    "pageNum": 1,
    "pageSize": 20
  }
}
```

## 1. 认证模块

### 1.1 用户登录

**接口**: `POST /login`

**请求参数**:
```json
{
  "username": "student",
  "password": "123456"
}
```

**响应**:
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 1.2 用户注册

**接口**: `POST /register`

**请求参数**:
```json
{
  "username": "newuser",
  "nickname": "新用户",
  "password": "123456",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "sex": "0",
  "roleKey": "student"
}
```

### 1.3 用户登出

**接口**: `POST /logout`

**需要认证**: 是

### 1.4 获取用户信息

**接口**: `GET /user/getInfo`

**需要认证**: 是

**响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "userId": 1,
    "username": "student",
    "nickname": "学生小明",
    "avatar": "",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "sex": "0",
    "status": "active",
    "roleKey": "student",
    "campus": "main",
    "studentId": "2021001",
    "realName": "张三",
    "isVerified": true,
    "createTime": "2024-01-15T10:30:00",
    "updateTime": "2024-01-15T10:30:00"
  }
}
```

### 1.5 获取角色列表

**接口**: `GET /roleList`

**响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": [
    {
      "roleId": 1,
      "roleName": "学生",
      "roleKey": "student"
    },
    {
      "roleId": 2,
      "roleName": "管理员", 
      "roleKey": "admin"
    }
  ]
}
```

## 2. 商品模块

### 2.1 获取商品列表

**接口**: `GET /products`

**查询参数**:
- `keyword` (string, optional): 搜索关键词
- `category` (string, optional): 商品分类
- `minPrice` (number, optional): 最低价格
- `maxPrice` (number, optional): 最高价格
- `condition` (string, optional): 新旧程度
- `campus` (string, optional): 校区
- `sortBy` (string, optional): 排序字段 (createTime/price/viewCount)
- `sortOrder` (string, optional): 排序方向 (asc/desc)
- `pageNum` (number, optional): 页码，默认1
- `pageSize` (number, optional): 每页数量，默认20

**需要认证**: 是

**响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "list": [
      {
        "productId": 1,
        "title": "高等数学教材",
        "description": "同济大学出版社，九成新",
        "category": "textbook",
        "price": 25,
        "originalPrice": 45,
        "condition": "good",
        "images": ["/uploads/product1.jpg"],
        "sellerId": 1,
        "sellerInfo": {
          "userId": 1,
          "nickname": "小明",
          "avatar": ""
        },
        "campus": "main",
        "location": "图书馆附近",
        "status": "published",
        "viewCount": 156,
        "likeCount": 23,
        "isLiked": false,
        "tags": ["教材", "数学"],
        "createTime": "2024-01-15T10:30:00",
        "updateTime": "2024-01-15T10:30:00"
      }
    ],
    "total": 100,
    "pageNum": 1,
    "pageSize": 20
  }
}
```

### 2.2 获取商品详情

**接口**: `GET /products/{productId}`

**需要认证**: 是

**响应**: 返回单个商品详细信息，格式同商品列表中的单项

### 2.3 发布商品

**接口**: `POST /products`

**需要认证**: 是

**请求参数**:
```json
{
  "title": "二手iPhone 13",
  "description": "9成新，无磕碰",
  "category": "digital",
  "price": 4500,
  "originalPrice": 6999,
  "condition": "like_new",
  "images": ["/uploads/phone1.jpg", "/uploads/phone2.jpg"],
  "campus": "main",
  "location": "宿舍楼下",
  "tags": ["手机", "苹果"]
}
```

### 2.4 更新商品

**接口**: `PUT /products/{productId}`

**需要认证**: 是

**请求参数**: 同发布商品，字段可选

### 2.5 删除商品

**接口**: `DELETE /products/{productId}`

**需要认证**: 是

### 2.6 获取我的商品

**接口**: `GET /products/my`

**查询参数**:
- `status` (string, optional): 商品状态
- `pageNum` (number, optional): 页码
- `pageSize` (number, optional): 每页数量

**需要认证**: 是

### 2.7 商品收藏/取消收藏

**接口**: `POST /products/{productId}/favorite`

**需要认证**: 是

### 2.8 获取收藏列表

**接口**: `GET /products/favorites`

**需要认证**: 是

## 3. 订单模块

### 3.1 创建订单

**接口**: `POST /orders`

**需要认证**: 是

**请求参数**:
```json
{
  "productId": 1,
  "pickupLocation": "图书馆门口",
  "remark": "下午3点后"
}
```

### 3.2 获取订单列表

**接口**: `GET /orders`

**查询参数**:
- `status` (string, optional): 订单状态
- `type` (string, optional): 订单类型 (buy/sell)
- `pageNum` (number, optional): 页码
- `pageSize` (number, optional): 每页数量

**需要认证**: 是

### 3.3 获取订单详情

**接口**: `GET /orders/{orderId}`

**需要认证**: 是

### 3.4 更新订单状态

**接口**: `PUT /orders/{orderId}/status`

**需要认证**: 是

**请求参数**:
```json
{
  "status": "completed",
  "remark": "交易完成"
}
```

### 3.5 取消订单

**接口**: `DELETE /orders/{orderId}`

**需要认证**: 是

## 4. 求购模块

### 4.1 获取求购列表

**接口**: `GET /wanted`

**查询参数**:
- `keyword` (string, optional): 搜索关键词
- `category` (string, optional): 分类
- `campus` (string, optional): 校区
- `pageNum` (number, optional): 页码
- `pageSize` (number, optional): 每页数量

**需要认证**: 是

### 4.2 发布求购信息

**接口**: `POST /wanted`

**需要认证**: 是

**请求参数**:
```json
{
  "title": "求购线性代数教材",
  "description": "需要同济版本，八成新以上",
  "category": "textbook",
  "maxPrice": 30,
  "campus": "main"
}
```

### 4.3 获取求购详情

**接口**: `GET /wanted/{wantedId}`

**需要认证**: 是

### 4.4 删除求购信息

**接口**: `DELETE /wanted/{wantedId}`

**需要认证**: 是

## 5. 消息模块

### 5.1 获取消息列表

**接口**: `GET /messages`

**查询参数**:
- `type` (string, optional): 消息类型 (system/trade/user)
- `isRead` (boolean, optional): 是否已读
- `pageNum` (number, optional): 页码
- `pageSize` (number, optional): 每页数量

**需要认证**: 是

### 5.2 标记消息已读

**接口**: `PUT /messages/{messageId}/read`

**需要认证**: 是

### 5.3 删除消息

**接口**: `DELETE /messages/{messageId}`

**需要认证**: 是

### 5.4 发送消息

**接口**: `POST /messages`

**需要认证**: 是

**请求参数**:
```json
{
  "receiverId": 2,
  "title": "关于商品咨询",
  "content": "请问这个商品还在吗？",
  "type": "user",
  "relatedId": 1
}
```

## 6. 文件上传模块

### 6.1 上传图片

**接口**: `POST /upload/image`

**需要认证**: 是

**请求**: multipart/form-data
- `file`: 图片文件

**响应**:
```json
{
  "code": 200,
  "msg": "上传成功",
  "data": {
    "url": "/uploads/20240115/image123.jpg",
    "filename": "image123.jpg",
    "size": 1024000
  }
}
```

## 7. 用户模块

### 7.1 更新用户信息

**接口**: `PUT /user/profile`

**需要认证**: 是

**请求参数**:
```json
{
  "nickname": "新昵称",
  "avatar": "/uploads/avatar.jpg",
  "phone": "13800138001",
  "email": "<EMAIL>"
}
```

### 7.2 修改密码

**接口**: `PUT /user/password`

**需要认证**: 是

**请求参数**:
```json
{
  "oldPassword": "123456",
  "newPassword": "654321"
}
```

### 7.3 获取用户详情

**接口**: `GET /users/{userId}`

**需要认证**: 是

## 8. 评论模块

### 8.1 获取商品评论

**接口**: `GET /products/{productId}/comments`

**查询参数**:
- `pageNum` (number, optional): 页码
- `pageSize` (number, optional): 每页数量

**需要认证**: 是

### 8.2 发表评论

**接口**: `POST /products/{productId}/comments`

**需要认证**: 是

**请求参数**:
```json
{
  "content": "商品不错，推荐购买",
  "parentId": null
}
```

### 8.3 删除评论

**接口**: `DELETE /comments/{commentId}`

**需要认证**: 是

## 9. 举报模块

### 9.1 提交举报

**接口**: `POST /reports`

**需要认证**: 是

**请求参数**:
```json
{
  "type": "fake_product",
  "targetType": "product",
  "targetId": 1,
  "reason": "虚假商品",
  "description": "商品描述与实际不符"
}
```

### 9.2 获取举报列表 (管理员)

**接口**: `GET /admin/reports`

**需要认证**: 是 (管理员权限)

## 10. 申诉模块

### 10.1 提交申诉

**接口**: `POST /appeals`

**需要认证**: 是

**请求参数**:
```json
{
  "type": "product_removal",
  "title": "商品被误删申诉",
  "description": "我的商品被误删，请求恢复",
  "relatedId": 1,
  "relatedType": "product",
  "evidence": ["/uploads/evidence1.jpg"]
}
```

### 10.2 获取申诉列表 (管理员)

**接口**: `GET /admin/appeals`

**需要认证**: 是 (管理员权限)

## 11. 管理员模块

### 11.1 获取统计数据

**接口**: `GET /admin/statistics`

**需要认证**: 是 (管理员权限)

**响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "totalProducts": 1250,
    "totalOrders": 856,
    "totalUsers": 2341,
    "todayOrders": 23,
    "todayRevenue": 1580.50,
    "popularCategories": [
      {
        "category": "textbook",
        "count": 450
      }
    ],
    "recentActivities": [
      {
        "type": "order",
        "description": "用户小明购买了商品",
        "time": "2024-01-20T10:30:00"
      }
    ]
  }
}
```

### 11.2 用户管理

**获取用户列表**: `GET /admin/users`
**更新用户状态**: `PUT /admin/users/{userId}/status`
**删除用户**: `DELETE /admin/users/{userId}`

### 11.3 商品管理

**获取所有商品**: `GET /admin/products`
**审核商品**: `PUT /admin/products/{productId}/review`
**下架商品**: `PUT /admin/products/{productId}/remove`

### 11.4 订单管理

**获取所有订单**: `GET /admin/orders`
**处理订单争议**: `PUT /admin/orders/{orderId}/dispute`

## 状态码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未认证或token过期
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 常量定义

### 商品分类
- `textbook`: 教材书籍
- `digital`: 数码电子
- `clothing`: 服装配饰
- `daily`: 生活用品
- `sports`: 运动器材
- `beauty`: 美妆护肤
- `food`: 食品零食
- `other`: 其他

### 商品状态
- `draft`: 草稿
- `published`: 已发布
- `sold`: 已售出
- `removed`: 已下架

### 商品新旧程度
- `new`: 全新
- `like_new`: 几乎全新
- `good`: 良好
- `fair`: 一般
- `poor`: 较差

### 订单状态
- `pending_payment`: 待付款
- `pending_pickup`: 待取货
- `pending_confirm`: 待确认
- `completed`: 已完成
- `cancelled`: 已取消

### 用户角色
- `student`: 学生
- `admin`: 管理员
- `super_admin`: 超级管理员

### 校区
- `main`: 主校区
- `north`: 北校区
- `south`: 南校区
- `east`: 东校区
- `west`: 西校区
