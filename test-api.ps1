# 商品接口测试脚本

Write-Output "🧪 开始测试商品接口..."

# 1. 登录获取Token
Write-Output "`n1️⃣ 测试登录接口"
try {
    $loginBody = '{"username": "admin", "password": "123456"}'
    $loginResponse = Invoke-WebRequest -Uri 'http://localhost:18080/api/login' -Method POST -ContentType 'application/json' -Body $loginBody
    $loginData = $loginResponse.Content | ConvertFrom-Json
    $token = $loginData.data.token
    $headers = @{"Authorization" = "Bearer $token"}
    
    Write-Output "✅ 登录成功: $($loginResponse.StatusCode)"
    Write-Output "Token: $($token.Substring(0,20))..."
} catch {
    Write-Output "❌ 登录失败: $($_.Exception.Message)"
    exit 1
}

# 2. 测试基础商品列表
Write-Output "`n2️⃣ 测试基础商品列表"
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:18080/api/products' -Method GET -Headers $headers
    $data = $response.Content | ConvertFrom-Json
    Write-Output "✅ 商品列表: $($response.StatusCode)"
    Write-Output "总商品数: $($data.data.total)"
    Write-Output "当前页商品数: $($data.data.records.Count)"
} catch {
    Write-Output "❌ 商品列表失败: $($_.Exception.Message)"
}

# 3. 测试分类筛选
Write-Output "`n3️⃣ 测试分类筛选"
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:18080/api/products?category=textbook' -Method GET -Headers $headers
    $data = $response.Content | ConvertFrom-Json
    Write-Output "✅ 分类筛选: $($response.StatusCode)"
    Write-Output "教材类商品数: $($data.data.total)"
} catch {
    Write-Output "❌ 分类筛选失败: $($_.Exception.Message)"
}

# 4. 测试价格筛选
Write-Output "`n4️⃣ 测试价格筛选"
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:18080/api/products?minPrice=100&maxPrice=1000' -Method GET -Headers $headers
    $data = $response.Content | ConvertFrom-Json
    Write-Output "✅ 价格筛选: $($response.StatusCode)"
    Write-Output "价格区间商品数: $($data.data.total)"
} catch {
    Write-Output "❌ 价格筛选失败: $($_.Exception.Message)"
}

# 5. 测试关键词搜索
Write-Output "`n5️⃣ 测试关键词搜索"
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:18080/api/products?keyword=iPhone' -Method GET -Headers $headers
    $data = $response.Content | ConvertFrom-Json
    Write-Output "✅ 关键词搜索: $($response.StatusCode)"
    Write-Output "iPhone搜索结果: $($data.data.total)"
} catch {
    Write-Output "❌ 关键词搜索失败: $($_.Exception.Message)"
}

# 6. 测试分页
Write-Output "`n6️⃣ 测试分页功能"
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:18080/api/products?pageNum=1&pageSize=2' -Method GET -Headers $headers
    $data = $response.Content | ConvertFrom-Json
    Write-Output "✅ 分页功能: $($response.StatusCode)"
    Write-Output "第1页，每页2条: 实际返回$($data.data.records.Count)条"
    Write-Output "总页数: $($data.data.totalPages)"
} catch {
    Write-Output "❌ 分页功能失败: $($_.Exception.Message)"
}

# 7. 测试商品分类列表
Write-Output "`n7️⃣ 测试商品分类列表"
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:18080/api/products/categories/list' -Method GET
    $data = $response.Content | ConvertFrom-Json
    Write-Output "✅ 分类列表: $($response.StatusCode)"
    Write-Output "可用分类数: $($data.data.Count)"
    Write-Output "分类列表:"
    foreach($category in $data.data) {
        Write-Output "  - $($category.categoryName) ($($category.categoryKey))"
    }
} catch {
    Write-Output "❌ 分类列表失败: $($_.Exception.Message)"
}

Write-Output "`n🎉 测试完成！"
