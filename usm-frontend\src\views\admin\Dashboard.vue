<template>
  <div class="admin-dashboard">
    <div class="dashboard-header">
      <h2>管理后台</h2>
      <div class="header-actions">
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon user-icon">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.totalUsers }}</div>
            <div class="stat-label">总用户数</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon product-icon">
            <el-icon><Goods /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.totalProducts }}</div>
            <div class="stat-label">商品总数</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon order-icon">
            <el-icon><ShoppingCart /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.totalOrders }}</div>
            <div class="stat-label">订单总数</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon revenue-icon">
            <el-icon><Money /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">¥{{ stats.totalRevenue }}</div>
            <div class="stat-label">交易总额</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon appeal-icon">
            <el-icon><ChatDotSquare /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.pendingAppeals }}</div>
            <div class="stat-label">用户申诉</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <el-card>
        <template #header>
          <span>快捷操作</span>
        </template>

        <div class="action-buttons">
          <el-button type="primary" @click="goToUserManagement">
            <el-icon><User /></el-icon>
            用户管理
          </el-button>
          <el-button type="success" @click="goToProductManagement">
            <el-icon><Goods /></el-icon>
            商品管理
          </el-button>
          <el-button type="warning" @click="goToOrderManagement">
            <el-icon><ShoppingCart /></el-icon>
            订单管理
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="chart-header">
                <span>用户注册趋势</span>
                <el-select v-model="userChartPeriod" size="small" style="width: 100px">
                  <el-option label="7天" value="7d" />
                  <el-option label="30天" value="30d" />
                  <el-option label="90天" value="90d" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <v-chart :option="userTrendOption" style="height: 300px;" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>商品分类分布</span>
            </template>
            <div class="chart-container">
              <v-chart :option="categoryDistributionOption" style="height: 300px;" />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 待处理事项 -->
    <div class="pending-section">
      <el-card>
        <template #header>
          <span>待处理事项</span>
        </template>
        
        <el-tabs v-model="activeTab">
          <el-tab-pane label="待审核商品" name="products">
            <div class="pending-list">
              <div v-for="item in pendingProducts" :key="item.productId" class="pending-item">
                <div class="item-info">
                  <el-image :src="item.images[0]" fit="cover" class="item-image">
                    <template #error>
                      <div class="image-slot">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                  <div class="item-details">
                    <h4>{{ item.title }}</h4>
                    <p>价格：¥{{ item.price }}</p>
                    <p>发布者：{{ item.sellerInfo?.nickname }}</p>
                  </div>
                </div>
                <div class="item-actions">
                  <el-button type="success" size="small" @click="handleApprove(item.productId)">
                    通过
                  </el-button>
                  <el-button type="danger" size="small" @click="handleReject(item.productId)">
                    拒绝
                  </el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="举报处理" name="reports">
            <div class="pending-list">
              <div v-for="report in pendingReports" :key="report.reportId" class="pending-item">
                <div class="item-info">
                  <div class="report-details">
                    <h4>{{ report.reason }}</h4>
                    <p>举报内容：{{ report.content }}</p>
                    <p>举报者：{{ report.reporterInfo?.nickname }}</p>
                    <p>举报时间：{{ formatTime(report.createTime) }}</p>
                  </div>
                </div>
                <div class="item-actions">
                  <el-button type="primary" size="small" @click="handleViewReport(report)">
                    查看详情
                  </el-button>
                  <el-button type="success" size="small" @click="handleProcessReport(report.reportId)">
                    处理
                  </el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="用户申诉" name="appeals">
            <div class="pending-list">
              <div
                v-for="appeal in pendingAppeals"
                :key="appeal.appealId"
                class="pending-item appeal-item"
                :class="{
                  'urgent-appeal': appeal.priority === 'urgent',
                  'high-priority': appeal.priority === 'high',
                  'resolved-appeal': appeal.status === 'resolved',
                  'rejected-appeal': appeal.status === 'rejected'
                }"
              >
                <div class="item-info">
                  <div class="appeal-details">
                    <div class="appeal-header">
                      <h4>{{ appeal.title }}</h4>
                      <div class="appeal-tags">
                        <el-tag
                          :type="getAppealPriorityType(appeal.priority)"
                          size="small"
                        >
                          {{ getAppealPriorityLabel(appeal.priority) }}
                        </el-tag>
                        <el-tag
                          :type="getAppealStatusType(appeal.status)"
                          size="small"
                        >
                          {{ getAppealStatusLabel(appeal.status) }}
                        </el-tag>
                      </div>
                    </div>
                    <p class="appeal-type">申诉类型：{{ getAppealTypeLabel(appeal.type) }}</p>
                    <p class="appeal-description">{{ appeal.description.length > 100 ? appeal.description.substring(0, 100) + '...' : appeal.description }}</p>
                    <div class="appeal-meta">
                      <span>申诉人：{{ appeal.userInfo?.nickname }}</span>
                      <span>申诉时间：{{ formatTime(appeal.createTime) }}</span>
                      <span v-if="appeal.relatedType">相关：{{ appeal.relatedType }} #{{ appeal.relatedId }}</span>
                      <span v-if="appeal.resolveTime" class="resolve-time">
                        处理时间：{{ formatTime(appeal.resolveTime) }}
                      </span>
                    </div>
                    <div v-if="appeal.adminReply" class="admin-reply">
                      <strong>管理员回复：</strong>{{ appeal.adminReply }}
                    </div>
                  </div>
                </div>
                <div class="item-actions">
                  <el-button type="primary" size="small" @click="handleViewAppeal(appeal)">
                    查看详情
                  </el-button>
                  <el-button
                    v-if="appeal.status === 'pending'"
                    type="success"
                    size="small"
                    @click="handleProcessAppeal(appeal.appealId)"
                  >
                    处理
                  </el-button>
                  <el-tag
                    v-else-if="appeal.status === 'resolved'"
                    type="success"
                    size="small"
                  >
                    已解决
                  </el-tag>
                  <el-tag
                    v-else-if="appeal.status === 'rejected'"
                    type="danger"
                    size="small"
                  >
                    已驳回
                  </el-tag>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 申诉详情对话框 -->
    <el-dialog v-model="appealDialogVisible" title="申诉详情" width="700px">
      <div class="appeal-detail">
        <div class="appeal-info">
          <h4>申诉信息</h4>
          <div class="info-item">
            <span class="label">申诉类型：</span>
            <span>{{ getAppealTypeLabel(currentAppeal.type) }}</span>
          </div>
          <div class="info-item">
            <span class="label">优先级：</span>
            <el-tag :type="getAppealPriorityType(currentAppeal.priority)" size="small">
              {{ getAppealPriorityLabel(currentAppeal.priority) }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="label">申诉时间：</span>
            <span>{{ formatTime(currentAppeal.createTime) }}</span>
          </div>
          <div class="info-item">
            <span class="label">申诉人：</span>
            <span>{{ currentAppeal.userInfo?.nickname }} ({{ currentAppeal.userInfo?.username }})</span>
          </div>
          <div class="info-item" v-if="currentAppeal.relatedType">
            <span class="label">相关内容：</span>
            <span>{{ currentAppeal.relatedType }} ID: {{ currentAppeal.relatedId }}</span>
          </div>
        </div>

        <div class="appeal-content">
          <h4>申诉标题</h4>
          <p>{{ currentAppeal.title }}</p>

          <h4>申诉内容</h4>
          <p>{{ currentAppeal.description }}</p>

          <div v-if="currentAppeal.evidence && currentAppeal.evidence.length > 0">
            <h4>证据材料</h4>
            <div class="evidence-list">
              <el-image
                v-for="(img, index) in currentAppeal.evidence"
                :key="index"
                :src="img"
                fit="cover"
                style="width: 100px; height: 100px; margin-right: 10px; border-radius: 4px"
                :preview-src-list="currentAppeal.evidence"
              />
            </div>
          </div>
        </div>

        <div class="appeal-reply" v-if="currentAppeal.adminReply">
          <h4>管理员回复</h4>
          <p>{{ currentAppeal.adminReply }}</p>
          <p class="reply-time">回复时间：{{ formatTime(currentAppeal.updateTime) }}</p>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="appealDialogVisible = false">关闭</el-button>
          <el-button type="danger" @click="handleAppealAction('reject')">
            驳回申诉
          </el-button>
          <el-button type="success" @click="handleAppealAction('resolve')">
            处理完成
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 举报详情对话框 -->
    <el-dialog v-model="reportDialogVisible" title="举报详情" width="600px">
      <div class="report-detail">
        <div class="report-info">
          <h4>举报信息</h4>
          <div class="info-item">
            <span class="label">举报类型：</span>
            <span>{{ currentReport.type }}</span>
          </div>
          <div class="info-item">
            <span class="label">举报原因：</span>
            <span>{{ currentReport.reason }}</span>
          </div>
          <div class="info-item">
            <span class="label">举报时间：</span>
            <span>{{ formatTime(currentReport.createTime) }}</span>
          </div>
          <div class="info-item">
            <span class="label">举报人：</span>
            <span>{{ currentReport.reporterName }}</span>
          </div>
        </div>

        <div class="report-content">
          <h4>举报内容</h4>
          <p>{{ currentReport.description }}</p>
        </div>

        <div class="reported-item" v-if="currentReport.targetType === 'product'">
          <h4>被举报商品</h4>
          <div class="product-info">
            <el-image
              :src="currentReport.targetInfo?.image"
              fit="cover"
              class="product-image"
            />
            <div class="product-details">
              <h5>{{ currentReport.targetInfo?.title }}</h5>
              <p>价格：¥{{ currentReport.targetInfo?.price }}</p>
              <p>卖家：{{ currentReport.targetInfo?.sellerName }}</p>
            </div>
          </div>
        </div>

        <div class="evidence-section" v-if="currentReport.evidence?.length">
          <h4>举报证据</h4>
          <div class="evidence-images">
            <el-image
              v-for="(img, index) in currentReport.evidence"
              :key="index"
              :src="img"
              fit="cover"
              class="evidence-image"
              :preview-src-list="currentReport.evidence"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="reportDialogVisible = false">关闭</el-button>
          <el-button type="danger" @click="handleReportAction('reject')">
            驳回举报
          </el-button>
          <el-button type="primary" @click="handleReportAction('approve')">
            处理举报
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  User,
  Goods,
  ShoppingCart,
  Money,
  Picture,
  Setting
} from '@element-plus/icons-vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { formatTime } from '@/utils'
import type { Product, Report, Appeal } from '@/types'
import { PRODUCT_CATEGORY_LABELS } from '@/constants'

use([
  CanvasRenderer,
  LineChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const router = useRouter()

const activeTab = ref('products')
const userChartPeriod = ref('30d')

// 统计数据
const stats = reactive({
  totalUsers: 1250,
  totalProducts: 3680,
  totalOrders: 892,
  totalRevenue: 45680
})

// 图表数据
const userTrendData = reactive({
  '7d': {
    dates: ['01-10', '01-11', '01-12', '01-13', '01-14', '01-15', '01-16'],
    values: [12, 19, 15, 23, 18, 25, 32]
  },
  '30d': {
    dates: ['12-18', '12-25', '01-01', '01-08', '01-15'],
    values: [85, 120, 95, 140, 165]
  },
  '90d': {
    dates: ['10-16', '11-16', '12-16', '01-16'],
    values: [280, 350, 420, 520]
  }
})

const categoryData = reactive([
  { name: '教材书籍', value: 1200 },
  { name: '数码电子', value: 800 },
  { name: '服装配饰', value: 600 },
  { name: '生活用品', value: 500 },
  { name: '运动器材', value: 300 },
  { name: '美妆护肤', value: 200 },
  { name: '食品零食', value: 150 },
  { name: '其他', value: 230 }
])

// 图表配置
const userTrendOption = computed(() => {
  const currentData = userTrendData[userChartPeriod.value]
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: currentData.dates,
      axisLine: {
        lineStyle: {
          color: '#e0e6ed'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e0e6ed'
        }
      }
    },
    series: [{
      name: '新增用户',
      type: 'line',
      smooth: true,
      data: currentData.values,
      lineStyle: {
        color: '#409eff'
      },
      itemStyle: {
        color: '#409eff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(64, 158, 255, 0.3)'
          }, {
            offset: 1, color: 'rgba(64, 158, 255, 0.1)'
          }]
        }
      }
    }]
  }
})

const categoryDistributionOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    data: categoryData.map(item => item.name)
  },
  series: [{
    name: '商品分类',
    type: 'pie',
    radius: ['40%', '70%'],
    center: ['60%', '50%'],
    avoidLabelOverlap: false,
    label: {
      show: false,
      position: 'center'
    },
    emphasis: {
      label: {
        show: true,
        fontSize: '18',
        fontWeight: 'bold'
      }
    },
    labelLine: {
      show: false
    },
    data: categoryData,
    itemStyle: {
      borderRadius: 5,
      borderColor: '#fff',
      borderWidth: 2
    }
  }]
}))

// 待审核商品
const pendingProducts = ref<Product[]>([])

// 待处理举报
const pendingReports = ref<Report[]>([])

// 待处理申诉
const pendingAppeals = ref<Appeal[]>([])

// 获取统计数据
const fetchStats = async () => {
  try {
    // 这里应该调用API获取统计数据
    // const response = await getStatsApi()
    // Object.assign(stats, response.data)
  } catch (error) {
    ElMessage.error('获取统计数据失败')
  }
}

// 获取待处理事项
const fetchPendingItems = async () => {
  try {
    // 模拟数据
    pendingProducts.value = [
      {
        productId: 1,
        title: '高等数学教材（第七版）',
        description: '同济大学出版社，九成新，无笔记',
        category: 'textbook',
        price: 25,
        condition: 'good',
        images: ['/src/assets/background.jpg'],
        sellerId: 1,
        sellerInfo: {
          userId: 1,
          username: 'student1',
          nickname: '小明',
          avatar: '',
          email: '',
          phone: '',
          sex: '0',
          status: '',
          roleKey: 'student'
        },
        campus: 'main',
        status: 'pending_review',
        viewCount: 0,
        likeCount: 0,
        createTime: '2024-01-15T10:30:00',
        updateTime: '2024-01-15T10:30:00'
      }
    ]

    pendingReports.value = [
      {
        reportId: 1,
        reporterId: 2,
        reporterInfo: {
          userId: 2,
          username: 'student2',
          nickname: '小红',
          avatar: '',
          email: '',
          phone: '',
          sex: '1',
          status: '',
          roleKey: 'student'
        },
        targetType: 'product',
        targetId: 1,
        reason: '虚假信息',
        content: '商品描述与实际不符',
        status: 'pending',
        createTime: '2024-01-16T09:00:00'
      }
    ]

    pendingAppeals.value = [
      {
        appealId: 1,
        userId: 3,
        userInfo: {
          userId: 3,
          username: 'student3',
          nickname: '小李',
          avatar: '',
          email: '<EMAIL>',
          phone: '138****5678',
          sex: '0',
          status: 'active',
          roleKey: 'student'
        },
        type: 'account_ban',
        title: '账号被误封申诉',
        description: '我的账号在2024年1月15日被误封，原因是系统误判我发布虚假商品信息。但实际上我发布的二手教材信息都是真实的，有购买凭证和实物照片为证。希望管理员能够重新审核我的账号状态，解除封禁。',
        relatedId: 3,
        relatedType: 'user',
        status: 'pending',
        priority: 'high',
        evidence: [
          '/src/assets/background.jpg',
          '/src/assets/background.jpg'
        ],
        createTime: '2024-01-16T14:30:00',
        updateTime: '2024-01-16T14:30:00'
      },
      {
        appealId: 2,
        userId: 4,
        userInfo: {
          userId: 4,
          username: 'student4',
          nickname: '小王',
          avatar: '',
          email: '<EMAIL>',
          phone: '139****9999',
          sex: '1',
          status: 'active',
          roleKey: 'student'
        },
        type: 'product_removal',
        title: '商品被误删申诉',
        description: '我发布的iPhone 13商品被系统删除，提示原因是"疑似虚假信息"。但这是我本人真实使用的手机，因为要换新机所以出售。商品信息、价格、成色描述都是真实的，有发票和包装盒为证。',
        relatedId: 15,
        relatedType: 'product',
        status: 'pending',
        priority: 'medium',
        evidence: [
          '/src/assets/background.jpg'
        ],
        createTime: '2024-01-17T10:15:00',
        updateTime: '2024-01-17T10:15:00'
      },
      {
        appealId: 3,
        userId: 5,
        userInfo: {
          userId: 5,
          username: 'student5',
          nickname: '小张',
          avatar: '',
          email: '<EMAIL>',
          phone: '137****7777',
          sex: '0',
          status: 'active',
          roleKey: 'student'
        },
        type: 'order_dispute',
        title: '订单纠纷申诉',
        description: '订单ORD202401150001中，买家收到商品后恶意差评并要求退款。商品完全按照描述发货，成色良好，但买家以"不满意"为由要求退款。这明显是恶意行为，希望平台能够公正处理。',
        relatedId: 1001,
        relatedType: 'order',
        status: 'processing',
        priority: 'medium',
        adminReply: '我们已经收到您的申诉，正在调查相关订单情况，请耐心等待处理结果。',
        adminId: 1,
        evidence: [
          '/src/assets/background.jpg',
          '/src/assets/background.jpg'
        ],
        createTime: '2024-01-17T16:45:00',
        updateTime: '2024-01-18T09:30:00'
      },
      {
        appealId: 4,
        userId: 6,
        userInfo: {
          userId: 6,
          username: 'student6',
          nickname: '小陈',
          avatar: '',
          email: '<EMAIL>',
          phone: '136****6666',
          sex: '1',
          status: 'active',
          roleKey: 'student'
        },
        type: 'payment_issue',
        title: '支付问题申诉',
        description: '在购买商品时，支付宝已经扣款成功，但系统显示支付失败，订单被自动取消。现在钱被扣了但没有收到商品，联系客服也没有回复。希望尽快处理退款或重新发货。',
        relatedId: 2001,
        relatedType: 'order',
        status: 'pending',
        priority: 'urgent',
        createTime: '2024-01-18T11:20:00',
        updateTime: '2024-01-18T11:20:00'
      },
      {
        appealId: 5,
        userId: 7,
        userInfo: {
          userId: 7,
          username: 'student7',
          nickname: '小刘',
          avatar: '',
          email: '<EMAIL>',
          phone: '135****5555',
          sex: '0',
          status: 'active',
          roleKey: 'student'
        },
        type: 'account_ban',
        title: '账号异常封禁申诉',
        description: '我的账号在2024年1月19日突然被封禁，系统提示"违规操作"，但我并没有进行任何违规行为。最近只是正常发布了几个二手书籍和生活用品的信息，都是按照平台规则操作的。请管理员核实情况并解封我的账号。',
        relatedId: 7,
        relatedType: 'user',
        status: 'pending',
        priority: 'high',
        evidence: [
          '/src/assets/background.jpg'
        ],
        createTime: '2024-01-19T09:15:00',
        updateTime: '2024-01-19T09:15:00'
      },
      {
        appealId: 6,
        userId: 8,
        userInfo: {
          userId: 8,
          username: 'student8',
          nickname: '小赵',
          avatar: '',
          email: '<EMAIL>',
          phone: '134****4444',
          sex: '1',
          status: 'active',
          roleKey: 'student'
        },
        type: 'order_dispute',
        title: '买家恶意退款申诉',
        description: '订单ORD202401180002中，我出售的MacBook Pro笔记本电脑，买家收货后使用了一周，然后以"商品描述不符"为由申请退款。但商品完全按照我的描述，9成新，功能正常，还提供了详细的配置截图。买家明显是想免费使用后退货，这种行为严重损害卖家权益。',
        relatedId: 1002,
        relatedType: 'order',
        status: 'pending',
        priority: 'medium',
        evidence: [
          '/src/assets/background.jpg',
          '/src/assets/background.jpg',
          '/src/assets/background.jpg'
        ],
        createTime: '2024-01-19T14:30:00',
        updateTime: '2024-01-19T14:30:00'
      },
      {
        appealId: 7,
        userId: 9,
        userInfo: {
          userId: 9,
          username: 'student9',
          nickname: '小孙',
          avatar: '',
          email: '<EMAIL>',
          phone: '133****3333',
          sex: '0',
          status: 'active',
          roleKey: 'student'
        },
        type: 'product_removal',
        title: '商品被恶意举报申诉',
        description: '我发布的化妆品套装被恶意举报并下架，举报理由是"假货"。但这些化妆品都是我从专柜购买的正品，有购买小票和专柜验货证明。怀疑是同类商品的竞争对手恶意举报，希望平台能够公正处理，恢复我的商品。',
        relatedId: 25,
        relatedType: 'product',
        status: 'processing',
        priority: 'medium',
        adminReply: '我们已经收到您的申诉，正在核实相关证据，请提供更详细的购买凭证。',
        adminId: 1,
        evidence: [
          '/src/assets/background.jpg',
          '/src/assets/background.jpg'
        ],
        createTime: '2024-01-19T16:45:00',
        updateTime: '2024-01-20T10:20:00'
      },
      {
        appealId: 8,
        userId: 10,
        userInfo: {
          userId: 10,
          username: 'student10',
          nickname: '小周',
          avatar: '',
          email: '<EMAIL>',
          phone: '132****2222',
          sex: '1',
          status: 'active',
          roleKey: 'student'
        },
        type: 'payment_issue',
        title: '重复扣款申诉',
        description: '在购买商品时出现网络异常，我点击了多次支付按钮，结果被重复扣款3次，总共扣了300元，但只收到了一件商品。联系客服多次都没有得到回复，希望尽快退还多扣的200元。',
        relatedId: 2002,
        relatedType: 'order',
        status: 'pending',
        priority: 'urgent',
        createTime: '2024-01-20T08:30:00',
        updateTime: '2024-01-20T08:30:00'
      },
      {
        appealId: 9,
        userId: 11,
        userInfo: {
          userId: 11,
          username: 'student11',
          nickname: '小吴',
          avatar: '',
          email: '<EMAIL>',
          phone: '131****1111',
          sex: '0',
          status: 'active',
          roleKey: 'student'
        },
        type: 'other',
        title: '评价系统异常申诉',
        description: '我购买的商品质量很好，给了5星好评，但系统显示我给了1星差评，这严重影响了卖家的信誉。我尝试修改评价但系统提示"评价已锁定"。这个技术问题需要尽快解决，否则会影响卖家的正常经营。',
        relatedId: 1003,
        relatedType: 'order',
        status: 'pending',
        priority: 'low',
        createTime: '2024-01-20T11:45:00',
        updateTime: '2024-01-20T11:45:00'
      },
      {
        appealId: 10,
        userId: 12,
        userInfo: {
          userId: 12,
          username: 'student12',
          nickname: '小郑',
          avatar: '',
          email: '<EMAIL>',
          phone: '130****0000',
          sex: '1',
          status: 'active',
          roleKey: 'student'
        },
        type: 'account_ban',
        title: '误判刷单封号申诉',
        description: '我的账号被系统判定为"刷单行为"并被封禁，但我从未进行过刷单。最近确实购买了较多商品，但都是因为开学需要购买教材和生活用品，这是正常的购买行为。希望管理员能够核实我的购买记录，解除误判。',
        relatedId: 12,
        relatedType: 'user',
        status: 'resolved',
        priority: 'medium',
        adminReply: '经过核实，您的购买行为确实属于正常消费，系统误判已纠正，账号已解封。',
        adminId: 2,
        resolveTime: '2024-01-20T15:30:00',
        createTime: '2024-01-19T20:15:00',
        updateTime: '2024-01-20T15:30:00'
      },
      {
        appealId: 11,
        userId: 13,
        userInfo: {
          userId: 13,
          username: 'student13',
          nickname: '小黄',
          avatar: '',
          email: '<EMAIL>',
          phone: '129****9999',
          sex: '0',
          status: 'active',
          roleKey: 'student'
        },
        type: 'product_removal',
        title: '学习资料被误删申诉',
        description: '我分享的考研复习资料被系统删除，提示"涉嫌版权问题"。但这些资料都是我自己整理的笔记和总结，没有涉及任何版权内容。这些资料对其他考研同学很有帮助，希望能够恢复发布。',
        relatedId: 35,
        relatedType: 'product',
        status: 'rejected',
        priority: 'low',
        adminReply: '经审核，您的资料中包含了教材的大量原文内容，确实存在版权风险，无法恢复发布。',
        adminId: 1,
        resolveTime: '2024-01-20T16:45:00',
        createTime: '2024-01-20T13:20:00',
        updateTime: '2024-01-20T16:45:00'
      }
    ]
  } catch (error) {
    ElMessage.error('获取待处理事项失败')
  }
}

// 刷新数据
const refreshData = () => {
  fetchStats()
  fetchPendingItems()
  ElMessage.success('数据已刷新')
}

// 审核操作
const handleApprove = (productId: number) => {
  ElMessage.success('商品审核通过')
  fetchPendingItems()
}

const handleReject = (productId: number) => {
  ElMessage.warning('商品审核拒绝')
  fetchPendingItems()
}

// 举报处理
const reportDialogVisible = ref(false)
const currentReport = ref<Report>({} as Report)

const handleViewReport = (report: Report) => {
  currentReport.value = report
  reportDialogVisible.value = true
}

const handleReportAction = async (action: 'approve' | 'reject') => {
  try {
    // 模拟处理举报
    await new Promise(resolve => setTimeout(resolve, 1000))

    const actionText = action === 'approve' ? '处理' : '驳回'
    ElMessage.success(`举报已${actionText}`)
    reportDialogVisible.value = false

    // 刷新数据
    fetchPendingItems()
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

const handleProcessReport = (reportId: number) => {
  ElMessage.success('举报已处理')
  fetchPendingItems()
}

// 申诉处理
const appealDialogVisible = ref(false)
const currentAppeal = ref<Appeal>({} as Appeal)

const handleViewAppeal = (appeal: Appeal) => {
  currentAppeal.value = appeal
  appealDialogVisible.value = true
}

const handleAppealAction = async (action: 'resolve' | 'reject') => {
  try {
    // 模拟处理申诉
    await new Promise(resolve => setTimeout(resolve, 1000))

    const actionText = action === 'resolve' ? '处理完成' : '驳回'
    ElMessage.success(`申诉已${actionText}`)
    appealDialogVisible.value = false

    // 刷新数据
    fetchPendingItems()
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

const handleProcessAppeal = (appealId: number) => {
  ElMessage.success('申诉已处理')
  fetchPendingItems()
}

// 申诉相关工具函数
const getAppealTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    account_ban: '账号封禁',
    product_removal: '商品下架',
    order_dispute: '订单纠纷',
    payment_issue: '支付问题',
    other: '其他'
  }
  return typeMap[type] || type
}

const getAppealPriorityLabel = (priority: string) => {
  const priorityMap: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return priorityMap[priority] || priority
}

const getAppealPriorityType = (priority: string) => {
  const typeMap: Record<string, string> = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger'
  }
  return typeMap[priority] || 'info'
}

const getAppealStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已解决',
    rejected: '已驳回'
  }
  return statusMap[status] || status
}

const getAppealStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    processing: 'primary',
    resolved: 'success',
    rejected: 'danger'
  }
  return typeMap[status] || 'info'
}

// 导航方法
const goToUserManagement = () => {
  router.push({ name: 'AdminUsers' })
}

const goToProductManagement = () => {
  router.push({ name: 'AdminProducts' })
}

const goToOrderManagement = () => {
  router.push({ name: 'AdminOrders' })
}

const goToPersonalSettings = () => {
  router.push({ name: 'PersonalSettings' })
}

onMounted(() => {
  fetchStats()
  fetchPendingItems()
})
</script>

<style lang="stylus" scoped>
.admin-dashboard
  padding 20px
  background #f5f7fa
  min-height calc(100vh - 60px)

.dashboard-header
  display flex
  justify-content space-between
  align-items center
  margin-bottom 20px
  
  h2
    color #333

.stats-cards
  display grid
  grid-template-columns repeat(auto-fit, minmax(250px, 1fr))
  gap 20px
  margin-bottom 30px
  
  .stat-card
    .stat-content
      display flex
      align-items center
      gap 15px
      
      .stat-icon
        width 50px
        height 50px
        border-radius 8px
        display flex
        align-items center
        justify-content center
        font-size 24px
        color white
        
        &.user-icon
          background linear-gradient(135deg, #667eea 0%, #764ba2 100%)
          
        &.product-icon
          background linear-gradient(135deg, #f093fb 0%, #f5576c 100%)
          
        &.order-icon
          background linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)
          
        &.revenue-icon
          background linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)
          
      .stat-info
        .stat-value
          font-size 28px
          font-weight bold
          color #333
          margin-bottom 4px
          
        .stat-label
          color #666
          font-size 14px

.charts-section
  margin-bottom 30px

  .chart-header
    display flex
    justify-content space-between
    align-items center

  .chart-container
    height 300px

.pending-section
  margin-bottom 30px
  
  .pending-list
    .pending-item
      display flex
      justify-content space-between
      align-items center
      padding 15px 0
      border-bottom 1px solid #f0f0f0
      
      &:last-child
        border-bottom none
        
      .item-info
        display flex
        align-items center
        gap 15px
        flex 1
        
        .item-image
          width 60px
          height 60px
          border-radius 4px
          
          .image-slot
            display flex
            align-items center
            justify-content center
            height 100%
            background #f5f7fa
            color #909399
            
        .item-details, .report-details
          h4
            margin 0 0 5px 0
            color #333
            
          p
            margin 0 0 3px 0
            color #666
            font-size 14px
            
      .item-actions
        display flex
        gap 8px

.quick-actions
  margin-bottom 20px

  .action-buttons
    display grid
    grid-template-columns repeat(auto-fit, minmax(200px, 1fr))
    gap 15px

@media (max-width: 768px)
  .admin-dashboard
    padding 10px
    
  .dashboard-header
    flex-direction column
    gap 15px
    align-items stretch
    
  .stats-cards
    grid-template-columns 1fr
    
  .pending-item
    flex-direction column
    align-items stretch
    gap 15px
    
  .action-buttons
    grid-template-columns 1fr

.appeal-detail
  .appeal-info, .appeal-content, .appeal-reply
    margin-bottom 20px

    h4
      margin 0 0 15px 0
      color #333
      font-size 16px

    .info-item
      display flex
      margin-bottom 8px
      align-items center

      .label
        width 80px
        color #666
        font-weight 500

    p
      margin 0
      line-height 1.6
      color #666

  .appeal-header
    display flex
    justify-content space-between
    align-items center
    margin-bottom 10px

    h4
      margin 0

    .appeal-tags
      display flex
      gap 8px

  .appeal-type, .appeal-description
    margin-bottom 8px

  .appeal-meta
    display flex
    gap 20px
    font-size 12px
    color #999

  .evidence-list
    display flex
    flex-wrap wrap
    gap 10px
    margin-top 10px

  .appeal-reply
    background #f8f9fa
    padding 15px
    border-radius 6px
    border-left 4px solid #409eff

    .reply-time
      font-size 12px
      color #999
      margin-top 10px

.report-detail
  .report-info, .report-content, .reported-item, .evidence-section
    margin-bottom 20px

    h4
      margin 0 0 10px 0
      color #333
      font-size 16px
      border-bottom 1px solid #eee
      padding-bottom 5px

  .info-item
    display flex
    margin-bottom 8px

    .label
      width 80px
      color #666
      font-weight 500

  .product-info
    display flex
    gap 15px

    .product-image
      width 80px
      height 80px
      border-radius 6px

    .product-details
      flex 1

      h5
        margin 0 0 8px 0
        color #333

      p
        margin 0 0 4px 0
        color #666
        font-size 14px

  .evidence-images
    display flex
    gap 10px
    flex-wrap wrap

    .evidence-image
      width 100px
      height 100px
      border-radius 6px
      cursor pointer

// 申诉项特殊样式
.appeal-item
  &.urgent-appeal
    border-left 4px solid #f56c6c
    background linear-gradient(to right, #fef0f0, #ffffff)

  &.high-priority
    border-left 4px solid #e6a23c
    background linear-gradient(to right, #fdf6ec, #ffffff)

  &.resolved-appeal
    border-left 4px solid #67c23a
    background linear-gradient(to right, #f0f9ff, #ffffff)
    opacity 0.8

  &.rejected-appeal
    border-left 4px solid #909399
    background linear-gradient(to right, #f5f7fa, #ffffff)
    opacity 0.7

  .admin-reply
    margin-top 10px
    padding 8px 12px
    background #e1f3ff
    border-radius 4px
    font-size 13px
    color #409eff
    border-left 3px solid #409eff

    strong
      color #303133

  .resolve-time
    color #67c23a
    font-weight 500
</style>
