<template>
  <div class="user-management">
    <div class="page-header">
      <h2>用户管理</h2>
      <div class="header-actions">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回后台
        </el-button>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入用户名"
            clearable
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="searchForm.role" placeholder="选择角色" clearable style="width: 180px">
            <el-option
              v-for="(label, value) in USER_ROLE_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 180px">
            <el-option
              v-for="(label, value) in USER_STATUS_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="校区">
          <el-select v-model="searchForm.campus" placeholder="选择校区" clearable style="width: 180px">
            <el-option
              v-for="(label, value) in CAMPUS_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
        </div>
      </template>

      <el-table
        :data="users"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="userId" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="nickname" label="昵称" width="120" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="campus" label="校区" width="100">
          <template #default="{ row }">
            <el-tag :type="getCampusTagType(row.campus)">
              {{ getCampusLabel(row.campus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="roleKey" label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="getRoleTagType(row.roleKey)">
              {{ getRoleLabel(row.roleKey) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="注册时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button
              :type="row.status === 'active' ? 'warning' : 'success'"
              size="small"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 'active' ? '禁用' : '恢复' }}
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 编辑用户对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑用户"
      width="600px"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" disabled />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="editForm.nickname" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" />
        </el-form-item>
        <el-form-item label="角色" prop="roleKey">
          <el-select v-model="editForm.roleKey" style="width: 100%">
            <el-option
              v-for="(label, value) in USER_ROLE_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="校区" prop="campus">
          <el-select v-model="editForm.campus" style="width: 100%">
            <el-option
              v-for="(label, value) in CAMPUS_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select v-model="editForm.sex" style="width: 100%">
            <el-option label="未知" value="0" />
            <el-option label="男" value="1" />
            <el-option label="女" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="editForm.status" style="width: 100%">
            <el-option
              v-for="(label, value) in USER_STATUS_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveEdit" :loading="saveLoading">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  Download
} from '@element-plus/icons-vue'
import { formatTime } from '@/utils'
import type { User } from '@/types'
import { USER_ROLE_LABELS, CAMPUS_LABELS } from '@/constants'
import {
  getUsersListApi,
  updateUserApi,
  updateUserStatusApi,
  deleteUserApi,
  getUserStatisticsApi,
  batchUserOperationApi
} from '@/request/userManagementApi'
import type { UserListParams, UserUpdateRequest, UserStatusUpdateRequest } from '@/request/userManagementApi'

// 用户状态标签
const USER_STATUS_LABELS = {
  active: '正常',
  disabled: '禁用'
} as const

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  username: '',
  role: '',
  status: '',
  campus: ''
})

// 用户列表数据
const users = ref<User[]>([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 编辑对话框
const editDialogVisible = ref(false)
const editFormRef = ref()
const saveLoading = ref(false)
const editForm = reactive({
  userId: 0,
  username: '',
  nickname: '',
  email: '',
  phone: '',
  roleKey: '',
  campus: '',
  status: '',
  sex: '0'
})

const editRules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

// 获取用户列表
const fetchUsers = async () => {
  loading.value = true
  try {
    // 调用真实的API
    const params: UserListParams = {
      username: searchForm.username || undefined,
      role: searchForm.role || undefined,
      status: searchForm.status || undefined,
      campus: searchForm.campus || undefined,
      sortBy: 'create_time',
      sortOrder: 'desc',
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }

    const response = await getUsersListApi(params)

    if (response.code === 200) {
      users.value = response.data.records
      total.value = response.data.total
    } else {
      ElMessage.error(response.msg || '获取用户列表失败')
      users.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
    users.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 标签样式函数
const getCampusTagType = (campus: string) => {
  const typeMap: Record<string, string> = {
    main: '',
    east: 'success',
    west: 'warning',
    south: 'info'
  }
  return typeMap[campus] || ''
}

const getCampusLabel = (campus: string) => {
  return CAMPUS_LABELS[campus as keyof typeof CAMPUS_LABELS] || campus
}

const getRoleTagType = (role: string) => {
  const typeMap: Record<string, string> = {
    student: '',
    admin: 'warning',
    super_admin: 'danger'
  }
  return typeMap[role] || ''
}

const getRoleLabel = (role: string) => {
  return USER_ROLE_LABELS[role as keyof typeof USER_ROLE_LABELS] || role
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    active: 'success',
    disabled: 'danger',
    pending: 'warning'
  }
  return typeMap[status] || ''
}

const getStatusLabel = (status: string) => {
  return USER_STATUS_LABELS[status as keyof typeof USER_STATUS_LABELS] || status
}

// 事件处理
const goBack = () => {
  router.push({ name: 'AdminDashboard' })
}

const refreshData = () => {
  fetchUsers()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchUsers()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    username: '',
    role: '',
    status: '',
    campus: ''
  })
  handleSearch()
}

const handleEdit = (user: User) => {
  Object.assign(editForm, user)
  editDialogVisible.value = true
}

const handleSaveEdit = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()
    saveLoading.value = true

    // 准备更新数据
    const updateData: UserUpdateRequest = {
      nickname: editForm.nickname,
      email: editForm.email,
      phone: editForm.phone,
      roleKey: editForm.roleKey,
      campus: editForm.campus,
      status: editForm.status,
      sex: editForm.sex
    }

    // 调用更新API
    const response = await updateUserApi(editForm.userId, updateData)

    if (response.code === 200) {
      ElMessage.success('用户信息更新成功')
      editDialogVisible.value = false
      fetchUsers()
    } else {
      ElMessage.error(response.msg || '更新失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

const handleToggleStatus = async (user: User) => {
  const newStatus = user.status === 'active' ? 'disabled' : 'active'
  const action = user.status === 'active' ? '禁用' : '恢复'

  try {
    await ElMessageBox.confirm(`确定要${action}用户 ${user.nickname} 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用状态更新API
    const response = await updateUserStatusApi(user.userId, { status: newStatus })

    if (response.code === 200) {
      ElMessage.success(`用户${action}成功`)
      fetchUsers()
    } else {
      ElMessage.error(response.msg || `${action}失败`)
    }
  } catch {
    // 用户取消
  }
}

const handleDelete = async (user: User) => {
  try {
    await ElMessageBox.confirm(`确定要删除用户 ${user.nickname} 吗？此操作不可恢复`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })

    // 调用删除API
    const response = await deleteUserApi(user.userId)

    if (response.code === 200) {
      ElMessage.success('用户删除成功')
      fetchUsers()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleExport = () => {
  try {
    // 准备导出数据
    const exportData = users.value.map(user => ({
      '用户ID': user.userId,
      '用户名': user.username,
      '昵称': user.nickname,
      '邮箱': user.email,
      '手机号': user.phone,
      '角色': USER_ROLE_LABELS[user.roleKey as keyof typeof USER_ROLE_LABELS],
      '校区': CAMPUS_LABELS[user.campus as keyof typeof CAMPUS_LABELS],
      '状态': USER_STATUS_LABELS[user.status as keyof typeof USER_STATUS_LABELS],
      '注册时间': formatTime(user.createTime)
    }))

    // 转换为CSV格式
    const headers = Object.keys(exportData[0])
    const csvContent = [
      headers.join(','),
      ...exportData.map(row =>
        headers.map(header => `"${row[header] || ''}"`).join(',')
      )
    ].join('\n')

    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `用户数据_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('用户数据导出成功')
  } catch (error) {
    ElMessage.error('导出失败，请重试')
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchUsers()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchUsers()
}

onMounted(() => {
  fetchUsers()
})
</script>

<style lang="stylus" scoped>
.user-management
  width 100%
  padding 20px

.page-header
  display flex
  justify-content space-between
  align-items center
  margin-bottom 20px
  
  h2
    margin 0
    color #333
    
  .header-actions
    display flex
    gap 10px

.search-card
  margin-bottom 20px

.table-card
  .card-header
    display flex
    justify-content space-between
    align-items center

.pagination-wrapper
  display flex
  justify-content center
  margin-top 20px

@media (max-width: 768px)
  .user-management
    padding 10px
    
  .page-header
    flex-direction column
    gap 15px
    align-items stretch
    
  .header-actions
    justify-content center
</style>
