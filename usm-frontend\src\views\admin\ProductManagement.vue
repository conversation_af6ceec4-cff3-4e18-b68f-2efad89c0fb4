<template>
  <div class="product-management">
    <div class="page-header">
      <h2>商品管理</h2>
      <div class="header-actions">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回后台
        </el-button>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="商品标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入商品标题"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="卖家用户名">
          <el-input
            v-model="searchForm.sellerUsername"
            placeholder="请输入卖家用户名"
            clearable
            style="width: 160px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="searchForm.category" placeholder="选择分类" clearable style="width: 140px">
            <el-option
              v-for="(label, value) in PRODUCT_CATEGORY_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 140px">
            <el-option
              v-for="(label, value) in PRODUCT_STATUS_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="校区">
          <el-select v-model="searchForm.campus" placeholder="选择校区" clearable style="width: 140px">
            <el-option
              v-for="(label, value) in CAMPUS_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <!-- 第二行搜索条件 -->
      <el-form :model="searchForm" inline style="margin-top: 10px">
        <el-form-item label="商品状况">
          <el-select v-model="searchForm.condition" placeholder="选择商品状况" clearable style="width: 140px">
            <el-option
              v-for="(label, value) in PRODUCT_CONDITION_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="价格范围">
          <div style="display: flex; align-items: center; gap: 8px;">
            <el-input
              v-model.number="searchForm.minPrice"
              placeholder="最低价"
              type="number"
              min="0"
              step="0.01"
              style="width: 100px"
            />
            <span>-</span>
            <el-input
              v-model.number="searchForm.maxPrice"
              placeholder="最高价"
              type="number"
              min="0"
              step="0.01"
              style="width: 100px"
            />
          </div>
        </el-form-item>
        <el-form-item label="发布时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="删除状态">
          <el-select v-model="searchForm.isDeleted" placeholder="选择删除状态" clearable style="width: 120px">
            <el-option label="正常商品" value="false" />
            <el-option label="已删除" value="true" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 商品列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>商品列表</span>
          <div class="header-actions">
            <el-button type="warning" @click="handleBatchAction">
              <el-icon><Operation /></el-icon>
              批量操作
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="products"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="productId" label="ID" width="80" />
        <el-table-column label="商品图片" width="100">
          <template #default="{ row }">
            <el-image
              :src="row.images[0]"
              :preview-src-list="row.images"
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 4px"
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="商品标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="category" label="分类" width="120">
          <template #default="{ row }">
            <el-tag class="category-tag">
              <CategoryIcon :category="row.category" :size="14" />
              {{ getCategoryLabel(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="价格" width="100">
          <template #default="{ row }">
            <span class="price">¥{{ row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="campus" label="校区" width="100">
          <template #default="{ row }">
            <el-tag :type="getCampusTagType(row.campus)">
              {{ getCampusLabel(row.campus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="viewCount" label="浏览量" width="100" />
        <el-table-column prop="createTime" label="发布时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <!-- 查看按钮 -->
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>

            <!-- 根据商品的实际删除状态显示操作按钮 -->
            <!-- 已删除商品的操作 -->
            <template v-if="row.isDeleted">
              <el-button
                type="success"
                size="small"
                @click="handleRestore(row)"
              >
                恢复
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handlePermanentDelete(row)"
              >
                永久删除
              </el-button>
            </template>

            <!-- 正常商品的操作 -->
            <template v-else>
              <el-button
                v-if="row.status === 'published'"
                type="warning"
                size="small"
                @click="handleRemove(row)"
              >
                下架
              </el-button>
              <el-button
                v-if="row.status === 'removed'"
                type="success"
                size="small"
                @click="handlePublish(row)"
              >
                上架
              </el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">
                删除
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量操作对话框 -->
    <el-dialog
      v-model="batchDialogVisible"
      title="批量操作"
      width="400px"
    >
      <div class="batch-actions">
        <p>已选择 {{ selectedProducts.length }} 个商品</p>
        <div class="action-buttons">
          <!-- 已删除商品的批量操作 -->
          <template v-if="searchForm.isDeleted === 'true'">
            <el-button type="success" @click="handleBatchRestore">批量恢复</el-button>
            <el-button type="danger" @click="handleBatchPermanentDelete">批量永久删除</el-button>
          </template>

          <!-- 正常商品的批量操作 -->
          <template v-else>
            <el-button type="warning" @click="handleBatchRemove">批量下架</el-button>
            <el-button type="success" @click="handleBatchPublish">批量上架</el-button>
            <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
          </template>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="batchDialogVisible = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  Download,
  Operation,
  Picture,
  Search
} from '@element-plus/icons-vue'
import CategoryIcon from '@/components/common/CategoryIcon.vue'
import { formatTime } from '@/utils'
import type { Product } from '@/types'
import { PRODUCT_CATEGORY_LABELS, CAMPUS_LABELS } from '@/constants'
import { getProductsApi, updateProductStatusApi, deleteProductApi, restoreProductApi } from '@/request/productApi'

// 商品状态标签
const PRODUCT_STATUS_LABELS = {
  published: '已发布',
  removed: '已下架',
  sold: '已售出',
  draft: '草稿',
  pending: '待审核'
} as const

// 商品状况标签
const PRODUCT_CONDITION_LABELS = {
  brand_new: '全新',
  like_new: '几乎全新',
  excellent: '成色极佳',
  good: '成色良好',
  fair: '成色一般',
  poor: '成色较差'
} as const

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  title: '',
  sellerUsername: '',
  category: '',
  status: '',
  campus: '',
  condition: '',
  minPrice: undefined as number | undefined,
  maxPrice: undefined as number | undefined,
  dateRange: null as string[] | null,
  isDeleted: '' as string  // 删除状态筛选
})

// 商品列表数据
const products = ref<Product[]>([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 批量操作
const selectedProducts = ref<Product[]>([])
const batchDialogVisible = ref(false)

// 获取商品列表
const fetchProducts = async () => {
  loading.value = true
  try {
    // 构建搜索参数
    const params: any = {
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }

    // 添加搜索条件
    if (searchForm.title) {
      params.keyword = searchForm.title
    }
    if (searchForm.sellerUsername) {
      params.sellerUsername = searchForm.sellerUsername
    }
    if (searchForm.category) {
      params.category = searchForm.category
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.campus) {
      params.campus = searchForm.campus
    }
    if (searchForm.condition) {
      params.condition = searchForm.condition
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.minPrice !== undefined && searchForm.minPrice !== null) {
      params.minPrice = searchForm.minPrice
    }
    if (searchForm.maxPrice !== undefined && searchForm.maxPrice !== null) {
      params.maxPrice = searchForm.maxPrice
    }
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }
    if (searchForm.isDeleted) {
      params.isDeleted = searchForm.isDeleted
    }

    // 调用真实的API - 管理员可以看到所有商品
    const response = await getProductsApi(params)

    if (response.code === 200) {
      products.value = response.data.records
      total.value = response.data.total
    } else {
      ElMessage.error(response.msg || '获取商品列表失败')
      products.value = []
      total.value = 0
    }
  } catch (error) {
    ElMessage.error('获取商品列表失败')
    console.error('获取商品列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 标签样式函数
const getCategoryLabel = (category: string) => {
  return PRODUCT_CATEGORY_LABELS[category as keyof typeof PRODUCT_CATEGORY_LABELS] || category
}

const getCampusTagType = (campus: string) => {
  const typeMap: Record<string, string> = {
    main: '',
    east: 'success',
    west: 'warning',
    south: 'info'
  }
  return typeMap[campus] || ''
}

const getCampusLabel = (campus: string) => {
  return CAMPUS_LABELS[campus as keyof typeof CAMPUS_LABELS] || campus
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    published: 'success',
    removed: 'warning',
    sold: 'info',
    draft: '',
    pending: 'danger'
  }
  return typeMap[status] || ''
}

const getStatusLabel = (status: string) => {
  return PRODUCT_STATUS_LABELS[status as keyof typeof PRODUCT_STATUS_LABELS] || status
}

// 事件处理
const goBack = () => {
  router.push({ name: 'AdminDashboard' })
}

const refreshData = () => {
  fetchProducts()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchProducts()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    title: '',
    sellerUsername: '',
    category: '',
    status: '',
    campus: '',
    condition: '',
    minPrice: undefined,
    maxPrice: undefined,
    dateRange: null
  })
  handleSearch()
}

const handleView = (product: Product) => {
  router.push({ name: 'ProductDetail', params: { id: product.productId } })
}

const handleRemove = async (product: Product) => {
  try {
    await ElMessageBox.confirm(`确定要下架商品 "${product.title}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用API下架商品
    const response = await updateProductStatusApi(product.productId, { status: 'removed' })
    if (response.code === 200) {
      ElMessage.success('商品下架成功')
      fetchProducts()
    } else {
      ElMessage.error(response.msg || '下架失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('下架商品失败')
      console.error('下架商品失败:', error)
    }
  }
}

const handlePublish = async (product: Product) => {
  try {
    await ElMessageBox.confirm(`确定要上架商品 "${product.title}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用API上架商品
    const response = await updateProductStatusApi(product.productId, { status: 'published' })
    if (response.code === 200) {
      ElMessage.success('商品上架成功')
      fetchProducts()
    } else {
      ElMessage.error(response.msg || '上架失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('上架商品失败')
      console.error('上架商品失败:', error)
    }
  }
}

const handleDelete = async (product: Product) => {
  try {
    await ElMessageBox.confirm(
      `<div style="text-align: left;">
        <p style="margin-bottom: 16px; font-weight: 600;">确定要删除商品 "<span style="color: #409EFF;">${product.title}</span>" 吗？</p>

        <div style="background: #f0f9ff; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <p style="margin: 0; color: #1890ff; font-weight: 500;">📦 此操作为逻辑删除，商品将被移至回收站</p>
        </div>

        <div style="margin-bottom: 16px;">
          <p style="margin: 0 0 8px 0; font-weight: 500; color: #666;">影响范围：</p>
          <ul style="margin: 0; padding-left: 20px; color: #666;">
            <li style="margin-bottom: 4px;">商品从正常列表中隐藏</li>
            <li style="margin-bottom: 4px;">自动取消相关的未完成订单</li>
            <li style="margin-bottom: 4px;">相关评论将被隐藏</li>
            <li style="margin-bottom: 4px;">商品图片和收藏记录将保留</li>
          </ul>
        </div>

        <div style="background: #f6ffed; padding: 12px; border-radius: 6px; border-left: 4px solid #52c41a;">
          <p style="margin: 0; color: #52c41a; font-weight: 500;">✅ 可以在"已删除"列表中恢复此商品</p>
        </div>
      </div>`,
      '删除商品',
      {
        confirmButtonText: '移至回收站',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        center: true
      }
    )

    // 调用API逻辑删除商品
    const response = await deleteProductApi(product.productId)
    if (response.code === 200) {
      ElMessage.success('商品已移至回收站，可在"已删除"列表中恢复')
      fetchProducts()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除商品失败')
      console.error('删除商品失败:', error)
    }
  }
}

// 恢复已删除的商品
const handleRestore = async (product: Product) => {
  try {
    await ElMessageBox.confirm(
      `<div style="text-align: left;">
        <p style="margin-bottom: 16px; font-weight: 600;">确定要恢复商品 "<span style="color: #409EFF;">${product.title}</span>" 吗？</p>

        <div style="background: #f0f9ff; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <p style="margin: 0; color: #1890ff; font-weight: 500;">🔄 此操作将从回收站恢复商品</p>
        </div>

        <div style="margin-bottom: 16px;">
          <p style="margin: 0 0 8px 0; font-weight: 500; color: #666;">恢复内容：</p>
          <ul style="margin: 0; padding-left: 20px; color: #666;">
            <li style="margin-bottom: 4px;">商品重新进入正常商品列表</li>
            <li style="margin-bottom: 4px;">商品状态将重置为"草稿"</li>
            <li style="margin-bottom: 4px;">相关评论将重新显示</li>
            <li style="margin-bottom: 4px;">商品图片和收藏记录恢复可见</li>
          </ul>
        </div>

        <div style="background: #fff7e6; padding: 12px; border-radius: 6px; border-left: 4px solid #faad14;">
          <p style="margin: 0; color: #faad14; font-weight: 500;">📝 恢复后商品需要重新审核发布</p>
        </div>
      </div>`,
      '恢复商品',
      {
        confirmButtonText: '确定恢复',
        cancelButtonText: '取消',
        type: 'info',
        dangerouslyUseHTMLString: true,
        center: true
      }
    )

    // 调用恢复商品API
    const response = await restoreProductApi(product.productId)
    if (response.code === 200) {
      ElMessage.success('商品恢复成功，已重置为草稿状态')
      fetchProducts()
    } else {
      ElMessage.error(response.msg || '恢复失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('恢复商品失败')
      console.error('恢复商品失败:', error)
    }
  }
}

// 永久删除商品
const handlePermanentDelete = async (product: Product) => {
  try {
    await ElMessageBox.confirm(
      `<div style="text-align: left;">
        <div style="background: #fff2f0; padding: 12px; border-radius: 6px; margin-bottom: 16px; border-left: 4px solid #ff4d4f;">
          <p style="margin: 0; color: #ff4d4f; font-weight: 600;">⚠️ 危险操作：永久删除商品 "<span style="color: #ff4d4f;">${product.title}</span>"</p>
        </div>

        <div style="background: #fafafa; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <p style="margin: 0; color: #666; font-weight: 500;">🗑️ 此操作为物理删除，将彻底清除所有数据</p>
        </div>

        <div style="margin-bottom: 16px;">
          <p style="margin: 0 0 8px 0; font-weight: 500; color: #666;">删除内容：</p>
          <ul style="margin: 0; padding-left: 20px; color: #666;">
            <li style="margin-bottom: 4px;">商品基本信息（标题、描述、价格等）</li>
            <li style="margin-bottom: 4px;">所有商品图片记录</li>
            <li style="margin-bottom: 4px;">所有用户收藏记录</li>
            <li style="margin-bottom: 4px;">相关评论和回复</li>
            <li style="margin-bottom: 4px;">相关订单记录</li>
            <li style="margin-bottom: 4px;">相关举报记录</li>
          </ul>
        </div>

        <div style="background: #fff2f0; padding: 12px; border-radius: 6px; border: 1px solid #ffccc7;">
          <p style="margin: 0 0 8px 0; color: #ff4d4f; font-weight: 600;">❌ 此操作不可逆转，数据将永久丢失！</p>
          <p style="margin: 0 0 8px 0; color: #ff4d4f; font-weight: 600;">❌ 删除后无法通过任何方式恢复！</p>
          <p style="margin: 0; color: #ff4d4f; font-weight: 500;">请谨慎操作！</p>
        </div>
      </div>`,
      '永久删除商品',
      {
        confirmButtonText: '我确认永久删除',
        cancelButtonText: '取消',
        type: 'error',
        dangerouslyUseHTMLString: true,
        center: true
      }
    )

    // TODO: 调用永久删除商品API
    ElMessage.success('商品已永久删除')
    fetchProducts()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('永久删除失败')
      console.error('永久删除失败:', error)
    }
  }
}

const handleSelectionChange = (selection: Product[]) => {
  selectedProducts.value = selection
}

const handleBatchAction = () => {
  if (selectedProducts.value.length === 0) {
    ElMessage.warning('请先选择要操作的商品')
    return
  }
  batchDialogVisible.value = true
}

const handleBatchRemove = async () => {
  try {
    await ElMessageBox.confirm(`确定要下架选中的 ${selectedProducts.value.length} 个商品吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 批量下架商品
    let successCount = 0
    let failedCount = 0

    for (const product of selectedProducts.value) {
      try {
        const response = await updateProductStatusApi(product.productId, { status: 'removed' })
        if (response.code === 200) {
          successCount++
        } else {
          failedCount++
        }
      } catch (error) {
        failedCount++
        console.error(`下架商品 ${product.productId} 失败:`, error)
      }
    }

    if (failedCount === 0) {
      ElMessage.success(`批量下架成功，共下架 ${successCount} 个商品`)
    } else {
      ElMessage.warning(`下架完成，成功 ${successCount} 个，失败 ${failedCount} 个`)
    }

    batchDialogVisible.value = false
    fetchProducts()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('批量下架失败')
      console.error('批量下架失败:', error)
    }
  }
}

const handleBatchPublish = async () => {
  try {
    await ElMessageBox.confirm(`确定要上架选中的 ${selectedProducts.value.length} 个商品吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 批量上架商品
    let successCount = 0
    let failedCount = 0

    for (const product of selectedProducts.value) {
      try {
        const response = await updateProductStatusApi(product.productId, { status: 'published' })
        if (response.code === 200) {
          successCount++
        } else {
          failedCount++
        }
      } catch (error) {
        failedCount++
        console.error(`上架商品 ${product.productId} 失败:`, error)
      }
    }

    if (failedCount === 0) {
      ElMessage.success(`批量上架成功，共上架 ${successCount} 个商品`)
    } else {
      ElMessage.warning(`上架完成，成功 ${successCount} 个，失败 ${failedCount} 个`)
    }

    batchDialogVisible.value = false
    fetchProducts()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('批量上架失败')
      console.error('批量上架失败:', error)
    }
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `<div style="text-align: left;">
        <p style="margin-bottom: 16px; font-weight: 600;">确定要批量删除选中的 <span style="color: #409EFF;">${selectedProducts.value.length}</span> 个商品吗？</p>

        <div style="background: #f0f9ff; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <p style="margin: 0; color: #1890ff; font-weight: 500;">📦 此操作为逻辑删除，商品将被移至回收站</p>
        </div>

        <div style="margin-bottom: 16px;">
          <p style="margin: 0 0 8px 0; font-weight: 500; color: #666;">批量操作影响：</p>
          <ul style="margin: 0; padding-left: 20px; color: #666;">
            <li style="margin-bottom: 4px;">所有选中商品从正常列表中隐藏</li>
            <li style="margin-bottom: 4px;">自动取消相关的未完成订单</li>
            <li style="margin-bottom: 4px;">相关评论将被隐藏</li>
            <li style="margin-bottom: 4px;">商品图片和收藏记录将保留</li>
          </ul>
        </div>

        <div style="background: #f6ffed; padding: 12px; border-radius: 6px; border-left: 4px solid #52c41a;">
          <p style="margin: 0; color: #52c41a; font-weight: 500;">✅ 可以在"已删除"列表中批量恢复这些商品</p>
        </div>
      </div>`,
      '批量删除商品',
      {
        confirmButtonText: '批量移至回收站',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        center: true
      }
    )

    // 批量删除商品
    let successCount = 0
    let failedCount = 0

    for (const product of selectedProducts.value) {
      try {
        const response = await deleteProductApi(product.productId)
        if (response.code === 200) {
          successCount++
        } else {
          failedCount++
        }
      } catch (error) {
        failedCount++
        console.error(`删除商品 ${product.productId} 失败:`, error)
      }
    }

    if (failedCount === 0) {
      ElMessage.success(`批量删除成功，共 ${successCount} 个商品已移至回收站`)
    } else {
      ElMessage.warning(`批量删除完成，成功 ${successCount} 个，失败 ${failedCount} 个`)
    }

    batchDialogVisible.value = false
    fetchProducts()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
      console.error('批量删除失败:', error)
    }
  }
}

// 批量恢复商品
const handleBatchRestore = async () => {
  try {
    await ElMessageBox.confirm(
      `<div style="text-align: left;">
        <p style="margin-bottom: 16px; font-weight: 600;">确定要批量恢复选中的 <span style="color: #409EFF;">${selectedProducts.value.length}</span> 个商品吗？</p>

        <div style="background: #f0f9ff; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <p style="margin: 0; color: #1890ff; font-weight: 500;">🔄 此操作将从回收站恢复所有选中商品</p>
        </div>

        <div style="margin-bottom: 16px;">
          <p style="margin: 0 0 8px 0; font-weight: 500; color: #666;">批量恢复影响：</p>
          <ul style="margin: 0; padding-left: 20px; color: #666;">
            <li style="margin-bottom: 4px;">所有选中商品重新进入正常商品列表</li>
            <li style="margin-bottom: 4px;">商品状态将重置为"草稿"</li>
            <li style="margin-bottom: 4px;">相关评论将重新显示</li>
            <li style="margin-bottom: 4px;">商品图片和收藏记录恢复可见</li>
          </ul>
        </div>

        <div style="background: #fff7e6; padding: 12px; border-radius: 6px; border-left: 4px solid #faad14;">
          <p style="margin: 0; color: #faad14; font-weight: 500;">📝 恢复后所有商品需要重新审核发布</p>
        </div>
      </div>`,
      '批量恢复商品',
      {
        confirmButtonText: '确定批量恢复',
        cancelButtonText: '取消',
        type: 'info',
        dangerouslyUseHTMLString: true,
        center: true
      }
    )

    // 批量恢复商品
    let successCount = 0
    let failedCount = 0

    for (const product of selectedProducts.value) {
      try {
        const response = await restoreProductApi(product.productId)
        if (response.code === 200) {
          successCount++
        } else {
          failedCount++
        }
      } catch (error) {
        failedCount++
        console.error(`恢复商品 ${product.productId} 失败:`, error)
      }
    }

    if (failedCount === 0) {
      ElMessage.success(`批量恢复成功，共恢复 ${successCount} 个商品`)
    } else {
      ElMessage.warning(`恢复完成，成功 ${successCount} 个，失败 ${failedCount} 个`)
    }

    batchDialogVisible.value = false
    fetchProducts()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('批量恢复失败')
      console.error('批量恢复失败:', error)
    }
  }
}

// 批量永久删除商品
const handleBatchPermanentDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `<div style="text-align: left;">
        <div style="background: #fff2f0; padding: 12px; border-radius: 6px; margin-bottom: 16px; border-left: 4px solid #ff4d4f;">
          <p style="margin: 0; color: #ff4d4f; font-weight: 600;">⚠️ 危险操作：批量永久删除选中的 <span style="color: #ff4d4f;">${selectedProducts.value.length}</span> 个商品</p>
        </div>

        <div style="background: #fafafa; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <p style="margin: 0; color: #666; font-weight: 500;">🗑️ 此操作为物理删除，将彻底清除所有数据</p>
        </div>

        <div style="margin-bottom: 16px;">
          <p style="margin: 0 0 8px 0; font-weight: 500; color: #666;">批量删除内容：</p>
          <ul style="margin: 0; padding-left: 20px; color: #666;">
            <li style="margin-bottom: 4px;">所有选中商品的基本信息</li>
            <li style="margin-bottom: 4px;">所有相关图片记录</li>
            <li style="margin-bottom: 4px;">所有用户收藏记录</li>
            <li style="margin-bottom: 4px;">相关评论和回复</li>
            <li style="margin-bottom: 4px;">相关订单记录</li>
            <li style="margin-bottom: 4px;">相关举报记录</li>
          </ul>
        </div>

        <div style="background: #fff2f0; padding: 12px; border-radius: 6px; border: 1px solid #ffccc7;">
          <p style="margin: 0 0 8px 0; color: #ff4d4f; font-weight: 600;">❌ 此操作不可逆转，数据将永久丢失！</p>
          <p style="margin: 0 0 8px 0; color: #ff4d4f; font-weight: 600;">❌ 删除后无法通过任何方式恢复！</p>
          <p style="margin: 0; color: #ff4d4f; font-weight: 500;">请谨慎操作！</p>
        </div>
      </div>`,
      '批量永久删除确认',
      {
        confirmButtonText: '我确认批量永久删除',
        cancelButtonText: '取消',
        type: 'error',
        dangerouslyUseHTMLString: true,
        center: true
      }
    )

    // TODO: 批量永久删除商品
    ElMessage.success(`批量永久删除成功，共删除 ${selectedProducts.value.length} 个商品`)
    batchDialogVisible.value = false
    fetchProducts()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('批量永久删除失败')
      console.error('批量永久删除失败:', error)
    }
  }
}

const handleExport = () => {
  try {
    // 准备导出数据
    const exportData = products.value.map(product => ({
      '商品ID': product.productId,
      '商品标题': product.title,
      '分类': getCategoryLabel(product.category),
      '价格': `¥${product.price}`,
      '原价': product.originalPrice ? `¥${product.originalPrice}` : '',
      '新旧程度': product.condition,
      '卖家ID': product.sellerId,
      '校区': getCampusLabel(product.campus),
      '状态': getStatusLabel(product.status),
      '浏览量': product.viewCount,
      '点赞数': product.likeCount,
      '发布时间': formatTime(product.createTime),
      '更新时间': formatTime(product.updateTime)
    }))

    // 转换为CSV格式
    const headers = Object.keys(exportData[0])
    const csvContent = [
      headers.join(','),
      ...exportData.map(row =>
        headers.map(header => `"${row[header] || ''}"`).join(',')
      )
    ].join('\n')

    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `商品数据_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('商品数据导出成功')
  } catch (error) {
    ElMessage.error('导出失败，请重试')
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchProducts()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchProducts()
}

onMounted(() => {
  fetchProducts()
})
</script>

<style lang="stylus" scoped>
.product-management
  width 100%
  padding 20px

.page-header
  display flex
  justify-content space-between
  align-items center
  margin-bottom 20px
  
  h2
    margin 0
    color #333
    
  .header-actions
    display flex
    gap 10px

.search-card
  margin-bottom 20px

.table-card
  .card-header
    display flex
    justify-content space-between
    align-items center

.pagination-wrapper
  display flex
  justify-content center
  margin-top 20px

.price
  color #f56c6c
  font-weight bold

.category-tag
  display flex
  align-items center
  gap 4px

.image-slot
  display flex
  justify-content center
  align-items center
  width 100%
  height 100%
  background #f5f7fa
  color #909399
  font-size 20px

.batch-actions
  text-align center
  
  .action-buttons
    display flex
    justify-content center
    gap 10px
    margin-top 20px

@media (max-width: 768px)
  .product-management
    padding 10px
    
  .page-header
    flex-direction column
    gap 15px
    align-items stretch
    
  .header-actions
    justify-content center
</style>
