<template>
  <el-header class="app-header">
    <div class="header-left">
      <div class="logo" @click="goHome">
        <el-icon><Shop /></el-icon>
        <span class="logo-text">校园二手</span>
      </div>
    </div>
    
    <div class="header-center">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索商品..."
        class="search-input"
        @keyup.enter="handleSearch"
        @focus="showSearchHistory = true"
        @blur="hideSearchHistory"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
        <template #suffix>
          <el-button 
            type="primary" 
            size="small"
            @click="handleSearch"
          >
            搜索
          </el-button>
        </template>
      </el-input>
      
      <!-- 搜索历史下拉 -->
      <div 
        v-show="showSearchHistory && searchHistory.length > 0"
        class="search-history"
      >
        <div class="history-header">
          <span>搜索历史</span>
          <el-button 
            type="text" 
            size="small"
            @click="clearHistory"
          >
            清除
          </el-button>
        </div>
        <div class="history-list">
          <div
            v-for="item in searchHistory"
            :key="item"
            class="history-item"
            @click="selectHistory(item)"
          >
            {{ item }}
          </div>
        </div>
      </div>
    </div>
    
    <div class="header-right">
      <!-- 发布商品按钮 -->
      <el-button 
        type="primary"
        @click="goPublish"
        v-if="userStore.isLoggedIn"
      >
        <el-icon><Plus /></el-icon>
        发布
      </el-button>
      
      <!-- 消息通知 -->
      <el-badge 
        :value="appStore.unreadMessageCount" 
        :hidden="appStore.unreadMessageCount === 0"
        class="message-badge"
        v-if="userStore.isLoggedIn"
      >
        <el-button 
          type="text" 
          @click="goMessages"
          class="icon-button"
        >
          <el-icon><Bell /></el-icon>
        </el-button>
      </el-badge>
      
      <!-- 用户菜单 -->
      <el-dropdown v-if="userStore.isLoggedIn" @command="handleUserCommand">
        <div class="user-info">
          <el-avatar 
            :src="userStore.userInfo?.avatar" 
            :size="32"
          >
            {{ userStore.userInfo?.nickname?.charAt(0) }}
          </el-avatar>
          <span class="username">{{ userStore.userInfo?.nickname }}</span>
          <el-icon><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">个人中心</el-dropdown-item>
            <el-dropdown-item command="orders">我的订单</el-dropdown-item>
            <el-dropdown-item command="products">我的商品</el-dropdown-item>
            <el-dropdown-item command="favorites">我的收藏</el-dropdown-item>
            <el-dropdown-item command="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      
      <!-- 未登录状态 -->
      <div v-else class="auth-buttons">
        <el-button @click="goLogin">登录</el-button>
        <el-button type="primary" @click="goRegister">注册</el-button>
      </div>
    </div>
  </el-header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Shop, Search, Plus, Bell, ArrowDown 
} from '@element-plus/icons-vue'
import { useUserStore, useAppStore } from '@/stores'

const router = useRouter()
const userStore = useUserStore()
const appStore = useAppStore()

// 搜索相关
const searchKeyword = ref('')
const showSearchHistory = ref(false)
const searchHistory = computed(() => appStore.searchHistory)

// 搜索功能
const handleSearch = () => {
  if (!searchKeyword.value.trim()) return
  
  appStore.addSearchHistory(searchKeyword.value.trim())
  router.push({
    name: 'ProductList',
    query: { keyword: searchKeyword.value.trim() }
  })
  showSearchHistory.value = false
}

// 选择搜索历史
const selectHistory = (keyword: string) => {
  searchKeyword.value = keyword
  handleSearch()
}

// 清除搜索历史
const clearHistory = () => {
  appStore.clearSearchHistory()
}

// 隐藏搜索历史（延迟执行，避免点击历史项时立即隐藏）
const hideSearchHistory = () => {
  setTimeout(() => {
    showSearchHistory.value = false
  }, 200)
}

// 导航功能
const goHome = () => {
  // 如果是管理员，跳转到后台首页
  if (userStore.isAdmin) {
    router.push('/admin')
  } else {
    // 普通用户跳转到前台首页
    router.push('/')
  }
}

const goPublish = () => {
  router.push('/publish')
}

const goMessages = () => {
  router.push('/messages')
}

const goLogin = () => {
  router.push('/login')
}

const goRegister = () => {
  router.push('/register')
}

// 用户菜单处理
const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'orders':
      router.push('/orders')
      break
    case 'products':
      router.push('/my-products')
      break
    case 'favorites':
      router.push('/favorites')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await userStore.logout()
        ElMessage.success('退出登录成功')
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}

onMounted(() => {
  appStore.initSearchHistory()
})
</script>

<style lang="stylus" scoped>
.app-header
  display flex
  align-items center
  justify-content space-between
  padding 0 20px
  background #fff
  border-bottom 1px solid #e4e7ed
  box-shadow 0 2px 4px rgba(0, 0, 0, 0.1)

.header-left
  .logo
    display flex
    align-items center
    cursor pointer
    font-size 18px
    font-weight bold
    color #409eff
    
    .logo-text
      margin-left 8px

.header-center
  flex 1
  max-width 600px
  margin 0 40px
  position relative
  
  .search-input
    width 100%
    
  .search-history
    position absolute
    top 100%
    left 0
    right 0
    background #fff
    border 1px solid #e4e7ed
    border-top none
    border-radius 0 0 4px 4px
    box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
    z-index 1000
    
    .history-header
      display flex
      justify-content space-between
      align-items center
      padding 8px 12px
      border-bottom 1px solid #f0f0f0
      font-size 12px
      color #909399
      
    .history-list
      max-height 200px
      overflow-y auto
      
      .history-item
        padding 8px 12px
        cursor pointer
        font-size 14px
        
        &:hover
          background #f5f7fa

.header-right
  display flex
  align-items center
  gap 16px
  
  .message-badge
    .icon-button
      padding 8px
      
  .user-info
    display flex
    align-items center
    gap 8px
    cursor pointer
    padding 4px 8px
    border-radius 4px
    
    &:hover
      background #f5f7fa
      
    .username
      font-size 14px
      
  .auth-buttons
    display flex
    gap 8px

@media (max-width: 768px)
  .app-header
    padding 0 12px
    
  .header-center
    margin 0 16px
    
  .header-right
    gap 8px
    
    .auth-buttons
      gap 4px
</style>