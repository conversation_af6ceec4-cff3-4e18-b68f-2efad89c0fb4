<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="contact-seller">
      <!-- 卖家信息 -->
      <div class="seller-info">
        <el-avatar :src="sellerInfo.avatar" :size="50">
          {{ sellerInfo.nickname?.charAt(0) }}
        </el-avatar>
        <div class="seller-details">
          <h4>{{ sellerInfo.nickname }}</h4>
          <div class="seller-stats">
            <el-tag size="small" type="success">信誉良好</el-tag>
            <span class="stat-item">成交 {{ sellerInfo.dealCount || 0 }} 笔</span>
            <span class="stat-item">好评率 {{ sellerInfo.goodRate || 95 }}%</span>
          </div>
        </div>
      </div>

      <!-- 联系方式选择 -->
      <div class="contact-methods">
        <h5>选择联系方式：</h5>
        <el-radio-group v-model="contactMethod" class="contact-radio-group">
          <div class="radio-item" :class="{ selected: contactMethod === 'message' }">
            <el-radio value="message">发送站内消息</el-radio>
          </div>
          <div class="radio-item" :class="{ selected: contactMethod === 'phone' }">
            <el-radio value="phone" :disabled="!sellerInfo.showPhone">查看手机号</el-radio>
          </div>
          <div class="radio-item" :class="{ selected: contactMethod === 'wechat' }">
            <el-radio value="wechat" :disabled="!sellerInfo.showWechat">查看微信号</el-radio>
          </div>
        </el-radio-group>
      </div>

      <!-- 发送消息 -->
      <div v-if="contactMethod === 'message'" class="message-section">
        <h5>发送消息：</h5>
        <el-input
          v-model="messageContent"
          type="textarea"
          :rows="4"
          placeholder="请输入您想对卖家说的话..."
          maxlength="200"
          show-word-limit
        />
        <div class="quick-messages">
          <span class="quick-label">快捷消息：</span>
          <el-button 
            v-for="msg in quickMessages" 
            :key="msg"
            size="small"
            @click="messageContent = msg"
          >
            {{ msg }}
          </el-button>
        </div>
      </div>

      <!-- 查看联系方式 -->
      <div v-else-if="contactMethod === 'phone'" class="contact-info">
        <div class="contact-item">
          <el-icon><Phone /></el-icon>
          <span>手机号：{{ sellerInfo.phone || '138****8888' }}</span>
          <el-button size="small" @click="copyToClipboard(sellerInfo.phone)">复制</el-button>
        </div>
        <div class="contact-tip">
          <el-icon><InfoFilled /></el-icon>
          <span>请通过正当渠道联系，注意个人信息安全</span>
        </div>
      </div>

      <div v-else-if="contactMethod === 'wechat'" class="contact-info">
        <div class="contact-item">
          <el-icon><ChatDotRound /></el-icon>
          <span>微信号：{{ sellerInfo.wechat || 'user_wechat_123' }}</span>
          <el-button size="small" @click="copyToClipboard(sellerInfo.wechat)">复制</el-button>
        </div>
        <div class="contact-tip">
          <el-icon><InfoFilled /></el-icon>
          <span>添加微信时请说明来意，文明交流</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          v-if="contactMethod === 'message'"
          type="primary" 
          @click="sendMessage"
          :loading="sending"
          :disabled="!messageContent.trim()"
        >
          发送消息
        </el-button>
        <el-button 
          v-else
          type="primary" 
          @click="handleClose"
        >
          知道了
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Phone, 
  ChatDotRound, 
  InfoFilled 
} from '@element-plus/icons-vue'

interface SellerInfo {
  userId: number
  nickname: string
  avatar?: string
  phone?: string
  wechat?: string
  showPhone: boolean
  showWechat: boolean
  dealCount?: number
  goodRate?: number
}

interface Props {
  modelValue: boolean
  sellerInfo: SellerInfo
  title?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'message-sent'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '联系卖家'
})
const emit = defineEmits<Emits>()

const visible = ref(props.modelValue)
const contactMethod = ref('message')
const messageContent = ref('')
const sending = ref(false)

// 快捷消息
const quickMessages = [
  '您好，我对这个商品很感兴趣',
  '请问商品还在吗？',
  '可以面交吗？在哪里交易？',
  '价格还能商量吗？',
  '商品的具体情况怎么样？'
]

// 发送消息
const sendMessage = async () => {
  if (!messageContent.value.trim()) {
    ElMessage.warning('请输入消息内容')
    return
  }

  sending.value = true
  try {
    // 模拟发送消息
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('消息发送成功')
    emit('message-sent')
    handleClose()
  } catch (error) {
    ElMessage.error('消息发送失败，请重试')
  } finally {
    sending.value = false
  }
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  emit('update:modelValue', false)
  // 重置表单
  contactMethod.value = 'message'
  messageContent.value = ''
}

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})
</script>

<style lang="stylus" scoped>
.contact-seller
  .seller-info
    display flex
    align-items center
    gap 15px
    padding 15px
    background #f8f9fa
    border-radius 8px
    margin-bottom 20px
    
    .seller-details
      flex 1
      
      h4
        margin 0 0 8px 0
        color #333
        
      .seller-stats
        display flex
        align-items center
        gap 10px
        
        .stat-item
          font-size 12px
          color #666

  .contact-methods
    margin-bottom 20px
    
    h5
      margin 0 0 10px 0
      color #333
      font-size 14px
      
    .contact-radio-group
      display flex
      flex-direction column
      gap 0

      .radio-item
        padding 12px 16px
        border-bottom 1px solid #f5f7fa
        border-radius 6px
        margin-bottom 8px
        background #fafbfc
        transition all 0.2s

        &:last-child
          border-bottom 1px solid #f5f7fa
          margin-bottom 0

        &:hover
          background #f0f9ff

        &.selected
          background #e1f3ff
          border-color #409eff

        .el-radio
          margin 0
          width 100%
          display flex
          align-items center
          min-height 24px

          :deep(.el-radio__input)
            margin-right 12px
            flex-shrink 0

          :deep(.el-radio__label)
            padding-left 0
            line-height 1.5
            font-size 14px
            flex 1

          &.is-disabled
            :deep(.el-radio__label)
              color #c0c4cc

  .message-section
    h5
      margin 0 0 10px 0
      color #333
      font-size 14px
      
    .quick-messages
      margin-top 10px
      display flex
      flex-wrap wrap
      align-items center
      gap 8px
      
      .quick-label
        font-size 12px
        color #666
        margin-right 5px

  .contact-info
    .contact-item
      display flex
      align-items center
      gap 10px
      padding 15px
      background #f8f9fa
      border-radius 8px
      margin-bottom 10px
      
      .el-icon
        color #409eff
        
    .contact-tip
      display flex
      align-items center
      gap 8px
      font-size 12px
      color #666
      
      .el-icon
        color #e6a23c

.dialog-footer
  display flex
  justify-content flex-end
  gap 10px
</style>
