import request from "./request";
import type { Product, ProductCreateRequest, ProductUpdateRequest } from "@/types";

// API响应类型
type ApiResponse<T = any> = {
  code: number;
  msg: string;
  data: T;
}

// 分页响应类型
type PageResponse<T> = {
  records: T[];
  total: number;
  pageNum: number;
  pageSize: number;
  totalPages: number;
}

// 商品列表查询参数
export interface ProductListParams {
  category?: string;
  campus?: string;
  condition?: string;
  minPrice?: number;
  maxPrice?: number;
  keyword?: string;
  sellerUsername?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: string;
  pageNum?: number;
  pageSize?: number;
}

// 商品分类信息
export interface ProductCategory {
  categoryId: number;
  categoryKey: string;
  categoryName: string;
  icon: string;
  sortOrder: number;
  status: boolean;
}

// 商品状态更新请求
export interface ProductStatusUpdateRequest {
  status: string;
}

/**
 * 获取商品列表
 */
export const getProductsApi = (params?: ProductListParams): Promise<ApiResponse<PageResponse<Product>>> => {
  return request.get('/products', { params });
}

/**
 * 获取商品详情
 */
export const getProductDetailApi = (productId: number): Promise<ApiResponse<Product>> => {
  return request.get(`/products/${productId}`);
}

/**
 * 发布商品
 */
export const createProductApi = (data: ProductCreateRequest): Promise<ApiResponse<Product>> => {
  return request.post('/products', data);
}

/**
 * 更新商品信息
 */
export const updateProductApi = (productId: number, data: ProductUpdateRequest): Promise<ApiResponse<Product>> => {
  return request.put(`/products/${productId}`, data);
}

/**
 * 更新商品状态
 */
export const updateProductStatusApi = (productId: number, data: ProductStatusUpdateRequest): Promise<ApiResponse<Product>> => {
  return request.put(`/products/${productId}/status`, data);
}

/**
 * 删除商品
 */
export const deleteProductApi = (productId: number): Promise<ApiResponse<boolean>> => {
  return request.delete(`/products/${productId}`);
}

/**
 * 恢复已删除的商品
 */
export const restoreProductApi = (productId: number): Promise<ApiResponse<boolean>> => {
  return request.post(`/products/${productId}/restore`);
}

/**
 * 获取我的商品列表
 */
export const getMyProductsApi = (params?: {
  status?: string;
  pageNum?: number;
  pageSize?: number;
}): Promise<ApiResponse<PageResponse<Product>>> => {
  return request.get('/products/my', { params });
}

/**
 * 获取商品分类列表
 */
export const getProductCategoriesApi = (): Promise<ApiResponse<ProductCategory[]>> => {
  return request.get('/products/categories/list');
}
