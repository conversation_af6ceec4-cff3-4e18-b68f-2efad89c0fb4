from typing import Optional, Dict, Any, List
from tortoise.expressions import Q
from tortoise.functions import Count, Sum
from datetime import datetime, timedelta
from decimal import Decimal

from app.models.order import Order
from app.models.user import User
from app.models.product import Product
from app.schemas.order_management import OrderListParams, OrderInfo, UserInfo, ProductInfo

class OrderManagementCRUD:
    
    async def get_orders_list(self, params: OrderListParams) -> Dict[str, Any]:
        """获取订单列表"""
        # 构建查询条件
        query = Order.all()
        
        # 添加筛选条件
        if params.order_no:
            query = query.filter(order_no__icontains=params.order_no)
        
        if params.status:
            query = query.filter(status=params.status)
            
        if params.start_date:
            start_date = datetime.fromisoformat(params.start_date.replace('Z', '+00:00'))
            query = query.filter(create_time__gte=start_date)
            
        if params.end_date:
            end_date = datetime.fromisoformat(params.end_date.replace('Z', '+00:00'))
            query = query.filter(create_time__lte=end_date)
            
        if params.min_price:
            query = query.filter(price__gte=params.min_price)
            
        if params.max_price:
            query = query.filter(price__lte=params.max_price)
        
        # 关联查询用户和商品信息进行筛选
        if params.buyer_username:
            buyer_ids = await User.filter(username__icontains=params.buyer_username).values_list('user_id', flat=True)
            if buyer_ids:
                query = query.filter(buyer_id__in=buyer_ids)
            else:
                # 如果没有找到匹配的买家，返回空结果
                return {
                    "records": [],
                    "total": 0,
                    "pageNum": params.pageNum,
                    "pageSize": params.pageSize,
                    "totalPages": 0
                }
        
        if params.seller_username:
            seller_ids = await User.filter(username__icontains=params.seller_username).values_list('user_id', flat=True)
            if seller_ids:
                query = query.filter(seller_id__in=seller_ids)
            else:
                return {
                    "records": [],
                    "total": 0,
                    "pageNum": params.pageNum,
                    "pageSize": params.pageSize,
                    "totalPages": 0
                }
        
        if params.product_title:
            product_ids = await Product.filter(title__icontains=params.product_title).values_list('product_id', flat=True)
            if product_ids:
                query = query.filter(product_id__in=product_ids)
            else:
                return {
                    "records": [],
                    "total": 0,
                    "pageNum": params.pageNum,
                    "pageSize": params.pageSize,
                    "totalPages": 0
                }
        
        # 获取总数
        total = await query.count()
        
        # 排序
        if params.sortOrder == "asc":
            query = query.order_by(params.sortBy)
        else:
            query = query.order_by(f"-{params.sortBy}")
        
        # 分页
        offset = (params.pageNum - 1) * params.pageSize
        orders = await query.offset(offset).limit(params.pageSize).prefetch_related('buyer', 'seller', 'product')
        
        # 获取关联的用户和商品信息
        order_records = []
        for order in orders:
            # 构建买家信息
            buyer_info = None
            if order.buyer:
                buyer_info = UserInfo(
                    userId=order.buyer.user_id,
                    username=order.buyer.username,
                    nickname=order.buyer.nickname,
                    phone=order.buyer.phone,
                    email=order.buyer.email
                )

            # 构建卖家信息
            seller_info = None
            if order.seller:
                seller_info = UserInfo(
                    userId=order.seller.user_id,
                    username=order.seller.username,
                    nickname=order.seller.nickname,
                    phone=order.seller.phone,
                    email=order.seller.email
                )

            # 构建商品信息
            product_info = None
            if order.product:
                # 获取商品图片
                product_images = await order.product.images.all()
                image_urls = [img.image_url for img in product_images]

                product_info = ProductInfo(
                    productId=order.product.product_id,
                    title=order.product.title,
                    category=order.product.category,
                    price=order.product.price,
                    images=image_urls
                )
            
            order_info = OrderInfo(
                orderId=order.order_id,
                orderNo=order.order_no,
                productId=order.product.product_id if order.product else 0,
                productInfo=product_info,
                buyerId=order.buyer.user_id if order.buyer else 0,
                buyerInfo=buyer_info,
                sellerId=order.seller.user_id if order.seller else 0,
                sellerInfo=seller_info,
                price=order.price,
                status=order.status,
                pickupLocation=order.pickup_location,
                pickupTime=order.pickup_time,
                paymentMethod='cash' if order.status != 'pending_payment' else None,  # 临时默认值
                remark=order.remark,
                createTime=order.create_time,
                updateTime=order.update_time
            )
            order_records.append(order_info)
        
        total_pages = (total + params.pageSize - 1) // params.pageSize
        
        return {
            "records": order_records,
            "total": total,
            "pageNum": params.pageNum,
            "pageSize": params.pageSize,
            "totalPages": total_pages
        }
    
    async def get_order_by_id(self, order_id: int) -> Optional[OrderInfo]:
        """根据ID获取订单详情"""
        order = await Order.get_or_none(order_id=order_id)
        if not order:
            return None
        
        # 获取关联信息 - 使用外键关系
        await order.fetch_related('buyer', 'seller', 'product')
        buyer = order.buyer
        seller = order.seller
        product = order.product
        
        buyer_info = None
        if buyer:
            buyer_info = UserInfo(
                userId=buyer.user_id,
                username=buyer.username,
                nickname=buyer.nickname,
                phone=buyer.phone,
                email=buyer.email
            )
        
        seller_info = None
        if seller:
            seller_info = UserInfo(
                userId=seller.user_id,
                username=seller.username,
                nickname=seller.nickname,
                phone=seller.phone,
                email=seller.email
            )
        
        product_info = None
        if product:
            # 获取商品图片
            product_images = await product.images.all()
            image_urls = [img.image_url for img in product_images]

            product_info = ProductInfo(
                productId=product.product_id,
                title=product.title,
                category=product.category,
                price=product.price,
                images=image_urls
            )
        
        return OrderInfo(
            orderId=order.order_id,
            orderNo=order.order_no,
            productId=product.product_id if product else 0,
            productInfo=product_info,
            buyerId=buyer.user_id if buyer else 0,
            buyerInfo=buyer_info,
            sellerId=seller.user_id if seller else 0,
            sellerInfo=seller_info,
            price=order.price,
            status=order.status,
            pickupLocation=order.pickup_location,
            pickupTime=order.pickup_time,
            paymentMethod='cash' if order.status != 'pending_payment' else None,  # 临时默认值
            remark=order.remark,
            createTime=order.create_time,
            updateTime=order.update_time
        )
    
    async def update_order_status(self, order_id: int, status: str, remark: Optional[str] = None) -> Optional[OrderInfo]:
        """更新订单状态"""
        order = await Order.get_or_none(order_id=order_id)
        if not order:
            return None
        
        order.status = status
        if remark:
            order.remark = remark
        await order.save()
        
        return await self.get_order_by_id(order_id)
    
    async def delete_order(self, order_id: int) -> bool:
        """删除订单"""
        order = await Order.get_or_none(order_id=order_id)
        if not order:
            return False
        
        await order.delete()
        return True

order_management_crud = OrderManagementCRUD()
