# 简化的商品接口测试

Write-Output "开始测试商品接口..."

# 1. 登录获取Token
Write-Output "1. 测试登录接口"
$loginBody = '{"username": "admin", "password": "123456"}'
$loginResponse = Invoke-WebRequest -Uri 'http://localhost:18080/api/login' -Method POST -ContentType 'application/json' -Body $loginBody
$loginData = $loginResponse.Content | ConvertFrom-Json
$token = $loginData.data.token
$headers = @{"Authorization" = "Bearer $token"}

Write-Output "登录成功: $($loginResponse.StatusCode)"
Write-Output "Token: $($token.Substring(0,20))..."

# 2. 测试商品列表
Write-Output "2. 测试商品列表"
$response = Invoke-WebRequest -Uri 'http://localhost:18080/api/products' -Method GET -Headers $headers
$data = $response.Content | ConvertFrom-Json
Write-Output "商品列表: $($response.StatusCode)"
Write-Output "总商品数: $($data.data.total)"

# 3. 测试分类筛选
Write-Output "3. 测试分类筛选"
$response = Invoke-WebRequest -Uri 'http://localhost:18080/api/products?category=textbook' -Method GET -Headers $headers
$data = $response.Content | ConvertFrom-Json
Write-Output "分类筛选: $($response.StatusCode)"
Write-Output "教材类商品数: $($data.data.total)"

# 4. 测试分类列表
Write-Output "4. 测试分类列表"
$response = Invoke-WebRequest -Uri 'http://localhost:18080/api/products/categories/list' -Method GET
$data = $response.Content | ConvertFrom-Json
Write-Output "分类列表: $($response.StatusCode)"
Write-Output "可用分类数: $($data.data.Count)"

Write-Output "测试完成！"
