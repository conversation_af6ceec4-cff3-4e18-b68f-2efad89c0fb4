// 应用全局状态管理
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 加载状态
  const loading = ref(false)
  
  // 侧边栏折叠状态
  const sidebarCollapsed = ref(false)
  
  // 设备类型
  const device = ref<'desktop' | 'tablet' | 'mobile'>('desktop')
  
  // 主题模式
  const theme = ref<'light' | 'dark'>('light')
  
  // 语言
  const locale = ref('zh-CN')
  
  // 搜索历史
  const searchHistory = ref<string[]>([])
  
  // 购物车商品数量
  const cartCount = ref(0)
  
  // 未读消息数量
  const unreadMessageCount = ref(0)

  // 设置加载状态
  const setLoading = (status: boolean) => {
    loading.value = status
  }

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  // 设置设备类型
  const setDevice = (deviceType: 'desktop' | 'tablet' | 'mobile') => {
    device.value = deviceType
  }

  // 切换主题
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
  }

  // 设置语言
  const setLocale = (lang: string) => {
    locale.value = lang
  }

  // 添加搜索历史
  const addSearchHistory = (keyword: string) => {
    if (!keyword.trim()) return
    
    // 移除重复项
    const index = searchHistory.value.indexOf(keyword)
    if (index > -1) {
      searchHistory.value.splice(index, 1)
    }
    
    // 添加到开头
    searchHistory.value.unshift(keyword)
    
    // 限制历史记录数量
    if (searchHistory.value.length > 10) {
      searchHistory.value = searchHistory.value.slice(0, 10)
    }
    
    // 保存到本地存储
    localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value))
  }

  // 清除搜索历史
  const clearSearchHistory = () => {
    searchHistory.value = []
    localStorage.removeItem('searchHistory')
  }

  // 初始化搜索历史
  const initSearchHistory = () => {
    try {
      const history = localStorage.getItem('searchHistory')
      if (history) {
        searchHistory.value = JSON.parse(history)
      }
    } catch (error) {
      console.error('初始化搜索历史失败:', error)
    }
  }

  // 设置购物车数量
  const setCartCount = (count: number) => {
    cartCount.value = count
  }

  // 增加购物车数量
  const incrementCartCount = () => {
    cartCount.value++
  }

  // 减少购物车数量
  const decrementCartCount = () => {
    if (cartCount.value > 0) {
      cartCount.value--
    }
  }

  // 设置未读消息数量
  const setUnreadMessageCount = (count: number) => {
    unreadMessageCount.value = count
  }

  // 增加未读消息数量
  const incrementUnreadMessageCount = () => {
    unreadMessageCount.value++
  }

  // 减少未读消息数量
  const decrementUnreadMessageCount = () => {
    if (unreadMessageCount.value > 0) {
      unreadMessageCount.value--
    }
  }

  // 清除未读消息数量
  const clearUnreadMessageCount = () => {
    unreadMessageCount.value = 0
  }

  return {
    // 状态
    loading,
    sidebarCollapsed,
    device,
    theme,
    locale,
    searchHistory,
    cartCount,
    unreadMessageCount,
    
    // 方法
    setLoading,
    toggleSidebar,
    setDevice,
    toggleTheme,
    setLocale,
    addSearchHistory,
    clearSearchHistory,
    initSearchHistory,
    setCartCount,
    incrementCartCount,
    decrementCartCount,
    setUnreadMessageCount,
    incrementUnreadMessageCount,
    decrementUnreadMessageCount,
    clearUnreadMessageCount
  }
})
