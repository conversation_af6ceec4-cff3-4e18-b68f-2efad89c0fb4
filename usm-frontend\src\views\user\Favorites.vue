<template>
  <div class="favorites-container">
    <div class="page-header">
      <h2>我的收藏</h2>
      <div class="header-actions">
        <el-button @click="handleClearAll" :disabled="selectedItems.length === 0">
          清空选中
        </el-button>
        <el-checkbox v-model="selectAll" @change="handleSelectAll">
          全选
        </el-checkbox>
      </div>
    </div>

    <div class="favorites-list" v-loading="loading">
      <div v-for="item in favorites" :key="item.productId" class="favorite-item">
        <div class="item-checkbox">
          <el-checkbox 
            v-model="selectedItems" 
            :label="item.productId"
            @change="handleItemSelect"
          />
        </div>

        <div class="product-image" @click="goToProduct(item.productId)">
          <el-image :src="item.images[0]" fit="cover">
            <template #error>
              <div class="image-slot">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div class="product-status" v-if="item.status === 'sold'">
            已售出
          </div>
        </div>

        <div class="product-info">
          <h4 class="product-title" @click="goToProduct(item.productId)">
            {{ item.title }}
          </h4>
          <p class="product-price">
            ¥{{ item.price }}
            <span v-if="item.originalPrice" class="original-price">
              ¥{{ item.originalPrice }}
            </span>
          </p>
          <div class="product-meta">
            <span class="product-category">{{ getCategoryLabel(item.category) }}</span>
            <span class="product-condition">{{ getConditionLabel(item.condition) }}</span>
            <span class="product-campus">{{ getCampusLabel(item.campus) }}</span>
          </div>
          <div class="seller-info">
            <el-avatar :src="item.sellerInfo?.avatar" :size="20">
              {{ item.sellerInfo?.nickname?.charAt(0) }}
            </el-avatar>
            <span class="seller-name">{{ item.sellerInfo?.nickname }}</span>
          </div>
        </div>

        <div class="item-actions">
          <el-button 
            type="primary" 
            size="small" 
            @click="handleBuy(item)"
            :disabled="item.status === 'sold'"
          >
            {{ item.status === 'sold' ? '已售出' : '立即购买' }}
          </el-button>
          <el-button size="small" @click="handleContact(item.sellerInfo)">
            联系卖家
          </el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="handleRemoveFavorite(item.productId)"
          >
            取消收藏
          </el-button>
        </div>

        <div class="favorite-time">
          收藏于 {{ formatTime(item.favoriteTime) }}
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <el-empty v-if="!loading && favorites.length === 0" description="暂无收藏商品">
      <el-button type="primary" @click="goToHome">去逛逛</el-button>
    </el-empty>

    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import { formatTime } from '@/utils'
import { 
  PRODUCT_CATEGORY_LABELS, 
  PRODUCT_CONDITION_LABELS, 
  CAMPUS_LABELS 
} from '@/constants'
import type { Product } from '@/types'

interface FavoriteItem extends Product {
  favoriteTime: string
}

const router = useRouter()

const loading = ref(false)
const favorites = ref<FavoriteItem[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const selectedItems = ref<number[]>([])

const selectAll = computed({
  get: () => selectedItems.value.length === favorites.value.length && favorites.value.length > 0,
  set: (value: boolean) => {
    if (value) {
      selectedItems.value = favorites.value.map(item => item.productId)
    } else {
      selectedItems.value = []
    }
  }
})

// 获取收藏列表
const fetchFavorites = async () => {
  loading.value = true
  try {
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    favorites.value = [
      {
        productId: 1,
        title: '高等数学教材（第七版）',
        description: '同济大学出版社，九成新，无笔记',
        category: 'textbook',
        price: 25,
        originalPrice: 45,
        condition: 'good',
        images: ['/src/assets/background.jpg'],
        sellerId: 1,
        sellerInfo: {
          userId: 1,
          username: 'student1',
          nickname: '小明',
          avatar: '',
          email: '',
          phone: '',
          sex: '0',
          status: '',
          roleKey: 'student'
        },
        campus: 'main',
        status: 'published',
        viewCount: 156,
        likeCount: 23,
        createTime: '2024-01-15T10:30:00',
        updateTime: '2024-01-15T10:30:00',
        favoriteTime: '2024-01-16T09:00:00'
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('获取收藏列表失败')
  } finally {
    loading.value = false
  }
}

// 工具函数
const getCategoryLabel = (category: string) => {
  return PRODUCT_CATEGORY_LABELS[category as keyof typeof PRODUCT_CATEGORY_LABELS] || category
}

const getConditionLabel = (condition: string) => {
  return PRODUCT_CONDITION_LABELS[condition as keyof typeof PRODUCT_CONDITION_LABELS] || condition
}

const getCampusLabel = (campus: string) => {
  return CAMPUS_LABELS[campus as keyof typeof CAMPUS_LABELS] || campus
}

// 导航方法
const goToProduct = (id: number) => {
  router.push({ name: 'ProductDetail', params: { id } })
}

const goToHome = () => {
  router.push({ name: 'Home' })
}

// 操作方法
const handleBuy = (item: FavoriteItem) => {
  router.push({ name: 'OrderCreate', query: { productId: item.productId } })
}

const handleContact = (seller: any) => {
  ElMessage.info('联系卖家功能开发中...')
}

const handleRemoveFavorite = async (productId: number) => {
  try {
    await ElMessageBox.confirm('确定要取消收藏这个商品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 这里应该调用API取消收藏
    ElMessage.success('已取消收藏')
    fetchFavorites()
  } catch {
    // 用户取消
  }
}

const handleSelectAll = () => {
  // selectAll 的 setter 会自动处理
}

const handleItemSelect = () => {
  // 选择项变化时的处理
}

const handleClearAll = async () => {
  if (selectedItems.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(`确定要取消收藏选中的 ${selectedItems.value.length} 个商品吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 这里应该调用API批量取消收藏
    ElMessage.success('已取消收藏')
    selectedItems.value = []
    fetchFavorites()
  } catch {
    // 用户取消
  }
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchFavorites()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchFavorites()
}

onMounted(() => {
  fetchFavorites()
})
</script>

<style lang="stylus" scoped>
.favorites-container
  width 100%
  padding 20px

.page-header
  display flex
  justify-content space-between
  align-items center
  margin-bottom 20px
  
  h2
    color #333
    
  .header-actions
    display flex
    align-items center
    gap 15px

.favorites-list
  .favorite-item
    display flex
    align-items center
    background white
    border-radius 8px
    box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
    margin-bottom 15px
    padding 20px
    gap 15px
    
    .item-checkbox
      flex-shrink 0
      
    .product-image
      width 100px
      height 100px
      position relative
      cursor pointer
      border-radius 4px
      overflow hidden
      flex-shrink 0
      
      .el-image
        width 100%
        height 100%
        
      .image-slot
        display flex
        align-items center
        justify-content center
        height 100%
        background #f5f7fa
        color #909399
        
      .product-status
        position absolute
        top 5px
        right 5px
        background rgba(0, 0, 0, 0.7)
        color white
        padding 2px 6px
        border-radius 3px
        font-size 10px
        
    .product-info
      flex 1
      min-width 0
      
      .product-title
        font-size 16px
        color #333
        margin-bottom 8px
        cursor pointer
        overflow hidden
        text-overflow ellipsis
        white-space nowrap
        
        &:hover
          color #409eff
          
      .product-price
        font-size 18px
        color #e74c3c
        font-weight bold
        margin-bottom 8px
        
        .original-price
          font-size 14px
          color #999
          text-decoration line-through
          margin-left 8px
          
      .product-meta
        display flex
        gap 15px
        margin-bottom 8px
        font-size 12px
        color #666
        
      .seller-info
        display flex
        align-items center
        gap 6px
        font-size 12px
        color #666
        
    .item-actions
      display flex
      flex-direction column
      gap 8px
      flex-shrink 0
      
    .favorite-time
      flex-shrink 0
      font-size 12px
      color #999
      writing-mode vertical-rl
      text-orientation mixed

.pagination-container
  display flex
  justify-content center
  margin-top 30px

@media (max-width: 768px)
  .favorites-container
    padding 10px
    
  .page-header
    flex-direction column
    gap 15px
    align-items stretch
    
  .favorite-item
    flex-direction column
    align-items stretch
    gap 15px
    
    .product-info
      .product-meta
        flex-wrap wrap
        
    .item-actions
      flex-direction row
      justify-content space-between
      
    .favorite-time
      writing-mode initial
      text-orientation initial
      text-align center
</style>
