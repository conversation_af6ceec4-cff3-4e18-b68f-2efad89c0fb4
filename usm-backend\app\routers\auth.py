from fastapi import API<PERSON>outer, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List

from app.schemas.common import ApiResponse
from app.schemas.auth import (
    LoginRequest, RegisterRequest, LoginResponse,
    UserInfo, RoleInfo
)
from app.services.auth_service import auth_service
from app.core.security import verify_token

router = APIRouter()
security = HTTPBearer()

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """获取当前用户"""
    token = credentials.credentials
    username = verify_token(token)
    if username is None:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return username



@router.post("/login", response_model=ApiResponse[LoginResponse])
async def login(login_data: LoginRequest):
    """用户登录"""
    try:
        result = await auth_service.login(login_data)
        return ApiResponse(
            code=200,
            msg="登录成功",
            data=LoginResponse(token=result["token"])
        )
    except HTTPException as e:
        return ApiResponse(
            code=e.status_code,
            msg=e.detail,
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"登录失败: {str(e)}",
            data=None
        )

@router.post("/register", response_model=ApiResponse[dict])
async def register(register_data: RegisterRequest):
    """用户注册"""
    try:
        result = await auth_service.register(register_data)
        return ApiResponse(
            code=200,
            msg="注册成功",
            data=result
        )
    except HTTPException as e:
        return ApiResponse(
            code=e.status_code,
            msg=e.detail,
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"注册失败: {str(e)}",
            data=None
        )

@router.post("/logout", response_model=ApiResponse[dict])
async def logout(current_user: str = Depends(get_current_user)):
    """用户登出"""
    # 在实际应用中，这里可以将token加入黑名单
    return ApiResponse(
        code=200,
        msg="登出成功",
        data={"message": "已成功登出"}
    )

@router.get("/user/getInfo", response_model=ApiResponse[UserInfo])
async def get_user_info(current_user: str = Depends(get_current_user)):
    """获取用户信息"""
    try:
        user_info = await auth_service.get_user_info(current_user)
        return ApiResponse(
            code=200,
            msg="success",
            data=user_info
        )
    except HTTPException as e:
        return ApiResponse(
            code=e.status_code,
            msg=e.detail,
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"获取用户信息失败: {str(e)}",
            data=None
        )

@router.get("/roleList", response_model=ApiResponse[List[RoleInfo]])
async def get_roles():
    """获取角色列表"""
    try:
        roles = await auth_service.get_roles()
        return ApiResponse(
            code=200,
            msg="success",
            data=roles
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"获取角色列表失败: {str(e)}",
            data=None
        )
