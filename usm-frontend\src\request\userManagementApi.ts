import request from "./request";
import type { User } from "@/types";

// API响应类型
type ApiResponse<T = any> = {
  code: number;
  msg: string;
  data: T;
}

// 分页响应类型
type PageResponse<T> = {
  records: T[];
  total: number;
  pageNum: number;
  pageSize: number;
  totalPages: number;
}

// 用户列表查询参数
export interface UserListParams {
  username?: string;
  role?: string;
  status?: string;
  campus?: string;
  email?: string;
  phone?: string;
  sortBy?: string;
  sortOrder?: string;
  pageNum?: number;
  pageSize?: number;
}

// 用户更新请求
export interface UserUpdateRequest {
  nickname?: string;
  email?: string;
  phone?: string;
  roleKey?: string;
  campus?: string;
  status?: string;
  sex?: string;
}

// 用户状态更新请求
export interface UserStatusUpdateRequest {
  status: string;
}

// 用户统计信息
export interface UserStatistics {
  totalUsers: number;
  activeUsers: number;
  disabledUsers: number;
  pendingUsers: number;
  roleStats: {
    student: number;
    admin: number;
    teacher: number;
  };
  campusStats: {
    [key: string]: number;
  };
}

// 批量操作请求
export interface BatchUserOperationRequest {
  userIds: number[];
  operation: string;
  value?: string;
}

// 批量操作结果
export interface BatchOperationResult {
  successCount: number;
  failedCount: number;
  failedUsers: Array<{
    userId: number;
    reason: string;
  }>;
}

/**
 * 获取用户列表
 */
export const getUsersListApi = (params?: UserListParams): Promise<ApiResponse<PageResponse<User>>> => {
  return request.get('/users/list', { params });
}

/**
 * 获取用户详情
 */
export const getUserDetailApi = (userId: number): Promise<ApiResponse<User>> => {
  return request.get(`/users/${userId}`);
}

/**
 * 更新用户信息
 */
export const updateUserApi = (userId: number, data: UserUpdateRequest): Promise<ApiResponse<User>> => {
  return request.put(`/users/${userId}`, data);
}

/**
 * 更新用户状态
 */
export const updateUserStatusApi = (userId: number, data: UserStatusUpdateRequest): Promise<ApiResponse<User>> => {
  return request.put(`/users/${userId}/status`, data);
}

/**
 * 删除用户
 */
export const deleteUserApi = (userId: number): Promise<ApiResponse<boolean>> => {
  return request.delete(`/users/${userId}`);
}

/**
 * 获取用户统计信息
 */
export const getUserStatisticsApi = (): Promise<ApiResponse<UserStatistics>> => {
  return request.get('/users/statistics/overview');
}

/**
 * 批量用户操作
 */
export const batchUserOperationApi = (data: BatchUserOperationRequest): Promise<ApiResponse<BatchOperationResult>> => {
  return request.post('/users/batch', data);
}
