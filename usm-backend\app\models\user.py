from tortoise.models import Model
from tortoise import fields

class User(Model):
    """用户模型"""

    user_id = fields.IntField(pk=True, description="用户ID")
    username = fields.Char<PERSON>ield(max_length=50, unique=True, index=True, description="用户名")
    password = fields.Char<PERSON>ield(max_length=255, description="密码")
    nickname = fields.CharField(max_length=50, description="昵称")
    avatar = fields.CharField(max_length=500, default="", description="头像URL")
    email = fields.CharField(max_length=100, unique=True, index=True, description="邮箱")
    phone = fields.Char<PERSON>ield(max_length=20, unique=True, index=True, description="手机号")
    sex = fields.IntField(description="性别：0-男，1-女")
    status = fields.CharField(max_length=20, default="active", description="状态：active-正常，disabled-禁用，deleted-删除")
    role_key = fields.Char<PERSON><PERSON>(max_length=50, default="student", description="角色标识")
    campus = fields.CharField(max_length=50, null=True, description="校区")
    student_id = fields.CharField(max_length=50, null=True, description="学号")
    real_name = fields.CharField(max_length=50, null=True, description="真实姓名")
    is_verified = fields.BooleanField(default=False, description="是否实名认证")
    last_login_time = fields.DatetimeField(null=True, description="最后登录时间")
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "users"

class Role(Model):
    """角色模型"""

    role_id = fields.IntField(pk=True, description="角色ID")
    role_name = fields.CharField(max_length=50, description="角色名称")
    role_key = fields.CharField(max_length=50, unique=True, description="角色标识")
    description = fields.CharField(max_length=200, null=True, description="角色描述")
    status = fields.BooleanField(default=True, description="状态：1-启用，0-禁用")
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "sys_roles"
