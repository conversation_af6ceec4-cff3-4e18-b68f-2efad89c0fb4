<template>
  <div class="publish-container">
    <div class="publish-form">
      <h2>发布商品</h2>
      
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.prevent="handleSubmit"
      >
        <el-form-item label="商品标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入商品标题"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="商品分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择商品分类">
            <el-option
              v-for="(label, value) in PRODUCT_CATEGORY_LABELS"
              :key="value"
              :label="label"
              :value="value"
            >
              <div class="category-option">
                <CategoryIcon :category="value" :size="16" />
                <span>{{ label }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="商品图片" prop="images">
          <el-upload
            v-model:file-list="fileList"
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :on-change="handleImageChange"
            :on-remove="handleImageRemove"
            :limit="9"
            accept="image/*"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">
                最多上传9张图片，每张不超过5MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        
        <el-form-item label="商品价格" prop="price">
          <el-input-number
            v-model="form.price"
            :min="0"
            :precision="2"
            placeholder="请输入价格"
            style="width: 200px"
          />
          <span style="margin-left: 10px">元</span>
        </el-form-item>
        
        <el-form-item label="原价" prop="originalPrice">
          <el-input-number
            v-model="form.originalPrice"
            :min="0"
            :precision="2"
            placeholder="可选，用于显示折扣"
            style="width: 200px"
          />
          <span style="margin-left: 10px">元</span>
        </el-form-item>
        
        <el-form-item label="新旧程度" prop="condition">
          <el-radio-group v-model="form.condition">
            <el-radio
              v-for="(label, value) in PRODUCT_CONDITION_LABELS"
              :key="value"
              :label="value"
            >
              {{ label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="所在校区" prop="campus">
          <el-select v-model="form.campus" placeholder="请选择校区">
            <el-option
              v-for="(label, value) in CAMPUS_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="具体位置" prop="location">
          <el-input
            v-model="form.location"
            placeholder="如：XX宿舍楼、XX教学楼等"
          />
        </el-form-item>
        
        <el-form-item label="商品描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="5"
            placeholder="请详细描述商品的状况、使用情况等"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="商品标签" prop="tags">
          <el-tag
            v-for="tag in form.tags"
            :key="tag"
            closable
            @close="removeTag(tag)"
            style="margin-right: 10px"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="inputVisible"
            ref="inputRef"
            v-model="inputValue"
            size="small"
            style="width: 100px"
            @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm"
          />
          <el-button v-else size="small" @click="showInput">
            + 添加标签
          </el-button>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            发布商品
          </el-button>
          <el-button @click="handleSaveDraft" :loading="loading">
            保存草稿
          </el-button>
          <el-button @click="handleCancel">
            取消
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { PRODUCT_CATEGORY_LABELS, PRODUCT_CONDITION_LABELS, CAMPUS_LABELS } from '@/constants'
import CategoryIcon from '@/components/common/CategoryIcon.vue'
import type { FormInstance, UploadFile } from 'element-plus'
import { createProductApi } from '@/request/productApi'

const router = useRouter()

const formRef = ref<FormInstance>()
const loading = ref(false)
const fileList = ref<UploadFile[]>([])
const inputVisible = ref(false)
const inputValue = ref('')
const inputRef = ref()

// 表单数据
const form = reactive({
  title: '',
  category: '',
  images: [] as string[],
  price: 0,
  originalPrice: undefined as number | undefined,
  condition: '',
  campus: '',
  location: '',
  description: '',
  tags: [] as string[]
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入商品标题', trigger: 'blur' },
    { min: 2, max: 50, message: '标题长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择商品分类', trigger: 'change' }
  ],
  images: [
    { required: true, message: '请上传商品图片', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入商品价格', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '价格必须大于0', trigger: 'blur' }
  ],
  condition: [
    { required: true, message: '请选择新旧程度', trigger: 'change' }
  ],
  campus: [
    { required: true, message: '请选择所在校区', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入商品描述', trigger: 'blur' },
    { min: 10, max: 500, message: '描述长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

// 图片处理
const handleImageChange = (file: UploadFile) => {
  if (file.raw) {
    // 这里应该上传图片到服务器
    // 现在只是模拟添加到数组
    const url = URL.createObjectURL(file.raw)
    form.images.push(url)
  }
}

const handleImageRemove = (file: UploadFile) => {
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    form.images.splice(index, 1)
  }
}

// 标签处理
const removeTag = (tag: string) => {
  form.tags.splice(form.tags.indexOf(tag), 1)
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !form.tags.includes(inputValue.value)) {
    form.tags.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

// 表单提交
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        // 准备提交数据
        const submitData = {
          title: form.title,
          description: form.description,
          category: form.category,
          price: form.price,
          originalPrice: form.originalPrice,
          condition: form.condition,
          campus: form.campus,
          location: form.location,
          tags: form.tags,
          images: form.images
        }

        // 调用创建商品API
        const response = await createProductApi(submitData)

        if (response.code === 200) {
          ElMessage.success('商品发布成功')
          router.push({ name: 'MyProducts' })
        } else {
          ElMessage.error(response.msg || '发布失败，请稍后重试')
        }
      } catch (error) {
        console.error('发布失败:', error)
        ElMessage.error('发布失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
  })
}

// 保存草稿
const handleSaveDraft = async () => {
  loading.value = true
  try {
    // 准备草稿数据（不需要完整验证）
    const draftData = {
      title: form.title || '未命名商品',
      description: form.description || '暂无描述',
      category: form.category || 'other',
      price: form.price || 0.01,
      originalPrice: form.originalPrice,
      condition: form.condition || 'good',
      campus: form.campus || 'main',
      location: form.location,
      tags: form.tags,
      images: form.images
    }

    // 调用创建商品API（状态为草稿）
    const response = await createProductApi(draftData)

    if (response.code === 200) {
      ElMessage.success('草稿保存成功')
      // 可以选择跳转到我的商品页面或继续编辑
    } else {
      ElMessage.error(response.msg || '保存失败，请稍后重试')
    }
  } catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 取消发布
const handleCancel = async () => {
  try {
    await ElMessageBox.confirm('确定要取消发布吗？未保存的内容将丢失', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '继续编辑',
      type: 'warning'
    })
    router.back()
  } catch {
    // 用户取消
  }
}
</script>

<style lang="stylus" scoped>
.publish-container
  width 100%
  padding 20px

.publish-form
  background white
  padding 30px
  border-radius 8px
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
  
  h2
    margin-bottom 30px
    color #333
    text-align center

.el-upload__tip
  color #999
  font-size 12px
  margin-top 5px

.category-option
  display flex
  align-items center
  gap 8px

@media (max-width: 768px)
  .publish-container
    padding 10px
    
  .publish-form
    padding 20px
</style>
