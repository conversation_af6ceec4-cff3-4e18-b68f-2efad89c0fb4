from typing import List, Optional, Dict, Any
from fastapi import HTTPException

from app.crud.product import product_crud, category_crud
from app.models.user import User
from app.schemas.product import (
    ProductCreateRequest, ProductUpdateRequest, ProductStatusUpdateRequest,
    ProductListRequest, ProductInfo, SellerInfo, ProductCategoryInfo
)


class ProductService:
    """商品服务"""

    async def _get_user_id_by_username(self, username: str) -> int:
        """根据用户名获取用户ID"""
        user = await User.filter(username=username).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        return user.user_id

    async def create_product(self, username: str, product_data: ProductCreateRequest) -> ProductInfo:
        """创建商品"""
        try:
            user_id = await self._get_user_id_by_username(username)
            product = await product_crud.create_product(user_id, product_data)
            return await self._convert_to_product_info(product)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"创建商品失败: {str(e)}")
    
    async def get_product_detail(self, product_id: int, username: Optional[str] = None) -> ProductInfo:
        """获取商品详情"""
        user_id = None
        if username:
            user_id = await self._get_user_id_by_username(username)

        product = await product_crud.get_product_by_id(product_id, user_id)
        if not product:
            raise HTTPException(status_code=404, detail="商品不存在")

        return await self._convert_to_product_info(product)
    
    async def get_products_list(self, params: ProductListRequest, username: Optional[str] = None) -> Dict[str, Any]:
        """获取商品列表"""
        try:
            user_id = None
            is_admin = False
            if username:
                # 获取用户信息
                user = await User.filter(username=username).first()
                if user:
                    user_id = user.user_id
                    is_admin = user.role_key == 'admin'

            result = await product_crud.get_products_list(params, user_id, is_admin)
            
            # 转换商品信息
            products_info = []
            for product in result["products"]:
                product_info = await self._convert_to_product_info(product)
                products_info.append(product_info)
            
            return {
                "products": products_info,
                "total": result["total"],
                "pageNum": result["pageNum"],
                "pageSize": result["pageSize"],
                "totalPages": result["totalPages"]
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取商品列表失败: {str(e)}")
    
    async def get_my_products(self, username: str, status: Optional[str] = None,
                            page_num: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取我的商品"""
        try:
            user_id = await self._get_user_id_by_username(username)
            result = await product_crud.get_my_products(user_id, status, page_num, page_size)
            
            # 转换商品信息
            products_info = []
            for product in result["products"]:
                product_info = await self._convert_to_product_info(product)
                products_info.append(product_info)
            
            return {
                "products": products_info,
                "total": result["total"],
                "pageNum": result["pageNum"],
                "pageSize": result["pageSize"],
                "totalPages": result["totalPages"]
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取我的商品失败: {str(e)}")
    
    async def update_product(self, product_id: int, username: str,
                           product_data: ProductUpdateRequest) -> ProductInfo:
        """更新商品"""
        user_id = await self._get_user_id_by_username(username)
        product = await product_crud.update_product(product_id, user_id, product_data)
        if not product:
            raise HTTPException(status_code=404, detail="商品不存在或无权限修改")

        return await self._convert_to_product_info(product)
    
    async def update_product_status(self, product_id: int, username: str,
                                  status_data: ProductStatusUpdateRequest) -> ProductInfo:
        """更新商品状态"""
        # 获取用户信息
        user = await User.filter(username=username).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 如果是管理员，可以修改任何商品状态
        if user.role_key == 'admin':
            product = await product_crud.admin_update_product_status(product_id, status_data.status)
        else:
            # 普通用户只能修改自己的商品状态
            product = await product_crud.update_product_status(product_id, user.user_id, status_data.status)

        if not product:
            raise HTTPException(status_code=404, detail="商品不存在或无权限修改")

        return await self._convert_to_product_info(product)
    
    async def delete_product(self, product_id: int, username: str) -> bool:
        """删除商品"""
        # 获取用户信息
        user = await User.filter(username=username).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 如果是管理员，可以删除任何商品
        if user.role_key == 'admin':
            success = await product_crud.admin_delete_product(product_id)
        else:
            # 普通用户只能删除自己的商品
            success = await product_crud.delete_product(product_id, user.user_id)

        if not success:
            raise HTTPException(status_code=404, detail="商品不存在或无权限删除")

        return True

    async def restore_product(self, product_id: int, current_user: str) -> bool:
        """恢复已删除的商品（仅管理员）"""
        # 检查管理员权限
        if current_user != "admin":
            raise HTTPException(status_code=403, detail="权限不足，只有管理员可以恢复商品")

        success = await product_crud.restore_product(product_id)
        if not success:
            raise HTTPException(status_code=404, detail="商品不存在或未被删除")

        return True

    async def get_categories(self) -> List[ProductCategoryInfo]:
        """获取商品分类列表"""
        try:
            categories = await category_crud.get_categories()
            return [
                ProductCategoryInfo(
                    categoryId=cat.category_id,
                    categoryKey=cat.category_key,
                    categoryName=cat.category_name,
                    icon=cat.icon,
                    sortOrder=cat.sort_order,
                    status=cat.status
                )
                for cat in categories
            ]
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取分类列表失败: {str(e)}")
    
    async def _convert_to_product_info(self, product) -> ProductInfo:
        """转换为商品信息对象"""
        # 获取卖家信息
        seller_info = None
        if product.seller_id:
            # 直接查询卖家信息，避免QuerySet问题
            seller = await User.filter(user_id=product.seller_id).first()
            if seller:
                seller_info = SellerInfo(
                    userId=seller.user_id,
                    username=seller.username,
                    nickname=seller.nickname or seller.username,
                    avatar=seller.avatar or "",
                    campus=getattr(seller, 'campus', None)
                )
        
        # 获取图片列表
        images = []
        from app.models.product import ProductImage
        product_images = await ProductImage.filter(product_id=product.product_id).order_by('sort_order')
        images = [img.image_url for img in product_images]
        
        # 获取收藏状态
        is_liked = getattr(product, 'is_liked', False)
        
        return ProductInfo(
            productId=product.product_id,
            title=product.title,
            description=product.description,
            category=product.category,
            price=product.price,
            originalPrice=product.original_price,
            condition=product.condition,
            campus=product.campus,
            location=product.location,
            sellerId=product.seller_id,
            sellerInfo=seller_info,
            status=product.status,
            viewCount=product.view_count,
            likeCount=product.like_count,
            isLiked=is_liked,
            isDeleted=product.is_deleted,
            images=images,
            tags=product.tags or [],
            createTime=product.create_time,
            updateTime=product.update_time
        )


# 创建服务实例
product_service = ProductService()
