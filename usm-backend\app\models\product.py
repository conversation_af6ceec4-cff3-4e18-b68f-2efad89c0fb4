from tortoise.models import Model
from tortoise import fields

class ProductCategory(Model):
    """商品分类模型"""
    
    category_id = fields.IntField(pk=True, description="分类ID")
    category_key = fields.CharField(max_length=50, unique=True, index=True, description="分类标识")
    category_name = fields.CharField(max_length=100, description="分类名称")
    icon = fields.CharField(max_length=50, default="", description="分类图标")
    sort_order = fields.IntField(default=0, description="排序")
    status = fields.BooleanField(default=True, description="状态：True-启用，False-禁用")
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "product_categories"


class Product(Model):
    """商品模型"""
    
    product_id = fields.IntField(pk=True, description="商品ID")
    title = fields.Char<PERSON>ield(max_length=200, description="商品标题")
    description = fields.TextField(description="商品描述")
    category = fields.CharField(max_length=50, description="商品分类")
    price = fields.DecimalField(max_digits=10, decimal_places=2, description="价格")
    original_price = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="原价")
    condition = fields.CharField(max_length=20, description="商品成色：new-全新，like_new-几乎全新，good-良好，fair-一般，poor-较差", source_field="condition_type")
    campus = fields.CharField(max_length=50, description="校区")
    location = fields.CharField(max_length=200, null=True, description="具体位置")
    
    # 关联用户
    seller = fields.ForeignKeyField("models.User", related_name="products", description="卖家")
    
    # 状态管理
    status = fields.CharField(max_length=20, default="draft", description="状态：draft-草稿，pending_review-待审核，published-已发布，sold-已售出，removed-已下架")
    
    # 统计信息
    view_count = fields.IntField(default=0, description="浏览次数")
    like_count = fields.IntField(default=0, description="点赞次数")
    
    # 标签
    tags = fields.JSONField(default=list, description="商品标签")

    # 逻辑删除标记
    is_deleted = fields.BooleanField(default=False, description="是否删除：True-已删除，False-未删除")

    # 时间字段
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "products"


class ProductImage(Model):
    """商品图片模型"""
    
    image_id = fields.IntField(pk=True, description="图片ID")
    product = fields.ForeignKeyField("models.Product", related_name="images", description="关联商品")
    image_url = fields.CharField(max_length=500, description="图片URL")
    sort_order = fields.IntField(default=0, description="排序")
    is_cover = fields.BooleanField(default=False, description="是否为封面图")
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")

    class Meta:
        table = "product_images"


class ProductFavorite(Model):
    """商品收藏模型"""
    
    favorite_id = fields.IntField(pk=True, description="收藏ID")
    user = fields.ForeignKeyField("models.User", related_name="favorites", description="用户")
    product = fields.ForeignKeyField("models.Product", related_name="favorites", description="商品")
    create_time = fields.DatetimeField(auto_now_add=True, description="收藏时间")

    class Meta:
        table = "product_favorites"
        unique_together = (("user", "product"),)  # 用户和商品的组合唯一
