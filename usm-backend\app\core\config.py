from pydantic_settings import BaseSettings
from typing import List

class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "University Second-hand Marketplace"
    VERSION: str = "1.0.0"
    DEBUG: bool = True

    # 服务器配置
    HOST: str = "localhost"
    PORT: int = 18080

    # 数据库配置
    DB_HOST: str = "localhost"
    DB_PORT: int = 3308
    DB_USER: str = "root"
    DB_PASSWORD: str = "root"
    DB_NAME: str = "db_usm"

    @property
    def DATABASE_URL(self) -> str:
        return f"mysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

    # JWT配置
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30 * 24 * 60  # 30天

    # CORS配置
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:5173",
        "http://localhost:3000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:3000"
    ]

    # TortoiseORM 配置
    def get_tortoise_config(self):
        return {
            "connections": {
                "default": self.DATABASE_URL
            },
            "apps": {
                "models": {
                    "models": ["app.models.user", "app.models.product", "app.models.order"],
                    "default_connection": "default",
                }
            },
        }
    
    # 文件上传配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 5 * 1024 * 1024  # 5MB
    ALLOWED_IMAGE_TYPES: List[str] = ["image/jpeg", "image/png", "image/gif", "image/webp"]
    
    class Config:
        env_file = ".env"

settings = Settings()
