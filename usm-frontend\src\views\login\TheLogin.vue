<template>
  <div class="login">
    <!-- 左侧宣传区域 -->
    <div class="login-banner">
      <div class="banner-content">
        <div class="logo-section">
          <div class="logo-icon">
            <el-icon size="48"><ShoppingBag /></el-icon>
          </div>
          <h1 class="platform-title">校园二手交易平台</h1>
          <p class="platform-slogan">让闲置物品重新焕发价值</p>
        </div>

        <div class="features">
          <div class="feature-item">
            <el-icon class="feature-icon"><Checked /></el-icon>
            <span>安全可靠的校园交易</span>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><User /></el-icon>
            <span>实名认证保障</span>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><ChatDotRound /></el-icon>
            <span>便捷的沟通交流</span>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Star /></el-icon>
            <span>优质的服务体验</span>
          </div>
        </div>

        <div class="stats">
          <div class="stat-item">
            <div class="stat-number">1000+</div>
            <div class="stat-label">注册用户</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">5000+</div>
            <div class="stat-label">成功交易</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">98%</div>
            <div class="stat-label">好评率</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧登录区域 -->
    <div class="login-form-section">
      <div class="login-container">
        <div class="form-header">
          <h2 class="form-title">欢迎回来</h2>
          <p class="form-subtitle">登录您的账号，开始校园交易之旅</p>
        </div>

        <!-- 测试账号提示 -->
        <el-alert
          title="测试账号"
          type="info"
          :closable="false"
          class="test-alert"
        >
          <div class="test-accounts">
            <div class="account-item">
              <el-tag type="primary" size="small">学生</el-tag>
              <span>student / 123456</span>
            </div>
            <div class="account-item">
              <el-tag type="warning" size="small">管理员</el-tag>
              <span>admin / 123456</span>
            </div>
          </div>
        </el-alert>

        <el-form class="login-form" @submit.prevent="loginClick">
          <el-form-item>
            <el-input
              v-model="username"
              placeholder="请输入用户名"
              size="large"
              class="form-input"
            >
              <template #prefix>
                <el-icon><User /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item>
            <el-input
              v-model="password"
              type="password"
              placeholder="请输入密码"
              size="large"
              class="form-input"
              show-password
            >
              <template #prefix>
                <el-icon><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <div class="form-options">
            <el-checkbox v-model="rememberMe">记住我</el-checkbox>
            <el-link type="primary" class="forgot-link">忘记密码？</el-link>
          </div>

          <el-button
            class="login-btn"
            type="primary"
            size="large"
            :loading="loading"
            @click="loginClick"
            block
          >
            <span v-if="!loading">立即登录</span>
            <span v-else>登录中...</span>
          </el-button>

          <div class="register-section">
            <span class="register-text">还没有账号？</span>
            <el-link type="primary" @click="registerClick" class="register-link">
              立即注册
            </el-link>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from "element-plus";
import { useUserStore } from '@/stores';
import { loginApi } from '@/request/loginApi'
import {
  ShoppingBag,
  User,
  Lock,
  Checked,
  ChatDotRound,
  Star
} from '@element-plus/icons-vue';

const username = ref('')
const password = ref('')
const loading = ref(false)
const rememberMe = ref(false)
const router = useRouter()
const userStore = useUserStore()

const loginClick = async() => {
  if (!username.value || !password.value) {
    ElMessage.error('请输入用户名和密码')
    return
  }

  loading.value = true

  try {
    const res = await loginApi({
      username: username.value,
      password: password.value,
      code: '',
      uuid: ''
    })

    if (res.code === 200 && res.data?.token) {
      await userStore.login(res.data.token)
      ElMessage.success('登录成功')
      // 获取并加载用户信息
      await userStore.fetchUserInfo()
      // 根据角色跳转
      if (userStore.userInfo?.roleKey === 'admin') {
        router.push('/admin')
      } else {
        router.push('/')
      }
    } else {
      ElMessage.error(res.msg || '用户名或密码错误')
    }
  } catch (error:any) {
    ElMessage.error(error?.response?.data?.msg || '登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

const registerClick = async () => {
  await router.push('/register');
}
</script>

<style lang="stylus" scoped>
.login {
  display: flex
  min-height: 100vh
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)
}

// 左侧宣传区域
.login-banner {
  flex: 1
  display: flex
  align-items: center
  justify-content: center
  padding: 60px 40px
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)
  position: relative
  overflow: hidden

  &::before {
    content: ''
    position: absolute
    top: 0
    left: 0
    right: 0
    bottom: 0
    background: url("../../assets/background.jpg") center/cover
    opacity: 0.1
    z-index: 0
  }
}

.banner-content {
  position: relative
  z-index: 1
  text-align: center
  color: white
  max-width: 500px
}

.logo-section {
  margin-bottom: 60px

  .logo-icon {
    margin-bottom: 20px

    .el-icon {
      background: rgba(255, 255, 255, 0.2)
      border-radius: 50%
      padding: 20px
      backdrop-filter: blur(10px)
    }
  }

  .platform-title {
    font-size: 36px
    font-weight: 700
    margin: 0 0 16px
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3)
  }

  .platform-slogan {
    font-size: 18px
    opacity: 0.9
    margin: 0
    font-weight: 300
  }
}

.features {
  margin-bottom: 60px

  .feature-item {
    display: flex
    align-items: center
    justify-content: center
    margin-bottom: 20px
    font-size: 16px
    opacity: 0.9

    .feature-icon {
      margin-right: 12px
      font-size: 20px
    }
  }
}

.stats {
  display: flex
  justify-content: space-around

  .stat-item {
    text-align: center

    .stat-number {
      font-size: 32px
      font-weight: 700
      margin-bottom: 8px
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3)
    }

    .stat-label {
      font-size: 14px
      opacity: 0.8
    }
  }
}

// 右侧登录区域
.login-form-section {
  flex: 1
  display: flex
  align-items: center
  justify-content: center
  padding: 40px
  background: #f8fafc
  min-width: 500px
}

.login-container {
  width: 100%
  max-width: 420px
  background: white
  border-radius: 16px
  padding: 40px
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1)
  transition: all 0.3s ease

  &:hover {
    transform: translateY(-2px)
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15)
  }
}

.form-header {
  text-align: center
  margin-bottom: 32px

  .form-title {
    font-size: 28px
    font-weight: 700
    color: #1a202c
    margin: 0 0 8px
  }

  .form-subtitle {
    font-size: 16px
    color: #718096
    margin: 0
    line-height: 1.5
  }
}

.test-alert {
  margin-bottom: 24px
  border-radius: 8px

  .test-accounts {
    .account-item {
      display: flex
      align-items: center
      gap: 12px
      margin-bottom: 8px
      font-size: 14px

      &:last-child {
        margin-bottom: 0
      }
    }
  }
}

.login-form {
  .form-input {
    margin-bottom: 20px

    :deep(.el-input__wrapper) {
      border-radius: 8px
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1)
      transition: all 0.3s ease

      &:hover {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15)
      }

      &.is-focus {
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1)
      }
    }
  }
}

.form-options {
  display: flex
  justify-content: space-between
  align-items: center
  margin-bottom: 24px

  .forgot-link {
    font-size: 14px
    text-decoration: none

    &:hover {
      text-decoration: underline
    }
  }
}

.login-btn {
  height: 48px
  font-size: 16px
  font-weight: 600
  border-radius: 8px
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)
  border: none
  transition: all 0.3s ease

  &:hover {
    transform: translateY(-1px)
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4)
  }

  &:active {
    transform: translateY(0)
  }
}

.register-section {
  text-align: center
  margin-top: 24px

  .register-text {
    color: #718096
    font-size: 14px
    margin-right: 8px
  }

  .register-link {
    font-size: 14px
    font-weight: 600
    text-decoration: none

    &:hover {
      text-decoration: underline
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .login {
    flex-direction: column
  }

  .login-banner {
    flex: none
    min-height: 40vh
    padding: 40px 20px

    .banner-content {
      max-width: 400px
    }

    .logo-section {
      margin-bottom: 40px

      .platform-title {
        font-size: 28px
      }

      .platform-slogan {
        font-size: 16px
      }
    }

    .features {
      margin-bottom: 40px

      .feature-item {
        font-size: 14px
        margin-bottom: 16px
      }
    }

    .stats {
      .stat-item {
        .stat-number {
          font-size: 24px
        }

        .stat-label {
          font-size: 12px
        }
      }
    }
  }

  .login-form-section {
    min-width: auto
    padding: 20px
  }

  .login-container {
    padding: 30px 20px
  }
}

@media (max-width: 768px) {
  .login-banner {
    min-height: 30vh
    padding: 30px 15px

    .logo-section {
      margin-bottom: 30px

      .platform-title {
        font-size: 24px
      }
    }

    .features {
      display: none
    }

    .stats {
      .stat-item {
        .stat-number {
          font-size: 20px
        }
      }
    }
  }

  .form-header {
    .form-title {
      font-size: 24px
    }

    .form-subtitle {
      font-size: 14px
    }
  }
}

// 测试账号提示样式
:deep(.el-alert) {
  border-radius: 6px

  .el-alert__content {
    p {
      margin: 3px 0
      font-size: 13px
      line-height: 1.4

      strong {
        color: #409eff
        font-weight: 600
      }
    }
  }
}
</style>