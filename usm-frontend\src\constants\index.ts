// 应用常量定义

// API 基础配置
export const API_BASE_URL = 'http://localhost:18080/api'

// 商品分类
export const PRODUCT_CATEGORIES = {
  TEXTBOOK: 'textbook',
  DIGITAL: 'digital',
  CLOTHING: 'clothing',
  DAILY: 'daily',
  SPORTS: 'sports',
  BEAUTY: 'beauty',
  FOOD: 'food',
  OTHER: 'other'
} as const

export const PRODUCT_CATEGORY_LABELS = {
  [PRODUCT_CATEGORIES.TEXTBOOK]: '教材书籍',
  [PRODUCT_CATEGORIES.DIGITAL]: '数码电子',
  [PRODUCT_CATEGORIES.CLOTHING]: '服装配饰',
  [PRODUCT_CATEGORIES.DAILY]: '生活用品',
  [PRODUCT_CATEGORIES.SPORTS]: '运动器材',
  [PRODUCT_CATEGORIES.BEAUTY]: '美妆护肤',
  [PRODUCT_CATEGORIES.FOOD]: '食品零食',
  [PRODUCT_CATEGORIES.OTHER]: '其他'
} as const

// 商品分类图标
export const PRODUCT_CATEGORY_ICONS = {
  [PRODUCT_CATEGORIES.TEXTBOOK]: 'Document',
  [PRODUCT_CATEGORIES.DIGITAL]: 'Monitor',
  [PRODUCT_CATEGORIES.CLOTHING]: 'User',
  [PRODUCT_CATEGORIES.DAILY]: 'Coffee',
  [PRODUCT_CATEGORIES.SPORTS]: 'Star',
  [PRODUCT_CATEGORIES.BEAUTY]: 'Brush',
  [PRODUCT_CATEGORIES.FOOD]: 'Apple',
  [PRODUCT_CATEGORIES.OTHER]: 'MoreFilled'
} as const

// 商品状态
export const PRODUCT_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  SOLD: 'sold',
  REMOVED: 'removed'
} as const

export const PRODUCT_STATUS_LABELS = {
  [PRODUCT_STATUS.DRAFT]: '草稿',
  [PRODUCT_STATUS.PUBLISHED]: '在售',
  [PRODUCT_STATUS.SOLD]: '已售出',
  [PRODUCT_STATUS.REMOVED]: '已下架'
} as const

// 商品新旧程度
export const PRODUCT_CONDITION = {
  NEW: 'new',
  LIKE_NEW: 'like_new',
  GOOD: 'good',
  FAIR: 'fair',
  POOR: 'poor'
} as const

export const PRODUCT_CONDITION_LABELS = {
  [PRODUCT_CONDITION.NEW]: '全新',
  [PRODUCT_CONDITION.LIKE_NEW]: '几乎全新',
  [PRODUCT_CONDITION.GOOD]: '良好',
  [PRODUCT_CONDITION.FAIR]: '一般',
  [PRODUCT_CONDITION.POOR]: '较差'
} as const

// 订单状态
export const ORDER_STATUS = {
  PENDING_PAYMENT: 'pending_payment',
  PENDING_PICKUP: 'pending_pickup',
  PENDING_CONFIRM: 'pending_confirm',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
} as const

export const ORDER_STATUS_LABELS = {
  [ORDER_STATUS.PENDING_PAYMENT]: '待付款',
  [ORDER_STATUS.PENDING_PICKUP]: '待取货',
  [ORDER_STATUS.PENDING_CONFIRM]: '待确认',
  [ORDER_STATUS.COMPLETED]: '已完成',
  [ORDER_STATUS.CANCELLED]: '已取消'
} as const

// 用户角色
export const USER_ROLES = {
  STUDENT: 'student',
  ADMIN: 'admin',
  SUPER_ADMIN: 'super_admin'
} as const

export const USER_ROLE_LABELS = {
  [USER_ROLES.STUDENT]: '学生',
  [USER_ROLES.ADMIN]: '管理员',
  [USER_ROLES.SUPER_ADMIN]: '超级管理员'
} as const

// 校区
export const CAMPUS = {
  MAIN: 'main',
  NORTH: 'north',
  SOUTH: 'south',
  EAST: 'east',
  WEST: 'west'
} as const

export const CAMPUS_LABELS = {
  [CAMPUS.MAIN]: '主校区',
  [CAMPUS.NORTH]: '北校区',
  [CAMPUS.SOUTH]: '南校区',
  [CAMPUS.EAST]: '东校区',
  [CAMPUS.WEST]: '西校区'
} as const

// 文件上传限制
export const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  MAX_COUNT: 9 // 最多上传9张图片
} as const

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZES: [10, 20, 50, 100]
} as const

// 本地存储键名
export const STORAGE_KEYS = {
  TOKEN: 'token',
  USER_INFO: 'userInfo',
  SEARCH_HISTORY: 'searchHistory',
  CART: 'cart'
} as const

// 消息类型
export const MESSAGE_TYPE = {
  SYSTEM: 'system',
  ORDER: 'order',
  COMMENT: 'comment',
  LIKE: 'like'
} as const

// 举报类型
export const REPORT_TYPE = {
  FAKE_PRODUCT: 'fake_product',
  INAPPROPRIATE_CONTENT: 'inappropriate_content',
  FRAUD: 'fraud',
  SPAM: 'spam',
  OTHER: 'other'
} as const

export const REPORT_TYPE_LABELS = {
  [REPORT_TYPE.FAKE_PRODUCT]: '虚假商品',
  [REPORT_TYPE.INAPPROPRIATE_CONTENT]: '不当内容',
  [REPORT_TYPE.FRAUD]: '欺诈行为',
  [REPORT_TYPE.SPAM]: '垃圾信息',
  [REPORT_TYPE.OTHER]: '其他'
} as const
