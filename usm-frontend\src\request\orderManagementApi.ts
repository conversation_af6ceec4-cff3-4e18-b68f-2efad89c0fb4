import request from "./request";

// API响应类型
type ApiResponse<T = any> = {
  code: number;
  msg: string;
  data: T;
}

// 分页响应类型
type PageResponse<T> = {
  records: T[];
  total: number;
  pageNum: number;
  pageSize: number;
  totalPages: number;
}

// 用户信息类型
export interface UserInfo {
  userId: number;
  username: string;
  nickname: string;
  phone: string;
  email: string;
}

// 商品信息类型
export interface ProductInfo {
  productId: number;
  title: string;
  category: string;
  price: number;
  images: string[];
}

// 订单信息类型
export interface OrderInfo {
  orderId: number;
  orderNo: string;
  productId: number;
  productInfo?: ProductInfo;
  buyerId: number;
  buyerInfo?: UserInfo;
  sellerId: number;
  sellerInfo?: UserInfo;
  price: number;
  status: string;
  pickupLocation?: string;
  pickupTime?: string;
  remark?: string;
  createTime: string;
  updateTime: string;
}

// 订单列表查询参数
export interface OrderListParams {
  orderNo?: string;
  buyerUsername?: string;
  sellerUsername?: string;
  productTitle?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: string;
  sortOrder?: string;
  pageNum?: number;
  pageSize?: number;
}

// 订单状态更新请求
export interface OrderStatusUpdateRequest {
  status: string;
  remark?: string;
}

// 订单统计信息
export interface OrderStatistics {
  totalOrders: number;
  pendingPayment: number;
  pendingPickup: number;
  pendingConfirm: number;
  completed: number;
  cancelled: number;
  todayOrders: number;
  totalAmount: number;
  statusStats: Record<string, number>;
  dailyStats: Array<{
    date: string;
    count: number;
  }>;
}

// 批量操作请求
export interface BatchOrderOperationRequest {
  orderIds: number[];
  operation: string;
  value?: string;
}

// 批量操作结果
export interface BatchOperationResult {
  successCount: number;
  failedCount: number;
  failedOrders: Array<{
    orderId: number;
    reason: string;
  }>;
}

/**
 * 获取订单列表
 */
export const getOrdersListApi = (params?: OrderListParams): Promise<ApiResponse<PageResponse<OrderInfo>>> => {
  return request.get('/orders/list', { params });
}

/**
 * 获取订单详情
 */
export const getOrderDetailApi = (orderId: number): Promise<ApiResponse<OrderInfo>> => {
  return request.get(`/orders/${orderId}`);
}

/**
 * 更新订单状态
 */
export const updateOrderStatusApi = (orderId: number, data: OrderStatusUpdateRequest): Promise<ApiResponse<OrderInfo>> => {
  return request.put(`/orders/${orderId}/status`, data);
}

/**
 * 删除订单
 */
export const deleteOrderApi = (orderId: number): Promise<ApiResponse<boolean>> => {
  return request.delete(`/orders/${orderId}`);
}

/**
 * 获取订单统计信息
 */
export const getOrderStatisticsApi = (): Promise<ApiResponse<OrderStatistics>> => {
  return request.get('/orders/statistics/overview');
}

/**
 * 批量订单操作
 */
export const batchOrderOperationApi = (data: BatchOrderOperationRequest): Promise<ApiResponse<BatchOperationResult>> => {
  return request.post('/orders/batch', data);
}
