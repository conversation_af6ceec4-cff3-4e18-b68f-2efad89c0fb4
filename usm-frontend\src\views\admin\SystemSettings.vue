<template>
  <div class="personal-settings">
    <div class="page-header">
      <h2>个人设置</h2>
      <div class="header-actions">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回后台
        </el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">
          <el-icon><Check /></el-icon>
          保存设置
        </el-button>
      </div>
    </div>

    <div class="settings-content">
      <el-row :gutter="20">
        <el-col :span="16">
          <!-- 基本信息 -->
          <el-card class="setting-card">
            <template #header>
              <span>基本信息</span>
            </template>

            <el-form :model="userInfo" label-width="120px" :rules="userRules" ref="userFormRef">
              <el-form-item label="头像">
                <div class="avatar-section">
                  <el-avatar :src="userInfo.avatar" :size="80">
                    {{ userInfo.nickname?.charAt(0) }}
                  </el-avatar>
                  <div class="avatar-actions">
                    <el-upload
                      class="avatar-uploader"
                      action="#"
                      :show-file-list="false"
                      :auto-upload="false"
                      :on-change="handleAvatarChange"
                      accept="image/*"
                    >
                      <el-button size="small">
                        <el-icon><Upload /></el-icon>
                        更换头像
                      </el-button>
                    </el-upload>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="用户名" prop="username">
                <el-input v-model="userInfo.username" disabled />
                <div class="form-tip">用户名不可修改</div>
              </el-form-item>
              <el-form-item label="昵称" prop="nickname">
                <el-input v-model="userInfo.nickname" placeholder="请输入昵称" />
              </el-form-item>
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="userInfo.email" placeholder="请输入邮箱" />
              </el-form-item>
              <el-form-item label="手机号" prop="phone">
                <el-input v-model="userInfo.phone" placeholder="请输入手机号" />
              </el-form-item>
              <el-form-item label="性别">
                <el-radio-group v-model="userInfo.sex">
                  <el-radio value="1">男</el-radio>
                  <el-radio value="2">女</el-radio>
                  <el-radio value="0">保密</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="个人简介">
                <el-input
                  v-model="userInfo.bio"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入个人简介"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 安全设置 -->
          <el-card class="setting-card">
            <template #header>
              <span>安全设置</span>
            </template>

            <el-form :model="securitySettings" label-width="120px" :rules="securityRules" ref="securityFormRef">
              <el-form-item label="当前密码" prop="currentPassword">
                <el-input
                  v-model="securitySettings.currentPassword"
                  type="password"
                  placeholder="请输入当前密码"
                  show-password
                />
              </el-form-item>
              <el-form-item label="新密码" prop="newPassword">
                <el-input
                  v-model="securitySettings.newPassword"
                  type="password"
                  placeholder="请输入新密码"
                  show-password
                />
              </el-form-item>
              <el-form-item label="确认密码" prop="confirmPassword">
                <el-input
                  v-model="securitySettings.confirmPassword"
                  type="password"
                  placeholder="请再次输入新密码"
                  show-password
                />
              </el-form-item>
            </el-form>
          </el-card>
          <!-- 通知设置 -->
          <el-card class="setting-card">
            <template #header>
              <span>通知设置</span>
            </template>

            <el-form :model="notificationSettings" label-width="120px">
              <el-form-item label="邮件通知">
                <el-switch
                  v-model="notificationSettings.emailNotification"
                  active-text="开启"
                  inactive-text="关闭"
                />
                <div class="form-tip">接收重要消息的邮件通知</div>
              </el-form-item>
              <el-form-item label="短信通知">
                <el-switch
                  v-model="notificationSettings.smsNotification"
                  active-text="开启"
                  inactive-text="关闭"
                />
                <div class="form-tip">接收交易相关的短信通知</div>
              </el-form-item>
              <el-form-item label="系统消息">
                <el-switch
                  v-model="notificationSettings.systemMessage"
                  active-text="开启"
                  inactive-text="关闭"
                />
                <div class="form-tip">接收系统公告和更新消息</div>
              </el-form-item>
              <el-form-item label="交易提醒">
                <el-switch
                  v-model="notificationSettings.tradeReminder"
                  active-text="开启"
                  inactive-text="关闭"
                />
                <div class="form-tip">接收商品询问、订单状态等提醒</div>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 隐私设置 -->
          <el-card class="setting-card">
            <template #header>
              <span>隐私设置</span>
            </template>

            <el-form :model="privacySettings" label-width="120px">
              <el-form-item label="个人资料可见性">
                <el-radio-group v-model="privacySettings.profileVisibility">
                  <el-radio value="public">公开</el-radio>
                  <el-radio value="friends">仅好友</el-radio>
                  <el-radio value="private">私密</el-radio>
                </el-radio-group>
                <div class="form-tip">控制其他用户查看您个人资料的权限</div>
              </el-form-item>
              <el-form-item label="联系方式可见性">
                <el-radio-group v-model="privacySettings.contactVisibility">
                  <el-radio value="public">公开</el-radio>
                  <el-radio value="buyers">仅买家</el-radio>
                  <el-radio value="private">私密</el-radio>
                </el-radio-group>
                <div class="form-tip">控制联系方式的显示范围</div>
              </el-form-item>
              <el-form-item label="在线状态">
                <el-switch
                  v-model="privacySettings.showOnlineStatus"
                  active-text="显示"
                  inactive-text="隐藏"
                />
                <div class="form-tip">是否显示在线状态给其他用户</div>
              </el-form-item>
              <el-form-item label="交易记录">
                <el-switch
                  v-model="privacySettings.showTradeHistory"
                  active-text="公开"
                  inactive-text="私密"
                />
                <div class="form-tip">是否公开显示交易记录和评价</div>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <el-col :span="8">
          <!-- 账户信息 -->
          <el-card class="info-card">
            <template #header>
              <span>账户信息</span>
            </template>

            <div class="account-info">
              <div class="info-item">
                <span class="label">注册时间：</span>
                <span class="value">2024-01-10</span>
              </div>
              <div class="info-item">
                <span class="label">最后登录：</span>
                <span class="value">2024-01-16 10:30</span>
              </div>
              <div class="info-item">
                <span class="label">登录次数：</span>
                <span class="value">156次</span>
              </div>
              <div class="info-item">
                <span class="label">账户状态：</span>
                <el-tag type="success" size="small">正常</el-tag>
              </div>
              <div class="info-item">
                <span class="label">用户等级：</span>
                <el-tag type="primary" size="small">活跃用户</el-tag>
              </div>
            </div>
          </el-card>

          <!-- 交易统计 -->
          <el-card class="info-card">
            <template #header>
              <span>交易统计</span>
            </template>

            <div class="trade-stats">
              <div class="stat-item">
                <div class="stat-number">12</div>
                <div class="stat-label">发布商品</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">8</div>
                <div class="stat-label">成功交易</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">4.8</div>
                <div class="stat-label">平均评分</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">¥1,280</div>
                <div class="stat-label">交易金额</div>
              </div>
            </div>
          </el-card>

          <!-- 快捷操作 -->
          <el-card class="info-card">
            <template #header>
              <span>快捷操作</span>
            </template>

            <div class="quick-actions">
              <el-button type="primary" size="small" @click="goToMyProducts" block>
                <el-icon><Goods /></el-icon>
                我的商品
              </el-button>
              <el-button type="success" size="small" @click="goToMyOrders" block>
                <el-icon><Document /></el-icon>
                我的订单
              </el-button>
              <el-button type="info" size="small" @click="goToMessages" block>
                <el-icon><ChatDotRound /></el-icon>
                消息中心
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Check,
  Upload,
  Goods,
  Document,
  ChatDotRound,
  Download
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()
const saveLoading = ref(false)
const userFormRef = ref()
const securityFormRef = ref()

// 用户信息
const userInfo = reactive({
  username: 'admin',
  nickname: '管理员',
  email: '<EMAIL>',
  phone: '13800138000',
  sex: '1',
  avatar: '',
  bio: '校园二手交易平台管理员，致力于为同学们提供安全便捷的交易环境。'
})

// 安全设置
const securitySettings = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 通知设置
const notificationSettings = reactive({
  emailNotification: true,
  smsNotification: false,
  systemMessage: true,
  tradeReminder: true
})

// 隐私设置
const privacySettings = reactive({
  profileVisibility: 'public',
  contactVisibility: 'buyers',
  showOnlineStatus: true,
  showTradeHistory: true
})

// 表单验证规则
const userRules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

const securityRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== securitySettings.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 事件处理
const goBack = () => {
  router.push({ name: 'AdminDashboard' })
}

const handleSave = async () => {
  // 验证表单
  const userValid = await userFormRef.value?.validate().catch(() => false)

  if (!userValid) {
    ElMessage.error('请检查基本信息填写')
    return
  }

  // 如果填写了密码相关信息，验证安全设置
  if (securitySettings.currentPassword || securitySettings.newPassword || securitySettings.confirmPassword) {
    const securityValid = await securityFormRef.value?.validate().catch(() => false)
    if (!securityValid) {
      ElMessage.error('请检查安全设置填写')
      return
    }
  }

  saveLoading.value = true
  try {
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 更新用户store中的信息
    userStore.updateUserInfo({
      ...userStore.userInfo,
      nickname: userInfo.nickname,
      email: userInfo.email,
      phone: userInfo.phone,
      sex: userInfo.sex,
      avatar: userInfo.avatar
    })

    ElMessage.success('个人设置保存成功')

    // 清空密码字段
    securitySettings.currentPassword = ''
    securitySettings.newPassword = ''
    securitySettings.confirmPassword = ''
  } catch (error) {
    ElMessage.error('保存失败，请重试')
  } finally {
    saveLoading.value = false
  }
}

const handleAvatarChange = (file: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    userInfo.avatar = e.target?.result as string
  }
  reader.readAsDataURL(file.raw)
}

// 快捷操作
const goToMyProducts = () => {
  router.push({ name: 'MyProducts' })
}

const goToMyOrders = () => {
  router.push({ name: 'OrderList' })
}

const goToMessages = () => {
  router.push({ name: 'MessageList' })
}

const handleExportData = async () => {
  try {
    await ElMessageBox.confirm('确定要导出个人数据吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    ElMessage.success('数据导出请求已提交，请稍后查看邮箱')
  } catch {
    // 用户取消
  }
}
onMounted(() => {
  // 从用户store加载当前用户信息
  if (userStore.userInfo) {
    Object.assign(userInfo, {
      username: userStore.userInfo.username,
      nickname: userStore.userInfo.nickname,
      email: userStore.userInfo.email,
      phone: userStore.userInfo.phone,
      sex: userStore.userInfo.sex,
      avatar: userStore.userInfo.avatar
    })
  }
})
</script>

<style lang="stylus" scoped>
.personal-settings
  width 100%
  padding 20px

.page-header
  display flex
  justify-content space-between
  align-items center
  margin-bottom 20px

  h2
    margin 0
    color #333

  .header-actions
    display flex
    gap 10px

.settings-content
  .setting-card
    margin-bottom 20px

    .form-tip
      font-size 12px
      color #999
      margin-top 5px

.avatar-section
  display flex
  align-items center
  gap 15px

  .avatar-actions
    display flex
    flex-direction column
    gap 10px

.avatar-uploader
  .el-upload
    border 1px dashed #d9d9d9
    border-radius 6px
    cursor pointer
    position relative
    overflow hidden

    &:hover
      border-color #409eff
.info-card
  margin-bottom 20px

.account-info
  .info-item
    display flex
    justify-content space-between
    align-items center
    padding 12px 0
    border-bottom 1px solid #f0f0f0

    &:last-child
      border-bottom none

    .label
      color #666
      font-size 14px

    .value
      font-weight 500
      color #333

.trade-stats
  display grid
  grid-template-columns 1fr 1fr
  gap 20px

  .stat-item
    text-align center
    padding 15px
    background #f8f9fa
    border-radius 8px

    .stat-number
      font-size 24px
      font-weight bold
      color #409eff
      margin-bottom 5px

    .stat-label
      font-size 12px
      color #666

.quick-actions
  .el-button
    margin-bottom 10px

    &:last-child
      margin-bottom 0

@media (max-width: 768px)
  .personal-settings
    padding 10px

  .page-header
    flex-direction column
    gap 15px
    align-items stretch

  .header-actions
    justify-content center

  .avatar-section
    flex-direction column
    align-items flex-start

  .trade-stats
    grid-template-columns 1fr

  .settings-content
    .el-col
      margin-bottom 20px
</style>
