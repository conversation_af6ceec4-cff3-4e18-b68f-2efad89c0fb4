from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

router = APIRouter()

class TestModel(BaseModel):
    name: str = Field(..., min_length=2, max_length=10)
    age: int = Field(..., ge=0, le=120)

@router.get("/test/404")
async def test_404():
    """测试404错误"""
    raise HTTPException(status_code=404, detail="测试资源未找到")

@router.get("/test/401")
async def test_401():
    """测试401错误"""
    raise HTTPException(status_code=401, detail="未授权访问")

@router.get("/test/403")
async def test_403():
    """测试403错误"""
    raise HTTPException(status_code=403, detail="禁止访问")

@router.get("/test/500")
async def test_500():
    """测试500错误"""
    raise Exception("这是一个测试异常")

@router.post("/test/422")
async def test_422(data: TestModel):
    """测试422验证错误"""
    return {"message": "验证成功", "data": data}
