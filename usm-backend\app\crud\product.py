from typing import List, Optional, Dict, Any
from tortoise.queryset import QuerySet
from tortoise.expressions import Q
from datetime import datetime
from decimal import Decimal

from app.models.product import Product, ProductImage, ProductFavorite, ProductCategory
from app.models.user import User
from app.schemas.product import ProductCreateRequest, ProductUpdateRequest, ProductListRequest


class ProductCRUD:
    """商品CRUD操作"""
    
    async def create_product(self, user_id: int, product_data: ProductCreateRequest) -> Product:
        """创建商品"""
        # 创建商品基础信息
        product = await Product.create(
            title=product_data.title,
            description=product_data.description,
            category=product_data.category,
            price=product_data.price,
            original_price=product_data.originalPrice,
            condition=product_data.condition,
            campus=product_data.campus,
            location=product_data.location,
            seller_id=user_id,
            tags=product_data.tags,
            status="draft"  # 默认为草稿状态
        )
        
        # 创建商品图片
        if product_data.images:
            for index, image_url in enumerate(product_data.images):
                await ProductImage.create(
                    product_id=product.product_id,
                    image_url=image_url,
                    sort_order=index,
                    is_cover=(index == 0)  # 第一张图片作为封面
                )
        
        return product
    
    async def get_product_by_id(self, product_id: int, user_id: Optional[int] = None) -> Optional[Product]:
        """根据ID获取商品详情"""
        product = await Product.filter(product_id=product_id, is_deleted=0).prefetch_related(
            'seller', 'images'
        ).first()
        
        if not product:
            return None
        
        # 增加浏览次数
        await Product.filter(product_id=product_id).update(view_count=product.view_count + 1)
        
        # 检查是否收藏（如果提供了用户ID）
        if user_id:
            is_liked = await ProductFavorite.filter(
                user_id=user_id, 
                product_id=product_id
            ).exists()
            # 动态添加属性
            setattr(product, 'is_liked', is_liked)
        else:
            setattr(product, 'is_liked', False)
        
        return product
    
    async def get_products_list(self, params: ProductListRequest, user_id: Optional[int] = None, is_admin: bool = False) -> Dict[str, Any]:
        """获取商品列表"""
        # 基础查询
        query = Product.all()

        # 删除状态筛选
        if params.isDeleted == 'true':
            # 查看已删除的商品（仅管理员）
            if is_admin:
                query = query.filter(is_deleted=True)
            else:
                # 普通用户不能查看已删除的商品，返回空结果
                query = query.filter(is_deleted=True, seller_id=-1)  # 不可能的条件
        elif params.isDeleted == 'false':
            # 明确查看未删除的商品
            query = query.filter(is_deleted=False)
        else:
            # 默认情况：普通用户只看未删除的，管理员看所有未删除的
            if is_admin:
                query = query.filter(is_deleted=False)
            else:
                query = query.filter(is_deleted=False)

        # 状态筛选（在删除状态筛选基础上）
        if params.status:
            query = query.filter(status=params.status)
        elif not is_admin:
            # 普通用户只能看到已发布的商品
            query = query.filter(status="published")

        # 分类筛选
        if params.category:
            query = query.filter(category=params.category)

        # 校区筛选
        if params.campus:
            query = query.filter(campus=params.campus)

        # 成色筛选
        if params.condition:
            query = query.filter(condition=params.condition)

        # 价格筛选
        if params.minPrice is not None:
            query = query.filter(price__gte=params.minPrice)
        if params.maxPrice is not None:
            query = query.filter(price__lte=params.maxPrice)

        # 关键词搜索
        if params.keyword:
            query = query.filter(
                Q(title__icontains=params.keyword) |
                Q(description__icontains=params.keyword)
            )

        # 卖家用户名搜索
        if params.sellerUsername:
            query = query.filter(seller__username__icontains=params.sellerUsername)

        # 日期范围筛选
        if params.startDate:
            query = query.filter(create_time__gte=f"{params.startDate} 00:00:00")
        if params.endDate:
            query = query.filter(create_time__lte=f"{params.endDate} 23:59:59")
        
        # 排序
        sort_field = params.sortBy
        if params.sortOrder == "desc":
            sort_field = f"-{sort_field}"
        query = query.order_by(sort_field)
        
        # 分页
        offset = (params.pageNum - 1) * params.pageSize
        total = await query.count()
        
        products = await query.offset(offset).limit(params.pageSize).prefetch_related(
            'seller', 'images'
        )
        
        # 如果提供了用户ID，检查收藏状态
        if user_id and products:
            product_ids = [p.product_id for p in products]
            liked_products = await ProductFavorite.filter(
                user_id=user_id,
                product_id__in=product_ids
            ).values_list('product_id', flat=True)
            
            for product in products:
                setattr(product, 'is_liked', product.product_id in liked_products)
        else:
            for product in products:
                setattr(product, 'is_liked', False)
        
        return {
            "products": products,
            "total": total,
            "pageNum": params.pageNum,
            "pageSize": params.pageSize,
            "totalPages": (total + params.pageSize - 1) // params.pageSize
        }
    
    async def get_my_products(self, user_id: int, status: Optional[str] = None,
                            page_num: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取我的商品列表"""
        query = Product.filter(seller_id=user_id, is_deleted=0)
        
        if status:
            query = query.filter(status=status)
        
        query = query.order_by("-create_time")
        
        # 分页
        offset = (page_num - 1) * page_size
        total = await query.count()
        
        products = await query.offset(offset).limit(page_size).prefetch_related('images')
        
        return {
            "products": products,
            "total": total,
            "pageNum": page_num,
            "pageSize": page_size,
            "totalPages": (total + page_size - 1) // page_size
        }
    
    async def update_product(self, product_id: int, user_id: int, 
                           product_data: ProductUpdateRequest) -> Optional[Product]:
        """更新商品信息"""
        # 检查商品是否存在且属于当前用户（排除已删除的商品）
        product = await Product.filter(product_id=product_id, seller_id=user_id, is_deleted=0).first()
        if not product:
            return None
        
        # 更新商品信息
        update_data = {}
        for field, value in product_data.dict(exclude_unset=True).items():
            if field == "images":
                continue  # 图片单独处理
            # 转换字段名
            db_field = self._convert_field_name(field)
            update_data[db_field] = value
        
        if update_data:
            await Product.filter(product_id=product_id).update(**update_data)
        
        # 更新图片
        if product_data.images is not None:
            # 删除原有图片
            await ProductImage.filter(product_id=product_id).delete()
            # 添加新图片
            for index, image_url in enumerate(product_data.images):
                await ProductImage.create(
                    product_id=product_id,
                    image_url=image_url,
                    sort_order=index,
                    is_cover=(index == 0)
                )
        
        # 返回更新后的商品
        return await self.get_product_by_id(product_id, user_id)
    
    async def update_product_status(self, product_id: int, user_id: int, status: str) -> Optional[Product]:
        """更新商品状态"""
        product = await Product.filter(product_id=product_id, seller_id=user_id, is_deleted=0).first()
        if not product:
            return None

        await Product.filter(product_id=product_id).update(status=status)
        return await Product.filter(product_id=product_id).first()

    async def admin_update_product_status(self, product_id: int, status: str) -> Optional[Product]:
        """管理员更新商品状态（无需验证所有者）"""
        product = await Product.filter(product_id=product_id, is_deleted=0).first()
        if not product:
            return None

        await Product.filter(product_id=product_id).update(status=status)
        return await Product.filter(product_id=product_id).first()
    
    async def delete_product(self, product_id: int, user_id: int) -> bool:
        """逻辑删除商品"""
        product = await Product.filter(product_id=product_id, seller_id=user_id, is_deleted=0).first()
        if not product:
            return False

        try:
            # 使用逻辑删除，只更新is_deleted字段
            await Product.filter(product_id=product_id).update(
                is_deleted=1,
                status='removed'  # 同时设置状态为已下架
            )

            # 逻辑删除相关数据（保留历史记录）
            from tortoise import connections
            db = connections.get("default")

            # 1. 逻辑删除评论
            await db.execute_query("UPDATE comments SET is_deleted = 1 WHERE product_id = %s", [product_id])

            # 2. 取消未完成的订单
            await db.execute_query(
                "UPDATE orders SET status = 'cancelled', remark = '商品已被删除，订单自动取消' WHERE product_id = %s AND status IN ('pending_payment', 'pending_pickup', 'pending_confirm')",
                [product_id]
            )

            # 3. 保留商品图片和收藏记录（用户可能需要查看历史）
            # 商品图片和收藏记录保留，通过商品的is_deleted字段来控制显示

            return True

        except Exception as e:
            # 如果删除过程中出现错误，记录详细日志
            print(f"逻辑删除商品 {product_id} 时出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    async def admin_delete_product(self, product_id: int) -> bool:
        """管理员逻辑删除商品（无需验证所有者）"""
        product = await Product.filter(product_id=product_id, is_deleted=0).first()
        if not product:
            return False

        try:
            # 使用逻辑删除，只更新is_deleted字段
            await Product.filter(product_id=product_id).update(
                is_deleted=1,
                status='removed'  # 同时设置状态为已下架
            )

            # 逻辑删除相关数据（保留历史记录）
            from tortoise import connections
            db = connections.get("default")

            # 1. 逻辑删除评论
            await db.execute_query("UPDATE comments SET is_deleted = 1 WHERE product_id = %s", [product_id])

            # 2. 取消未完成的订单
            await db.execute_query(
                "UPDATE orders SET status = 'cancelled', remark = '商品已被管理员删除，订单自动取消' WHERE product_id = %s AND status IN ('pending_payment', 'pending_pickup', 'pending_confirm')",
                [product_id]
            )

            # 3. 保留商品图片和收藏记录（用户可能需要查看历史）
            # 商品图片和收藏记录保留，通过商品的is_deleted字段来控制显示

            return True

        except Exception as e:
            # 如果删除过程中出现错误，记录详细日志
            print(f"管理员逻辑删除商品 {product_id} 时出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def _convert_field_name(self, field_name: str) -> str:
        """转换字段名（驼峰转下划线）"""
        field_mapping = {
            "originalPrice": "original_price"
        }
        return field_mapping.get(field_name, field_name)

    async def restore_product(self, product_id: int) -> bool:
        """恢复已删除的商品（仅管理员）"""
        product = await Product.filter(product_id=product_id, is_deleted=1).first()
        if not product:
            return False

        try:
            # 恢复商品
            await Product.filter(product_id=product_id).update(
                is_deleted=0,
                status='draft'  # 恢复为草稿状态，需要重新审核
            )

            # 恢复评论
            from tortoise import connections
            db = connections.get("default")
            await db.execute_query("UPDATE comments SET is_deleted = 0 WHERE product_id = %s", [product_id])

            return True

        except Exception as e:
            print(f"恢复商品 {product_id} 时出现错误: {str(e)}")
            return False


class ProductCategoryCRUD:
    """商品分类CRUD操作"""
    
    async def get_categories(self) -> List[ProductCategory]:
        """获取所有分类"""
        return await ProductCategory.filter(status=True).order_by("sort_order")


# 创建实例
product_crud = ProductCRUD()
category_crud = ProductCategoryCRUD()
