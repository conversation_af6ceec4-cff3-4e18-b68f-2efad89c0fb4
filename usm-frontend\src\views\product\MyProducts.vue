<template>
  <div class="my-products-container">
    <div class="page-header">
      <h2>我的商品</h2>
      <el-button type="primary" @click="goToPublish">
        <el-icon><Plus /></el-icon>
        发布商品
      </el-button>
    </div>

    <div class="filter-tabs">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="全部" name="all" />
        <el-tab-pane label="在售" name="published" />
        <el-tab-pane label="已售出" name="sold" />
        <el-tab-pane label="已下架" name="removed" />
        <el-tab-pane label="草稿" name="draft" />
      </el-tabs>
    </div>

    <div class="products-list" v-loading="loading">
      <div v-for="product in products" :key="product.productId" class="product-item">
        <div class="product-image" @click="goToProduct(product.productId)">
          <el-image :src="product.images[0]" fit="cover">
            <template #error>
              <div class="image-slot">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div class="product-status-overlay" v-if="product.status !== 'published'">
            {{ getStatusLabel(product.status) }}
          </div>
        </div>

        <div class="product-info">
          <h4 class="product-title" @click="goToProduct(product.productId)">
            {{ product.title }}
          </h4>
          <p class="product-price">¥{{ product.price }}</p>
          <div class="product-meta">
            <span class="product-category">{{ getCategoryLabel(product.category) }}</span>
            <span class="product-campus">{{ getCampusLabel(product.campus) }}</span>
          </div>
          <div class="product-stats">
            <span class="stat-item">
              <el-icon><View /></el-icon>
              {{ product.viewCount }}
            </span>
            <span class="stat-item">
              <el-icon><Star /></el-icon>
              {{ product.likeCount }}
            </span>
            <span class="product-time">{{ formatTime(product.createTime) }}</span>
          </div>
        </div>

        <div class="product-actions">
          <el-dropdown @command="handleAction">
            <el-button type="text">
              操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item 
                  :command="`edit_${product.productId}`"
                  v-if="product.status !== 'sold'"
                >
                  编辑
                </el-dropdown-item>
                <el-dropdown-item 
                  :command="`republish_${product.productId}`"
                  v-if="product.status === 'removed' || product.status === 'draft'"
                >
                  重新发布
                </el-dropdown-item>
                <el-dropdown-item 
                  :command="`remove_${product.productId}`"
                  v-if="product.status === 'published'"
                >
                  下架
                </el-dropdown-item>
                <el-dropdown-item 
                  :command="`sold_${product.productId}`"
                  v-if="product.status === 'published'"
                >
                  标记为已售出
                </el-dropdown-item>
                <el-dropdown-item 
                  :command="`delete_${product.productId}`"
                  divided
                  v-if="product.status !== 'sold'"
                >
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <el-empty v-if="!loading && products.length === 0" description="暂无商品">
      <el-button type="primary" @click="goToPublish">发布第一个商品</el-button>
    </el-empty>

    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 48]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Picture, View, Star, ArrowDown } from '@element-plus/icons-vue'
import { formatTime } from '@/utils'
import { useUserStore } from '@/stores/user'
import {
  PRODUCT_STATUS_LABELS,
  PRODUCT_CATEGORY_LABELS,
  CAMPUS_LABELS
} from '@/constants'
import type { Product } from '@/types'
import { getMyProductsApi, updateProductStatusApi, deleteProductApi } from '@/request/productApi'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const products = ref<Product[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(12)
const activeTab = ref('all')

// 获取我的商品列表
const fetchMyProducts = async () => {
  loading.value = true
  try {
    // 调用真实的API
    const response = await getMyProductsApi({
      status: activeTab.value === 'all' ? undefined : activeTab.value,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    })

    if (response.code === 200) {
      products.value = response.data.records
      total.value = response.data.total
    } else {
      ElMessage.error(response.msg || '获取商品列表失败')
      products.value = []
      total.value = 0
    }


  } catch (error) {
    ElMessage.error('获取商品列表失败')
  } finally {
    loading.value = false
  }
}

// 工具函数
const getStatusLabel = (status: string) => {
  return PRODUCT_STATUS_LABELS[status as keyof typeof PRODUCT_STATUS_LABELS] || status
}

const getCategoryLabel = (category: string) => {
  return PRODUCT_CATEGORY_LABELS[category as keyof typeof PRODUCT_CATEGORY_LABELS] || category
}

const getCampusLabel = (campus: string) => {
  return CAMPUS_LABELS[campus as keyof typeof CAMPUS_LABELS] || campus
}

// 导航方法
const goToPublish = () => {
  router.push({ name: 'ProductPublish' })
}

const goToProduct = (id: number) => {
  router.push({ name: 'ProductDetail', params: { id } })
}

// 操作处理
const handleAction = async (command: string) => {
  const [action, productId] = command.split('_')
  const id = Number(productId)
  
  switch (action) {
    case 'edit':
      router.push({ name: 'ProductEdit', params: { id } })
      break
      
    case 'republish':
      try {
        await ElMessageBox.confirm('确定要重新发布这个商品吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })
        ElMessage.success('重新发布成功')
        fetchMyProducts()
      } catch {
        // 用户取消
      }
      break
      
    case 'remove':
      try {
        await ElMessageBox.confirm('确定要下架这个商品吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        ElMessage.success('商品已下架')
        fetchMyProducts()
      } catch {
        // 用户取消
      }
      break
      
    case 'sold':
      try {
        await ElMessageBox.confirm('确定要标记为已售出吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })
        ElMessage.success('已标记为售出')
        fetchMyProducts()
      } catch {
        // 用户取消
      }
      break
      
    case 'delete':
      try {
        await ElMessageBox.confirm('确定要删除这个商品吗？删除后无法恢复', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'danger'
        })
        ElMessage.success('商品已删除')
        fetchMyProducts()
      } catch {
        // 用户取消
      }
      break
  }
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  currentPage.value = 1
  fetchMyProducts()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchMyProducts()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchMyProducts()
}

onMounted(() => {
  fetchMyProducts()
})
</script>

<style lang="stylus" scoped>
.my-products-container
  width 100%
  padding 20px

.page-header
  display flex
  justify-content space-between
  align-items center
  margin-bottom 20px
  
  h2
    color #333

.filter-tabs
  background white
  border-radius 8px
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
  margin-bottom 20px

.products-list
  display grid
  grid-template-columns repeat(auto-fill, minmax(280px, 1fr))
  gap 20px
  
  .product-item
    background white
    border-radius 8px
    overflow hidden
    box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
    transition all 0.3s ease
    
    &:hover
      transform translateY(-2px)
      box-shadow 0 4px 16px rgba(0, 0, 0, 0.15)
      
    .product-image
      height 200px
      position relative
      cursor pointer
      overflow hidden
      
      .el-image
        width 100%
        height 100%
        
      .image-slot
        display flex
        align-items center
        justify-content center
        height 100%
        background #f5f7fa
        color #909399
        
      .product-status-overlay
        position absolute
        top 10px
        right 10px
        background rgba(0, 0, 0, 0.7)
        color white
        padding 4px 8px
        border-radius 4px
        font-size 12px
        
    .product-info
      padding 15px
      
      .product-title
        font-size 16px
        color #333
        margin-bottom 8px
        cursor pointer
        overflow hidden
        text-overflow ellipsis
        white-space nowrap
        
        &:hover
          color #409eff
          
      .product-price
        font-size 18px
        color #e74c3c
        font-weight bold
        margin-bottom 8px
        
      .product-meta
        display flex
        justify-content space-between
        margin-bottom 10px
        font-size 12px
        color #666
        
      .product-stats
        display flex
        justify-content space-between
        align-items center
        font-size 12px
        color #999
        
        .stat-item
          display flex
          align-items center
          gap 4px
          
        .product-time
          font-size 12px
          
    .product-actions
      padding 10px 15px
      border-top 1px solid #f0f0f0
      text-align right

.pagination-container
  display flex
  justify-content center
  margin-top 30px

@media (max-width: 768px)
  .my-products-container
    padding 10px
    
  .page-header
    flex-direction column
    gap 15px
    align-items stretch
    
  .products-list
    grid-template-columns repeat(2, 1fr)
    gap 10px
</style>
