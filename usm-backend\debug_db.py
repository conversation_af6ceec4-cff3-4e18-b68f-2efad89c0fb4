#!/usr/bin/env python3
"""
调试数据库连接和用户数据
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.user import User
from app.core.security import verify_password
from sqlalchemy import text

def debug_database():
    """调试数据库"""
    db = SessionLocal()
    try:
        print("🔍 开始调试数据库...")
        
        # 1. 测试数据库连接
        result = db.execute(text("SELECT 1")).fetchone()
        print(f"✅ 数据库连接成功: {result}")
        
        # 2. 查询所有用户
        users = db.query(User).all()
        print(f"\n📊 数据库中的用户数量: {len(users)}")
        
        for user in users:
            print(f"  - 用户名: {user.username}")
            print(f"    昵称: {user.nickname}")
            print(f"    邮箱: {user.email}")
            print(f"    角色: {user.role_key}")
            print(f"    状态: {user.status}")
            print(f"    密码哈希: {user.password[:50]}...")
            print()
        
        # 3. 测试admin用户
        admin_user = db.query(User).filter(User.username == "admin").first()
        if admin_user:
            print(f"✅ 找到admin用户:")
            print(f"  - ID: {admin_user.user_id}")
            print(f"  - 用户名: {admin_user.username}")
            print(f"  - 昵称: {admin_user.nickname}")
            print(f"  - 状态: {admin_user.status}")
            print(f"  - 完整密码哈希: {admin_user.password}")
            
            # 测试密码验证
            test_password = "123456"
            is_valid = verify_password(test_password, admin_user.password)
            print(f"  - 密码验证结果: {is_valid}")
            
            if not is_valid:
                print("❌ 密码验证失败！")
                # 尝试生成正确的密码哈希
                from app.core.security import get_password_hash
                correct_hash = get_password_hash(test_password)
                print(f"  - 正确的密码哈希应该是: {correct_hash}")
        else:
            print("❌ 未找到admin用户")
        
        # 4. 测试student用户
        student_user = db.query(User).filter(User.username == "student").first()
        if student_user:
            print(f"\n✅ 找到student用户:")
            print(f"  - ID: {student_user.user_id}")
            print(f"  - 用户名: {student_user.username}")
            print(f"  - 昵称: {student_user.nickname}")
            print(f"  - 状态: {student_user.status}")
            
            # 测试密码验证
            test_password = "123456"
            is_valid = verify_password(test_password, student_user.password)
            print(f"  - 密码验证结果: {is_valid}")
        else:
            print("❌ 未找到student用户")
            
    except Exception as e:
        print(f"❌ 数据库调试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    debug_database()
