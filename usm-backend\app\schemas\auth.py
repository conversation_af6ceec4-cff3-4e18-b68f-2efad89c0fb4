from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime

class LoginRequest(BaseModel):
    """登录请求"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    password: str = Field(..., min_length=6, max_length=100, description="密码")

class RegisterRequest(BaseModel):
    """注册请求"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    nickname: str = Field(..., min_length=2, max_length=50, description="昵称")
    password: str = Field(..., min_length=6, max_length=100, description="密码")
    phone: str = Field(..., pattern=r"^1[3-9]\d{9}$", description="手机号")
    email: EmailStr = Field(..., description="邮箱")
    sex: str = Field(..., pattern=r"^[01]$", description="性别：0-男，1-女")
    roleKey: str = Field(default="student", description="角色")
    campus: str = Field(..., description="校区")

class LoginResponse(BaseModel):
    """登录响应"""
    token: str = Field(..., description="访问令牌")

class UserInfo(BaseModel):
    """用户信息"""
    userId: int
    username: str
    nickname: str
    avatar: str = ""
    email: str
    phone: str
    sex: str
    status: str = "active"
    roleKey: str = "student"
    campus: Optional[str] = None
    studentId: Optional[str] = None
    realName: Optional[str] = None
    isVerified: bool = False
    createTime: datetime
    updateTime: datetime



class RoleInfo(BaseModel):
    """角色信息"""
    roleId: int
    roleName: str
    roleKey: str
