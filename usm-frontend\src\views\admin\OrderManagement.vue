<template>
  <div class="order-management">
    <div class="page-header">
      <h2>订单管理</h2>
      <div class="header-actions">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回后台
        </el-button>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalOrders }}</div>
              <div class="stat-label">总订单数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.pendingPayment }}</div>
              <div class="stat-label">待付款</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.pendingPickup }}</div>
              <div class="stat-label">待取货</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.todayOrders }}</div>
              <div class="stat-label">今日订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">¥{{ statistics.totalAmount }}</div>
              <div class="stat-label">总交易额</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderNo"
            placeholder="请输入订单号"
            clearable
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="买家">
          <el-input
            v-model="searchForm.buyerName"
            placeholder="请输入买家昵称"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="卖家">
          <el-input
            v-model="searchForm.sellerName"
            placeholder="请输入卖家昵称"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="商品标题">
          <el-input
            v-model="searchForm.productTitle"
            placeholder="请输入商品标题"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 180px">
            <el-option
              v-for="(label, value) in ORDER_STATUS_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>订单列表</span>
          
        </div>
      </template>

      <el-table
        :data="orders"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="orderId" label="订单ID" width="100" />
        <el-table-column prop="orderNo" label="订单号" width="180" />
        <el-table-column label="商品信息" min-width="250">
          <template #default="{ row }">
            <div class="product-info" v-if="row.productInfo">
              <div class="product-details">
                <div class="product-title">{{ row.productInfo.title }}</div>
                <div class="product-category">{{ row.productInfo.category }}</div>
                <div class="product-price">¥{{ row.price }}</div>
              </div>
            </div>
            <div v-else class="no-data">商品信息缺失</div>
          </template>
        </el-table-column>
        <el-table-column label="买家" width="150">
          <template #default="{ row }">
            <div v-if="row.buyerInfo">
              <div class="user-name">{{ row.buyerInfo.username }}</div>
              <div class="user-nickname">{{ row.buyerInfo.nickname }}</div>
            </div>
            <div v-else class="no-data">买家信息缺失</div>
          </template>
        </el-table-column>
        <el-table-column label="卖家" width="150">
          <template #default="{ row }">
            <div v-if="row.sellerInfo">
              <div class="user-name">{{ row.sellerInfo.username }}</div>
              <div class="user-nickname">{{ row.sellerInfo.nickname }}</div>
            </div>
            <div v-else class="no-data">卖家信息缺失</div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="订单状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentMethod" label="支付方式" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.paymentMethod">{{ getPaymentLabel(row.paymentMethod) }}</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="下单时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看详情
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleEditStatus(row)"
            >
              修改状态
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="订单详情"
      width="800px"
    >
      <div v-if="currentOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ currentOrder.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusTagType(currentOrder.status)">
              {{ getStatusLabel(currentOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="商品标题">{{ currentOrder.productTitle }}</el-descriptions-item>
          <el-descriptions-item label="订单金额">¥{{ currentOrder.totalAmount }}</el-descriptions-item>
          <el-descriptions-item label="买家">{{ currentOrder.buyerName }}</el-descriptions-item>
          <el-descriptions-item label="卖家">{{ currentOrder.sellerName }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">
            {{ currentOrder.paymentMethod ? getPaymentLabel(currentOrder.paymentMethod) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="下单时间">{{ formatTime(currentOrder.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="收货地址" :span="2">
            {{ currentOrder.deliveryAddress || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注信息" :span="2">
            {{ currentOrder.remark || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 状态编辑对话框 -->
    <el-dialog
      v-model="editStatusDialogVisible"
      title="修改订单状态"
      width="500px"
    >
      <el-form :model="editStatusForm" label-width="100px">
        <el-form-item label="新状态" required>
          <el-select v-model="editStatusForm.status" placeholder="请选择状态" style="width: 100%">
            <el-option
              v-for="(label, value) in ORDER_STATUS_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="editStatusForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="editStatusDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmStatusUpdate">确认更新</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  Download,
  Picture
} from '@element-plus/icons-vue'
import { formatTime } from '@/utils'
import {
  getOrdersListApi,
  getOrderDetailApi,
  updateOrderStatusApi,
  deleteOrderApi,
  getOrderStatisticsApi,
  batchOrderOperationApi,
  type OrderInfo,
  type OrderListParams,
  type OrderStatistics,
  type OrderStatusUpdateRequest,
  type BatchOrderOperationRequest
} from '@/request/orderManagementApi'

// 订单状态标签
const ORDER_STATUS_LABELS = {
  pending_payment: '待付款',
  pending_pickup: '待取货',
  pending_confirm: '待确认',
  completed: '已完成',
  cancelled: '已取消'
}

// 支付方式标签
const PAYMENT_METHOD_LABELS = {
  wechat: '微信支付',
  alipay: '支付宝',
  cash: '现金交易'
} as const

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  orderNo: '',
  buyerName: '',
  sellerName: '',
  productTitle: '',
  status: '',
  dateRange: null as any
})

// 订单列表数据
const orders = ref<OrderInfo[]>([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 订单详情
const detailDialogVisible = ref(false)
const currentOrder = ref<OrderInfo | null>(null)

// 统计信息
const statistics = reactive<OrderStatistics>({
  totalOrders: 0,
  pendingPayment: 0,
  pendingPickup: 0,
  pendingConfirm: 0,
  completed: 0,
  cancelled: 0,
  todayOrders: 0,
  totalAmount: 0,
  statusStats: {},
  dailyStats: []
})

// 获取订单列表
const fetchOrders = async () => {
  loading.value = true
  try {
    const params: OrderListParams = {
      orderNo: searchForm.orderNo || undefined,
      buyerUsername: searchForm.buyerName || undefined,
      sellerUsername: searchForm.sellerName || undefined,
      productTitle: searchForm.productTitle || undefined,
      status: searchForm.status || undefined,
      startDate: searchForm.dateRange?.[0] || undefined,
      endDate: searchForm.dateRange?.[1] || undefined,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      sortBy: 'create_time',
      sortOrder: 'desc'
    }

    const response = await getOrdersListApi(params)

    if (response.code === 200) {
      orders.value = response.data.records
      total.value = response.data.total
      currentPage.value = response.data.pageNum
    } else {
      ElMessage.error(response.msg || '获取订单列表失败')
    }
  } catch (error) {
    ElMessage.error('获取订单列表失败')
    console.error('获取订单列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStatistics = async () => {
  try {
    const response = await getOrderStatisticsApi()

    if (response.code === 200) {
      Object.assign(statistics, response.data)
    } else {
      console.error('获取统计信息失败:', response.msg)
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

// 标签样式函数
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending_payment: 'warning',
    pending_pickup: 'info',
    pending_confirm: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return typeMap[status] || ''
}

const getStatusLabel = (status: string) => {
  return ORDER_STATUS_LABELS[status as keyof typeof ORDER_STATUS_LABELS] || status
}

const getPaymentLabel = (method: string) => {
  return PAYMENT_METHOD_LABELS[method as keyof typeof PAYMENT_METHOD_LABELS] || method
}

// 事件处理
const goBack = () => {
  router.push({ name: 'AdminDashboard' })
}

const refreshData = () => {
  fetchOrders()
  fetchStatistics()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchOrders()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    orderNo: '',
    buyerName: '',
    sellerName: '',
    productTitle: '',
    status: '',
    dateRange: null
  })
  handleSearch()
}

const handleView = async (order: OrderInfo) => {
  try {
    loading.value = true
    const response = await getOrderDetailApi(order.orderId)

    if (response.code === 200) {
      currentOrder.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取订单详情失败')
    }
  } catch (error) {
    ElMessage.error('获取订单详情失败')
    console.error('获取订单详情失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = async (order: OrderInfo) => {
  try {
    await ElMessageBox.confirm(`确定要取消订单 ${order.orderNo} 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await updateOrderStatusApi(order.orderId, {
      status: 'cancelled',
      remark: '管理员取消订单'
    })

    if (response.code === 200) {
      ElMessage.success('订单取消成功')
      fetchOrders()
      fetchStatistics()
    } else {
      ElMessage.error(response.msg || '订单取消失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('订单取消失败')
      console.error('订单取消失败:', error)
    }
  }
}

const handleDelete = async (order: OrderInfo) => {
  try {
    await ElMessageBox.confirm(`确定要删除订单 ${order.orderNo} 吗？此操作不可恢复。`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await deleteOrderApi(order.orderId)

    if (response.code === 200) {
      ElMessage.success('订单删除成功')
      fetchOrders()
      fetchStatistics()
    } else {
      ElMessage.error(response.msg || '订单删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('订单删除失败')
      console.error('订单删除失败:', error)
    }
  }
}

// 状态编辑相关
const editStatusDialogVisible = ref(false)
const editStatusForm = reactive({
  orderId: 0,
  status: '',
  remark: ''
})

const handleEditStatus = (order: OrderInfo) => {
  editStatusForm.orderId = order.orderId
  editStatusForm.status = order.status
  editStatusForm.remark = order.remark || ''
  editStatusDialogVisible.value = true
}

const handleConfirmStatusUpdate = async () => {
  try {
    const response = await updateOrderStatusApi(editStatusForm.orderId, {
      status: editStatusForm.status,
      remark: editStatusForm.remark
    })

    if (response.code === 200) {
      ElMessage.success('订单状态更新成功')
      editStatusDialogVisible.value = false
      fetchOrders()
      fetchStatistics()
    } else {
      ElMessage.error(response.msg || '订单状态更新失败')
    }
  } catch (error) {
    ElMessage.error('订单状态更新失败')
    console.error('订单状态更新失败:', error)
  }
}

const handleExport = () => {
  try {
    // 准备导出数据
    const exportData = orders.value.map(order => ({
      '订单ID': order.orderId,
      '商品标题': order.productTitle,
      '买家用户名': order.buyerUsername,
      '卖家用户名': order.sellerUsername,
      '订单金额': `¥${order.totalAmount}`,
      '支付方式': order.paymentMethod,
      '交易地点': order.pickupLocation,
      '状态': getStatusLabel(order.status),
      '创建时间': formatTime(order.createTime),
      '支付时间': order.payTime ? formatTime(order.payTime) : '',
      '完成时间': order.completeTime ? formatTime(order.completeTime) : ''
    }))

    // 转换为CSV格式
    const headers = Object.keys(exportData[0])
    const csvContent = [
      headers.join(','),
      ...exportData.map(row =>
        headers.map(header => `"${row[header] || ''}"`).join(',')
      )
    ].join('\n')

    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `订单数据_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('订单数据导出成功')
  } catch (error) {
    ElMessage.error('导出失败，请重试')
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchOrders()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchOrders()
}

onMounted(() => {
  fetchOrders()
  fetchStatistics()
})
</script>

<style lang="stylus" scoped>
.order-management
  width 100%
  padding 20px

.page-header
  display flex
  justify-content space-between
  align-items center
  margin-bottom 20px
  
  h2
    margin 0
    color #333
    
  .header-actions
    display flex
    gap 10px

.stats-section
  margin-bottom 20px

  .stat-card
    text-align center

    .stat-content
      .stat-number
        font-size 24px
        font-weight bold
        color #409eff
        margin-bottom 5px

      .stat-label
        font-size 14px
        color #666

.search-card
  margin-bottom 20px

.table-card
  .card-header
    display flex
    justify-content space-between
    align-items center

.pagination-wrapper
  display flex
  justify-content center
  margin-top 20px

.product-info
  display flex
  align-items center
  
  .product-details
    .product-title
      font-weight bold
      margin-bottom 4px

    .product-category
      font-size 12px
      color #999
      margin-bottom 4px

    .product-price
      color #f56c6c
      font-weight bold

.user-name
  font-weight bold
  margin-bottom 2px

.user-nickname
  font-size 12px
  color #666

.no-data
  color #999
  font-style italic
      margin-bottom 4px
      
    .product-price
      color #f56c6c
      font-weight bold

.image-slot
  display flex
  justify-content center
  align-items center
  width 100%
  height 100%
  background #f5f7fa
  color #909399
  font-size 20px

.order-detail
  .el-descriptions
    margin-top 20px

@media (max-width: 768px)
  .order-management
    padding 10px
    
  .page-header
    flex-direction column
    gap 15px
    align-items stretch
    
  .header-actions
    justify-content center
</style>
