from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional

from app.routers.auth import get_current_user
from app.schemas.common import ApiResponse, PageResponse
from app.schemas.order_management import (
    OrderListParams, OrderInfo, OrderStatusUpdateRequest, 
    OrderStatistics, BatchOrderOperationRequest, BatchOperationResult
)
from app.services.order_management_service import order_management_service

router = APIRouter(prefix="/orders", tags=["订单管理"])

@router.get("/list", response_model=ApiResponse[PageResponse[OrderInfo]])
async def get_orders_list(
    order_no: Optional[str] = Query(None, description="订单号"),
    buyer_username: Optional[str] = Query(None, description="买家用户名"),
    seller_username: Optional[str] = Query(None, description="卖家用户名"),
    product_title: Optional[str] = Query(None, description="商品标题"),
    status: Optional[str] = Query(None, description="订单状态"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    min_price: Optional[float] = Query(None, description="最低价格"),
    max_price: Optional[float] = Query(None, description="最高价格"),
    sortBy: str = Query("create_time", description="排序字段"),
    sortOrder: str = Query("desc", description="排序方向"),
    pageNum: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: str = Depends(get_current_user)
):
    """获取订单列表"""
    try:
        params = OrderListParams(
            order_no=order_no,
            buyer_username=buyer_username,
            seller_username=seller_username,
            product_title=product_title,
            status=status,
            start_date=start_date,
            end_date=end_date,
            min_price=min_price,
            max_price=max_price,
            sortBy=sortBy,
            sortOrder=sortOrder,
            pageNum=pageNum,
            pageSize=pageSize
        )
        
        result = await order_management_service.get_orders_list(params)
        
        return ApiResponse(
            code=200,
            msg="获取订单列表成功",
            data=PageResponse(
                records=result["records"],
                total=result["total"],
                pageNum=result["pageNum"],
                pageSize=result["pageSize"],
                totalPages=result["totalPages"]
            )
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"获取订单列表失败: {str(e)}",
            data=None
        )

@router.get("/{order_id}", response_model=ApiResponse[OrderInfo])
async def get_order_detail(
    order_id: int,
    current_user: str = Depends(get_current_user)
):
    """获取订单详情"""
    try:
        order_info = await order_management_service.get_order_detail(order_id)
        return ApiResponse(
            code=200,
            msg="获取订单详情成功",
            data=order_info
        )
    except ValueError as e:
        return ApiResponse(
            code=404,
            msg=str(e),
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"获取订单详情失败: {str(e)}",
            data=None
        )

@router.put("/{order_id}/status", response_model=ApiResponse[OrderInfo])
async def update_order_status(
    order_id: int,
    request: OrderStatusUpdateRequest,
    current_user: str = Depends(get_current_user)
):
    """更新订单状态"""
    try:
        order_info = await order_management_service.update_order_status(
            order_id, request.status, request.remark
        )
        return ApiResponse(
            code=200,
            msg="订单状态更新成功",
            data=order_info
        )
    except ValueError as e:
        return ApiResponse(
            code=404,
            msg=str(e),
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"更新订单状态失败: {str(e)}",
            data=None
        )

@router.delete("/{order_id}", response_model=ApiResponse[bool])
async def delete_order(
    order_id: int,
    current_user: str = Depends(get_current_user)
):
    """删除订单"""
    try:
        success = await order_management_service.delete_order(order_id)
        return ApiResponse(
            code=200,
            msg="订单删除成功",
            data=success
        )
    except ValueError as e:
        return ApiResponse(
            code=404,
            msg=str(e),
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"删除订单失败: {str(e)}",
            data=None
        )

@router.get("/statistics/overview", response_model=ApiResponse[OrderStatistics])
async def get_order_statistics(
    current_user: str = Depends(get_current_user)
):
    """获取订单统计信息"""
    try:
        stats = await order_management_service.get_order_statistics()
        return ApiResponse(
            code=200,
            msg="获取统计信息成功",
            data=stats
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"获取统计信息失败: {str(e)}",
            data=None
        )

@router.post("/batch", response_model=ApiResponse[BatchOperationResult])
async def batch_order_operation(
    request: BatchOrderOperationRequest,
    current_user: str = Depends(get_current_user)
):
    """批量订单操作"""
    try:
        result = await order_management_service.batch_order_operation(request)
        return ApiResponse(
            code=200,
            msg=f"批量操作完成，成功{result.successCount}个，失败{result.failedCount}个",
            data=result
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            msg=f"批量操作失败: {str(e)}",
            data=None
        )
