<template>
  <el-dialog
    v-model="visible"
    title="评价订单"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="review-dialog">
      <!-- 订单信息 -->
      <div class="order-info">
        <el-image 
          :src="orderInfo.productImage" 
          fit="cover"
          class="product-image"
        />
        <div class="product-details">
          <h4>{{ orderInfo.productTitle }}</h4>
          <p class="order-amount">订单金额：¥{{ orderInfo.totalAmount }}</p>
        </div>
      </div>

      <!-- 评分 -->
      <div class="rating-section">
        <h5>整体评价：</h5>
        <el-rate
          v-model="reviewForm.rating"
          :colors="['#F7BA2A', '#F7BA2A', '#F7BA2A']"
          show-text
          :texts="['很差', '较差', '一般', '满意', '非常满意']"
        />
      </div>

      <!-- 评价标签 -->
      <div class="tags-section">
        <h5>选择标签：</h5>
        <div class="tag-groups">
          <div class="tag-group">
            <span class="tag-label">商品质量：</span>
            <el-checkbox-group v-model="reviewForm.qualityTags">
              <el-checkbox-button 
                v-for="tag in qualityTags" 
                :key="tag" 
                :value="tag"
              >
                {{ tag }}
              </el-checkbox-button>
            </el-checkbox-group>
          </div>
          
          <div class="tag-group">
            <span class="tag-label">卖家服务：</span>
            <el-checkbox-group v-model="reviewForm.serviceTags">
              <el-checkbox-button 
                v-for="tag in serviceTags" 
                :key="tag" 
                :value="tag"
              >
                {{ tag }}
              </el-checkbox-button>
            </el-checkbox-group>
          </div>
        </div>
      </div>

      <!-- 评价内容 -->
      <div class="content-section">
        <h5>评价内容：</h5>
        <el-input
          v-model="reviewForm.content"
          type="textarea"
          :rows="4"
          placeholder="请详细描述您的购买体验..."
          maxlength="200"
          show-word-limit
        />
      </div>

      <!-- 上传图片 -->
      <div class="images-section">
        <h5>上传图片：</h5>
        <el-upload
          v-model:file-list="reviewForm.images"
          action="#"
          list-type="picture-card"
          :auto-upload="false"
          :limit="3"
          accept="image/*"
        >
          <el-icon><Plus /></el-icon>
          <template #tip>
            <div class="el-upload__tip">
              最多上传3张图片，支持jpg/png格式
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 匿名评价 -->
      <div class="anonymous-section">
        <el-checkbox v-model="reviewForm.anonymous">
          匿名评价
        </el-checkbox>
        <span class="anonymous-tip">（匿名评价不会显示您的用户名）</span>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="submitReview"
          :loading="submitting"
          :disabled="!reviewForm.rating"
        >
          提交评价
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

interface OrderInfo {
  orderId: string
  productTitle: string
  productImage: string
  totalAmount: number
}

interface Props {
  modelValue: boolean
  orderInfo: OrderInfo
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'review-submitted'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(props.modelValue)
const submitting = ref(false)

// 评价表单
const reviewForm = reactive({
  rating: 0,
  qualityTags: [] as string[],
  serviceTags: [] as string[],
  content: '',
  images: [] as any[],
  anonymous: false
})

// 评价标签
const qualityTags = [
  '质量很好', '描述相符', '性价比高', '包装完好', '成色不错'
]

const serviceTags = [
  '态度很好', '回复及时', '交易顺利', '守时守信', '推荐购买'
]

// 提交评价
const submitReview = async () => {
  if (!reviewForm.rating) {
    ElMessage.warning('请选择评分')
    return
  }

  submitting.value = true
  try {
    // 模拟提交评价
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    ElMessage.success('评价提交成功')
    emit('review-submitted')
    handleClose()
  } catch (error) {
    ElMessage.error('评价提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  emit('update:modelValue', false)
  // 重置表单
  Object.assign(reviewForm, {
    rating: 0,
    qualityTags: [],
    serviceTags: [],
    content: '',
    images: [],
    anonymous: false
  })
}

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})
</script>

<style lang="stylus" scoped>
.review-dialog
  .order-info
    display flex
    align-items center
    gap 15px
    padding 15px
    background #f8f9fa
    border-radius 8px
    margin-bottom 20px
    
    .product-image
      width 60px
      height 60px
      border-radius 6px
      
    .product-details
      flex 1
      
      h4
        margin 0 0 8px 0
        color #333
        font-size 16px
        
      .order-amount
        margin 0
        color #e74c3c
        font-weight 500

  .rating-section, .tags-section, .content-section, .images-section
    margin-bottom 20px
    
    h5
      margin 0 0 10px 0
      color #333
      font-size 14px
      font-weight 500

  .tags-section
    .tag-groups
      .tag-group
        margin-bottom 15px
        
        .tag-label
          display inline-block
          width 80px
          font-size 13px
          color #666
          margin-bottom 8px
          
        .el-checkbox-group
          display flex
          flex-wrap wrap
          gap 8px

  .images-section
    :deep(.el-upload-list)
      display flex
      flex-wrap wrap
      gap 8px
      
    :deep(.el-upload--picture-card)
      width 80px
      height 80px
      
    :deep(.el-upload-list__item)
      width 80px
      height 80px

  .anonymous-section
    display flex
    align-items center
    gap 8px
    
    .anonymous-tip
      font-size 12px
      color #999

.dialog-footer
  display flex
  justify-content flex-end
  gap 10px
</style>
